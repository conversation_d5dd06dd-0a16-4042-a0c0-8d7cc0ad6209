module.exports = [
"[project]/.next-internal/server/app/api/characters/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[project]/src/lib/database.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "chapterDb",
    ()=>chapterDb,
    "characterDb",
    ()=>characterDb,
    "jobDb",
    ()=>jobDb,
    "novelDb",
    ()=>novelDb,
    "presetDb",
    ()=>presetDb,
    "ruleDb",
    ()=>ruleDb
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
// 数据存储路径
const DATA_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const NOVELS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'novels.json');
const CHAPTERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'chapters.json');
const RULES_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'rewrite_rules.json');
const JOBS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'rewrite_jobs.json');
const CHARACTERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'characters.json');
const PRESETS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'presets.json');
// 确保数据目录存在
function ensureDataDir() {
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DATA_DIR)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DATA_DIR, {
            recursive: true
        });
    }
}
// 读取JSON文件
function readJsonFile(filePath) {
    ensureDataDir();
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath)) {
        return [];
    }
    try {
        const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`Error reading ${filePath}:`, error);
        return [];
    }
}
// 写入JSON文件
function writeJsonFile(filePath, data) {
    ensureDataDir();
    try {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
    } catch (error) {
        console.error(`Error writing ${filePath}:`, error);
        throw error;
    }
}
// 生成唯一ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
const novelDb = {
    getAll: ()=>readJsonFile(NOVELS_FILE),
    getById: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        return novels.find((novel)=>novel.id === id);
    },
    create: (novel)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const newNovel = {
            ...novel,
            id: generateId(),
            createdAt: new Date().toISOString()
        };
        novels.push(newNovel);
        writeJsonFile(NOVELS_FILE, novels);
        return newNovel;
    },
    update: (id, updates)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return null;
        novels[index] = {
            ...novels[index],
            ...updates
        };
        writeJsonFile(NOVELS_FILE, novels);
        return novels[index];
    },
    delete: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return false;
        novels.splice(index, 1);
        writeJsonFile(NOVELS_FILE, novels);
        return true;
    }
};
const chapterDb = {
    getAll: ()=>readJsonFile(CHAPTERS_FILE),
    getByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.filter((chapter)=>chapter.novelId === novelId);
    },
    getById: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.find((chapter)=>chapter.id === id);
    },
    create: (chapter)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const newChapter = {
            ...chapter,
            id: generateId(),
            createdAt: new Date().toISOString()
        };
        chapters.push(newChapter);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return newChapter;
    },
    createBatch: (chapters)=>{
        const existingChapters = readJsonFile(CHAPTERS_FILE);
        const newChapters = chapters.map((chapter)=>({
                ...chapter,
                id: generateId(),
                createdAt: new Date().toISOString()
            }));
        existingChapters.push(...newChapters);
        writeJsonFile(CHAPTERS_FILE, existingChapters);
        return newChapters;
    },
    delete: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const index = chapters.findIndex((chapter)=>chapter.id === id);
        if (index === -1) return false;
        chapters.splice(index, 1);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const filteredChapters = chapters.filter((chapter)=>chapter.novelId !== novelId);
        writeJsonFile(CHAPTERS_FILE, filteredChapters);
        return true;
    }
};
const ruleDb = {
    getAll: ()=>readJsonFile(RULES_FILE),
    getById: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        return rules.find((rule)=>rule.id === id);
    },
    create: (rule)=>{
        const rules = readJsonFile(RULES_FILE);
        const newRule = {
            ...rule,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        rules.push(newRule);
        writeJsonFile(RULES_FILE, rules);
        return newRule;
    },
    update: (id, updates)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return null;
        rules[index] = {
            ...rules[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(RULES_FILE, rules);
        return rules[index];
    },
    delete: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return false;
        rules.splice(index, 1);
        writeJsonFile(RULES_FILE, rules);
        return true;
    }
};
const jobDb = {
    getAll: ()=>readJsonFile(JOBS_FILE),
    getById: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        return jobs.find((job)=>job.id === id);
    },
    create: (job)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const newJob = {
            ...job,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        jobs.push(newJob);
        writeJsonFile(JOBS_FILE, jobs);
        return newJob;
    },
    update: (id, updates)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return null;
        jobs[index] = {
            ...jobs[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(JOBS_FILE, jobs);
        return jobs[index];
    },
    delete: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return false;
        jobs.splice(index, 1);
        writeJsonFile(JOBS_FILE, jobs);
        return true;
    }
};
const characterDb = {
    getAll: ()=>readJsonFile(CHARACTERS_FILE),
    getByNovelId: (novelId)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        return characters.filter((character)=>character.novelId === novelId);
    },
    getById: (id)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        return characters.find((character)=>character.id === id);
    },
    create: (character)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const newCharacter = {
            ...character,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        characters.push(newCharacter);
        writeJsonFile(CHARACTERS_FILE, characters);
        return newCharacter;
    },
    update: (id, updates)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const index = characters.findIndex((character)=>character.id === id);
        if (index === -1) return null;
        characters[index] = {
            ...characters[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(CHARACTERS_FILE, characters);
        return characters[index];
    },
    delete: (id)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const index = characters.findIndex((character)=>character.id === id);
        if (index === -1) return false;
        characters.splice(index, 1);
        writeJsonFile(CHARACTERS_FILE, characters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const filteredCharacters = characters.filter((character)=>character.novelId !== novelId);
        writeJsonFile(CHARACTERS_FILE, filteredCharacters);
        return true;
    }
};
const presetDb = {
    getAll: ()=>readJsonFile(PRESETS_FILE),
    getById: (id)=>{
        const presets = readJsonFile(PRESETS_FILE);
        return presets.find((preset)=>preset.id === id);
    },
    create: (preset)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const newPreset = {
            ...preset,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        presets.push(newPreset);
        writeJsonFile(PRESETS_FILE, presets);
        return newPreset;
    },
    update: (id, updates)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const index = presets.findIndex((preset)=>preset.id === id);
        if (index === -1) return null;
        presets[index] = {
            ...presets[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(PRESETS_FILE, presets);
        return presets[index];
    },
    delete: (id)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const index = presets.findIndex((preset)=>preset.id === id);
        if (index === -1) return false;
        presets.splice(index, 1);
        writeJsonFile(PRESETS_FILE, presets);
        return true;
    }
};
}),
"[project]/src/app/api/characters/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "DELETE",
    ()=>DELETE,
    "GET",
    ()=>GET,
    "POST",
    ()=>POST,
    "PUT",
    ()=>PUT
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const novelId = searchParams.get('novelId');
        if (!novelId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '小说ID不能为空'
            }, {
                status: 400
            });
        }
        const characters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["characterDb"].getByNovelId(novelId);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: characters
        });
    } catch (error) {
        console.error('获取人物设定列表失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '获取人物设定列表失败'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { novelId, name, role, description, personality, appearance, relationships } = body;
        if (!novelId || !name || !role) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '小说ID、人物名称和角色类型不能为空'
            }, {
                status: 400
            });
        }
        const character = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["characterDb"].create({
            novelId,
            name,
            role,
            description: description || '',
            personality,
            appearance,
            relationships
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: character,
            message: '人物设定创建成功'
        });
    } catch (error) {
        console.error('创建人物设定失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '创建人物设定失败'
        }, {
            status: 500
        });
    }
}
async function PUT(request) {
    try {
        const body = await request.json();
        const { id, name, role, description, personality, appearance, relationships } = body;
        if (!id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '人物ID不能为空'
            }, {
                status: 400
            });
        }
        const character = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["characterDb"].update(id, {
            name,
            role,
            description,
            personality,
            appearance,
            relationships
        });
        if (!character) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '人物设定不存在'
            }, {
                status: 404
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: character,
            message: '人物设定更新成功'
        });
    } catch (error) {
        console.error('更新人物设定失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '更新人物设定失败'
        }, {
            status: 500
        });
    }
}
async function DELETE(request) {
    try {
        const { searchParams } = new URL(request.url);
        const id = searchParams.get('id');
        if (!id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '人物ID不能为空'
            }, {
                status: 400
            });
        }
        const success = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["characterDb"].delete(id);
        if (!success) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '人物设定不存在'
            }, {
                status: 404
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: '人物设定删除成功'
        });
    } catch (error) {
        console.error('删除人物设定失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '删除人物设定失败'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__6119748e._.js.map