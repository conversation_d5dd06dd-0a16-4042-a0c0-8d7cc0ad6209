{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/head-manager-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\n\nexport const HeadManagerContext: React.Context<{\n  updateHead?: (state: any) => void\n  mountedInstances?: any\n  updateScripts?: (state: any) => void\n  scripts?: any\n  getIsSsr?: () => boolean\n\n  // Used in app directory, to render script tags as server components.\n  appDir?: boolean\n  nonce?: string\n}> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  HeadManagerContext.displayName = 'HeadManagerContext'\n}\n"], "names": ["HeadManagerContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAcIG,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAZhBL,sBAAAA;;;eAAAA;;;;gEAFK;AAEX,MAAMA,qBAURC,OAAAA,OAAK,CAACC,aAAa,CAAC,CAAC;AAE1B,wCAA2C;IACzCF,mBAAmBM,WAAW,GAAG;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/mitt.ts"], "sourcesContent": ["/*\nMIT License\n\nCopyright (c) <PERSON> (https://jasonformat.com/)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\n// This file is based on https://github.com/developit/mitt/blob/v1.1.3/src/index.js\n// It's been edited for the needs of this script\n// See the LICENSE at the top of the file\n\ntype Handler = (...evts: any[]) => void\n\nexport type MittEmitter<T> = {\n  on(type: T, handler: Handler): void\n  off(type: T, handler: Handler): void\n  emit(type: T, ...evts: any[]): void\n}\n\nexport default function mitt(): MittEmitter<string> {\n  const all: { [s: string]: Handler[] } = Object.create(null)\n\n  return {\n    on(type: string, handler: Handler) {\n      ;(all[type] || (all[type] = [])).push(handler)\n    },\n\n    off(type: string, handler: Handler) {\n      if (all[type]) {\n        all[type].splice(all[type].indexOf(handler) >>> 0, 1)\n      }\n    },\n\n    emit(type: string, ...evts: any[]) {\n      // eslint-disable-next-line array-callback-return\n      ;(all[type] || []).slice().map((handler: Handler) => {\n        handler(...evts)\n      })\n    },\n  }\n}\n"], "names": ["mitt", "all", "Object", "create", "on", "type", "handler", "push", "off", "splice", "indexOf", "emit", "evts", "slice", "map"], "mappings": "AAAA;;;;;;;;;;AAUA,GAEA,mFAAmF;AACnF,gDAAgD;AAChD,yCAAyC;;;;+BAUzC,WAAA;;;eAAwBA;;;AAAT,SAASA;IACtB,MAAMC,MAAkCC,OAAOC,MAAM,CAAC;IAEtD,OAAO;QACLC,IAAGC,IAAY,EAAEC,OAAgB;;YAC7BL,CAAAA,GAAG,CAACI,KAAK,IAAKJ,CAAAA,GAAG,CAACI,KAAK,GAAG,EAAC,CAAC,EAAGE,IAAI,CAACD;QACxC;QAEAE,KAAIH,IAAY,EAAEC,OAAgB;YAChC,IAAIL,GAAG,CAACI,KAAK,EAAE;gBACbJ,GAAG,CAACI,KAAK,CAACI,MAAM,CAACR,GAAG,CAACI,KAAK,CAACK,OAAO,CAACJ,aAAa,GAAG;YACrD;QACF;QAEAK,MAAKN,IAAY;YAAE,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGO,OAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;gBAAGA,IAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAc;;YAC/B,iDAAiD;;YAC/CX,CAAAA,GAAG,CAACI,KAAK,IAAI,EAAC,EAAGQ,KAAK,GAAGC,GAAG,CAAC,CAACR;gBAC9BA,WAAWM;YACb;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\nimport type { NextRouter } from './router/router'\n\nexport const RouterContext = React.createContext<NextRouter | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  RouterContext.displayName = 'RouterContext'\n}\n"], "names": ["RouterContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAKIG,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAFhBL,iBAAAA;;;eAAAA;;;;gEAHK;AAGX,MAAMA,gBAAgBC,OAAAA,OAAK,CAACC,aAAa,CAAoB;AAEpE,wCAA2C;IACzCF,cAAcM,WAAW,GAAG;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/utils/warn-once.ts"], "sourcesContent": ["let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n"], "names": ["warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,YAAAA;;;eAAAA;;;AAXT,IAAIA,WAAW,CAACC,KAAe;AAC/B,wCAA2C;IACzC,MAAMI,WAAW,IAAIC;IACrBN,WAAW,CAACO;QACV,IAAI,CAACF,SAASG,GAAG,CAACD,MAAM;YACtBE,QAAQC,IAAI,CAACH;QACf;QACAF,SAASM,GAAG,CAACJ;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/disable-smooth-scroll.ts"], "sourcesContent": ["import { warnOnce } from '../../utils/warn-once'\n\n/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function disableSmoothScrollDuringRouteTransition(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n\n  const htmlElement = document.documentElement\n  const hasDataAttribute = htmlElement.dataset.scrollBehavior === 'smooth'\n\n  // Since this is a breaking change, this is temporarily flagged\n  // and will be false by default.\n  // In the next major (v16), this will be automatically enabled\n  if (process.env.__NEXT_OPTIMIZE_ROUTER_SCROLL) {\n    if (!hasDataAttribute) {\n      // No smooth scrolling configured, run directly without style manipulation\n      fn()\n      return\n    }\n  } else {\n    // Old behavior: always manipulate styles, but warn about upcoming change\n\n    // Warn if smooth scrolling is detected but no data attribute is present\n    if (\n      process.env.NODE_ENV === 'development' &&\n      !hasDataAttribute &&\n      getComputedStyle(htmlElement).scrollBehavior === 'smooth'\n    ) {\n      warnOnce(\n        'Detected `scroll-behavior: smooth` on the `<html>` element. In a future version, ' +\n          'Next.js will no longer automatically disable smooth scrolling during route transitions. ' +\n          'To prepare for this change, add `data-scroll-behavior=\"smooth\"` to your <html> element. ' +\n          'Learn more: https://nextjs.org/docs/messages/missing-data-scroll-behavior'\n      )\n    }\n  }\n\n  // Proceed with temporarily disabling smooth scrolling\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n"], "names": ["disableSmoothScrollDuringRouteTransition", "fn", "options", "onlyHashChange", "htmlElement", "document", "documentElement", "hasDataAttribute", "dataset", "scroll<PERSON>eh<PERSON>or", "process", "env", "__NEXT_OPTIMIZE_ROUTER_SCROLL", "NODE_ENV", "getComputedStyle", "warnOnce", "existing", "style", "dontForceLayout", "getClientRects"], "mappings": "AAuBMU,QAAQC,GAAG,CAACC,6BAA6B,EAAE;;;;;+BAjBjCZ,4CAAAA;;;eAAAA;;;0BANS;AAMlB,SAASA,yCACdC,EAAc,EACdC,OAAqE;IAArEA,IAAAA,YAAAA,KAAAA,GAAAA,UAAmE,CAAC;IAEpE,yEAAyE;IACzE,6FAA6F;IAC7F,IAAIA,QAAQC,cAAc,EAAE;QAC1BF;QACA;IACF;IAEA,MAAMG,cAAcC,SAASC,eAAe;IAC5C,MAAMC,mBAAmBH,YAAYI,OAAO,CAACC,cAAc,KAAK;IAEhE,+DAA+D;IAC/D,gCAAgC;IAChC,8DAA8D;IAC9D;;SAMO;QACL,yEAAyE;QAEzE,wEAAwE;QACxE,IACEC,QAAQC,GAAG,CAACE,QAAQ,gCAAK,iBACzB,CAACN,oBACDO,iBAAiBV,aAAaK,cAAc,KAAK,UACjD;YACAM,CAAAA,GAAAA,UAAAA,QAAQ,EACN,sFACE,6FACA,6FACA;QAEN;IACF;IAEA,sDAAsD;IACtD,MAAMC,WAAWZ,YAAYa,KAAK,CAACR,cAAc;IACjDL,YAAYa,KAAK,CAACR,cAAc,GAAG;IACnC,IAAI,CAACP,QAAQgB,eAAe,EAAE;QAC5B,8EAA8E;QAC9E,4DAA4D;QAC5D,yFAAyF;QACzFd,YAAYe,cAAc;IAC5B;IACAlB;IACAG,YAAYa,KAAK,CAACR,cAAc,GAAGO;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC;;;+BACeA,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;IA4BaA,mBAAmB,EAAA;eAAnBA;;IADAC,gBAAgB,EAAA;eAAhBA;;IAhBGC,4BAA4B,EAAA;eAA5BA;;IATAC,cAAc,EAAA;eAAdA;;IAKAC,sBAAsB,EAAA;eAAtBA;;;AALT,SAASD,eAAeE,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASF,uBAAuBC,OAAe;IACpD,OAAOA,QAAQE,UAAU,CAAC,QAAQF,YAAY;AAChD;AAEO,SAASH,6BACdG,OAAgB,EAChBG,YAA2D;IAE3D,MAAMC,gBAAgBJ,QAAQK,QAAQ,CAACT;IAEvC,IAAIQ,eAAe;QACjB,MAAME,mBAAmBC,KAAKC,SAAS,CAACL;QACxC,OAAOG,qBAAqB,OACxBV,mBAAmB,MAAMU,mBACzBV;IACN;IAEA,OAAOI;AACT;AAEO,MAAMJ,mBAAmB;AACzB,MAAMD,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["normalizeAppPath", "normalizeRscURL", "route", "ensureLeadingSlash", "split", "reduce", "pathname", "segment", "index", "segments", "isGroupSegment", "length", "url", "replace"], "mappings": ";;;;;;;;;;;;;;IAsBgBA,gBAAgB,EAAA;eAAhBA;;IAmCAC,eAAe,EAAA;eAAfA;;;oCAzDmB;yBACJ;AAqBxB,SAASD,iBAAiBE,KAAa;IAC5C,OAAOC,CAAAA,GAAAA,oBAAAA,kBAAkB,EACvBD,MAAME,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,IAAII,CAAAA,GAAAA,SAAAA,cAAc,EAACH,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASE,MAAM,GAAG,GAC5B;YACA,OAAOL;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASN,gBAAgBW,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "interceptingRoute", "marker", "interceptedRoute", "Error", "normalizeAppPath", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;;;;;;;;;;;IAGaA,0BAA0B,EAAA;eAA1BA;;IAkBGC,mCAAmC,EAAA;eAAnCA;;IAXAC,0BAA0B,EAAA;eAA1BA;;;0BAViB;AAG1B,MAAMF,6BAA6B;IACxC;IACA;IACA;IACA;CACD;AAEM,SAASE,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLN,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASR,oCAAoCE,IAAY;IAC9D,IAAIO,mBACFC,QACAC;IAEF,KAAK,MAAMN,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCO,SAASX,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAII,QAAQ;;YACT,CAACD,mBAAmBE,iBAAiB,GAAGT,KAAKC,KAAK,CAACO,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,iCAA8BV,OAAK,sFADhC,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAO,oBAAoBI,CAAAA,GAAAA,UAAAA,gBAAgB,EAACJ,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAoB,MAAGA;YACzB,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIG,MACP,iCAA8BV,OAAK,iEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAS,mBAAmBF,kBAChBN,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIL,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMM,yBAAyBR,kBAAkBN,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,OAAA,cAEL,CAFK,IAAIN,MACP,iCAA8BV,OAAK,oEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAS,mBAAmBM,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF;YACE,MAAM,OAAA,cAAyC,CAAzC,IAAIJ,MAAM,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;IAClD;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/is-dynamic.ts"], "sourcesContent": ["import {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\n\n// Identify /.*[param].*/ in route string\nconst TEST_ROUTE = /\\/[^/]*\\[[^/]+\\][^/]*(?=\\/|$)/\n\n// Identify /[param]/ in route string\nconst TEST_STRICT_ROUTE = /\\/\\[[^/]+\\](?=\\/|$)/\n\n/**\n * Check if a route is dynamic.\n *\n * @param route - The route to check.\n * @param strict - Whether to use strict mode which prohibits segments with prefixes/suffixes (default: true).\n * @returns Whether the route is dynamic.\n */\nexport function isDynamicRoute(route: string, strict: boolean = true): boolean {\n  if (isInterceptionRouteAppPath(route)) {\n    route = extractInterceptionRouteInformation(route).interceptedRoute\n  }\n\n  if (strict) {\n    return TEST_STRICT_ROUTE.test(route)\n  }\n\n  return TEST_ROUTE.test(route)\n}\n"], "names": ["isDynamicRoute", "TEST_ROUTE", "TEST_STRICT_ROUTE", "route", "strict", "isInterceptionRouteAppPath", "extractInterceptionRouteInformation", "interceptedRoute", "test"], "mappings": ";;;+BAkBgBA,kBAAAA;;;eAAAA;;;oCAfT;AAEP,yCAAyC;AACzC,MAAMC,aAAa;AAEnB,qCAAqC;AACrC,MAAMC,oBAAoB;AASnB,SAASF,eAAeG,KAAa,EAAEC,MAAsB;IAAtBA,IAAAA,WAAAA,KAAAA,GAAAA,SAAkB;IAC9D,IAAIC,CAAAA,GAAAA,oBAAAA,0BAA0B,EAACF,QAAQ;QACrCA,QAAQG,CAAAA,GAAAA,oBAAAA,mCAAmC,EAACH,OAAOI,gBAAgB;IACrE;IAEA,IAAIH,QAAQ;QACV,OAAOF,kBAAkBM,IAAI,CAACL;IAChC;IAEA,OAAOF,WAAWO,IAAI,CAACL;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/runtime-config.external.ts"], "sourcesContent": ["let runtimeConfig: any\n\nexport default () => {\n  return runtimeConfig\n}\n\nexport function setConfig(configValue: any): void {\n  runtimeConfig = configValue\n}\n"], "names": ["setConfig", "runtimeConfig", "config<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;IAEA,OAEC,EAAA;eAFD;;IAIgBA,SAAS,EAAA;eAATA;;;AANhB,IAAIC;MAEJ,WAAe;IACb,OAAOA;AACT;AAEO,SAASD,UAAUE,WAAgB;IACxCD,gBAAgBC;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": "AA8WM+C,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDlBjD,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,wCAA2C;YACrCD;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/parse-path.ts"], "sourcesContent": ["/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n"], "names": ["parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "substring", "query", "undefined", "hash", "slice"], "mappings": "AAAA;;;;CAIC;;;+BACeA,aAAAA;;;eAAAA;;;AAAT,SAASA,UAAUC,IAAY;IACpC,MAAMC,YAAYD,KAAKE,OAAO,CAAC;IAC/B,MAAMC,aAAaH,KAAKE,OAAO,CAAC;IAChC,MAAME,WAAWD,aAAa,CAAC,KAAMF,CAAAA,YAAY,KAAKE,aAAaF,SAAQ;IAE3E,IAAIG,YAAYH,YAAY,CAAC,GAAG;QAC9B,OAAO;YACLI,UAAUL,KAAKM,SAAS,CAAC,GAAGF,WAAWD,aAAaF;YACpDM,OAAOH,WACHJ,KAAKM,SAAS,CAACH,YAAYF,YAAY,CAAC,IAAIA,YAAYO,aACxD;YACJC,MAAMR,YAAY,CAAC,IAAID,KAAKU,KAAK,CAACT,aAAa;QACjD;IACF;IAEA,OAAO;QAAEI,UAAUL;QAAMO,OAAO;QAAIE,MAAM;IAAG;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/add-path-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n"], "names": ["addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;+BAMgBA,iBAAAA;;;eAAAA;;;2BANU;AAMnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACN;IAC5C,OAAQ,KAAEC,SAASE,WAAWC,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/remove-trailing-slash.ts"], "sourcesContent": ["/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n"], "names": ["removeTrailingSlash", "route", "replace"], "mappings": "AAAA;;;;;;CAMC;;;+BACeA,uBAAAA;;;eAAAA;;;AAAT,SAASA,oBAAoBC,KAAa;IAC/C,OAAOA,MAAMC,OAAO,CAAC,OAAO,OAAO;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/route-match-utils.ts"], "sourcesContent": ["/**\n * Client-safe utilities for route matching that don't import server-side\n * utilities to avoid bundling issues with Turbopack\n */\n\nimport type {\n  Key,\n  TokensToRegexpOptions,\n  ParseOptions,\n  TokensToFunctionOptions,\n} from 'next/dist/compiled/path-to-regexp'\nimport {\n  pathToRegexp,\n  compile,\n  regexpToFunction,\n} from 'next/dist/compiled/path-to-regexp'\nimport {\n  hasAdjacentParameterIssues,\n  normalizeAdjacentParameters,\n  stripParameterSeparators,\n} from '../../../../lib/route-pattern-normalizer'\n\n/**\n * Client-safe wrapper around pathToRegexp that handles path-to-regexp 6.3.0+ validation errors.\n * This includes both \"Can not repeat without prefix/suffix\" and \"Must have text between parameters\" errors.\n */\nexport function safePathToRegexp(\n  route: string | RegExp | Array<string | RegExp>,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions\n): RegExp {\n  if (typeof route !== 'string') {\n    return pathToRegexp(route, keys, options)\n  }\n\n  // Check if normalization is needed and cache the result\n  const needsNormalization = hasAdjacentParameterIssues(route)\n  const routeToUse = needsNormalization\n    ? normalizeAdjacentParameters(route)\n    : route\n\n  try {\n    return pathToRegexp(routeToUse, keys, options)\n  } catch (error) {\n    // Only try normalization if we haven't already normalized\n    if (!needsNormalization) {\n      try {\n        const normalizedRoute = normalizeAdjacentParameters(route)\n        return pathToRegexp(normalizedRoute, keys, options)\n      } catch (retryError) {\n        // If that doesn't work, fall back to original error\n        throw error\n      }\n    }\n    throw error\n  }\n}\n\n/**\n * Client-safe wrapper around compile that handles path-to-regexp 6.3.0+ validation errors.\n * No server-side error reporting to avoid bundling issues.\n */\nexport function safeCompile(\n  route: string,\n  options?: TokensToFunctionOptions & ParseOptions\n) {\n  // Check if normalization is needed and cache the result\n  const needsNormalization = hasAdjacentParameterIssues(route)\n  const routeToUse = needsNormalization\n    ? normalizeAdjacentParameters(route)\n    : route\n\n  try {\n    return compile(routeToUse, options)\n  } catch (error) {\n    // Only try normalization if we haven't already normalized\n    if (!needsNormalization) {\n      try {\n        const normalizedRoute = normalizeAdjacentParameters(route)\n        return compile(normalizedRoute, options)\n      } catch (retryError) {\n        // If that doesn't work, fall back to original error\n        throw error\n      }\n    }\n    throw error\n  }\n}\n\n/**\n * Client-safe wrapper around regexpToFunction that automatically cleans parameters.\n */\nexport function safeRegexpToFunction<\n  T extends Record<string, any> = Record<string, any>,\n>(regexp: RegExp, keys?: Key[]): (pathname: string) => { params: T } | false {\n  const originalMatcher = regexpToFunction<T>(regexp, keys || [])\n\n  return (pathname: string) => {\n    const result = originalMatcher(pathname)\n    if (!result) return false\n\n    // Clean parameters before returning\n    return {\n      ...result,\n      params: stripParameterSeparators(result.params as any) as T,\n    }\n  }\n}\n\n/**\n * Safe wrapper for route matcher functions that automatically cleans parameters.\n * This is client-safe and doesn't import path-to-regexp.\n */\nexport function safeRouteMatcher<T extends Record<string, any>>(\n  matcherFn: (pathname: string) => false | T\n): (pathname: string) => false | T {\n  return (pathname: string) => {\n    const result = matcherFn(pathname)\n    if (!result) return false\n\n    // Clean parameters before returning\n    return stripParameterSeparators(result) as T\n  }\n}\n"], "names": ["safeCompile", "safePathToRegexp", "safeRegexpToFunction", "safeRouteMatcher", "route", "keys", "options", "pathToRegexp", "needsNormalization", "hasAdjacentParameterIssues", "routeToUse", "normalizeAdjacentParameters", "error", "normalizedRoute", "retryError", "compile", "regexp", "originalMatcher", "regexpToFunction", "pathname", "result", "params", "stripParameterSeparators", "matcherFn"], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;IA2DeA,WAAW,EAAA;eAAXA;;IApCAC,gBAAgB,EAAA;eAAhBA;;IAkEAC,oBAAoB,EAAA;eAApBA;;IAqBAC,gBAAgB,EAAA;eAAhBA;;;8BAlGT;wCAKA;AAMA,SAASF,iBACdG,KAA+C,EAC/CC,IAAY,EACZC,OAA8C;IAE9C,IAAI,OAAOF,UAAU,UAAU;QAC7B,OAAOG,CAAAA,GAAAA,cAAAA,YAAY,EAACH,OAAOC,MAAMC;IACnC;IAEA,wDAAwD;IACxD,MAAME,qBAAqBC,CAAAA,GAAAA,wBAAAA,0BAA0B,EAACL;IACtD,MAAMM,aAAaF,qBACfG,CAAAA,GAAAA,wBAAAA,2BAA2B,EAACP,SAC5BA;IAEJ,IAAI;QACF,OAAOG,CAAAA,GAAAA,cAAAA,YAAY,EAACG,YAAYL,MAAMC;IACxC,EAAE,OAAOM,OAAO;QACd,0DAA0D;QAC1D,IAAI,CAACJ,oBAAoB;YACvB,IAAI;gBACF,MAAMK,kBAAkBF,CAAAA,GAAAA,wBAAAA,2BAA2B,EAACP;gBACpD,OAAOG,CAAAA,GAAAA,cAAAA,YAAY,EAACM,iBAAiBR,MAAMC;YAC7C,EAAE,OAAOQ,YAAY;gBACnB,oDAAoD;gBACpD,MAAMF;YACR;QACF;QACA,MAAMA;IACR;AACF;AAMO,SAASZ,YACdI,KAAa,EACbE,OAAgD;IAEhD,wDAAwD;IACxD,MAAME,qBAAqBC,CAAAA,GAAAA,wBAAAA,0BAA0B,EAACL;IACtD,MAAMM,aAAaF,qBACfG,CAAAA,GAAAA,wBAAAA,2BAA2B,EAACP,SAC5BA;IAEJ,IAAI;QACF,OAAOW,CAAAA,GAAAA,cAAAA,OAAO,EAACL,YAAYJ;IAC7B,EAAE,OAAOM,OAAO;QACd,0DAA0D;QAC1D,IAAI,CAACJ,oBAAoB;YACvB,IAAI;gBACF,MAAMK,kBAAkBF,CAAAA,GAAAA,wBAAAA,2BAA2B,EAACP;gBACpD,OAAOW,CAAAA,GAAAA,cAAAA,OAAO,EAACF,iBAAiBP;YAClC,EAAE,OAAOQ,YAAY;gBACnB,oDAAoD;gBACpD,MAAMF;YACR;QACF;QACA,MAAMA;IACR;AACF;AAKO,SAASV,qBAEdc,MAAc,EAAEX,IAAY;IAC5B,MAAMY,kBAAkBC,CAAAA,GAAAA,cAAAA,gBAAgB,EAAIF,QAAQX,QAAQ,EAAE;IAE9D,OAAO,CAACc;QACN,MAAMC,SAASH,gBAAgBE;QAC/B,IAAI,CAACC,QAAQ,OAAO;QAEpB,oCAAoC;QACpC,OAAO;YACL,GAAGA,MAAM;YACTC,QAAQC,CAAAA,GAAAA,wBAAAA,wBAAwB,EAACF,OAAOC,MAAM;QAChD;IACF;AACF;AAMO,SAASlB,iBACdoB,SAA0C;IAE1C,OAAO,CAACJ;QACN,MAAMC,SAASG,UAAUJ;QACzB,IAAI,CAACC,QAAQ,OAAO;QAEpB,oCAAoC;QACpC,OAAOE,CAAAA,GAAAA,wBAAAA,wBAAwB,EAACF;IAClC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/route-matcher.ts"], "sourcesContent": ["import type { Group } from './route-regex'\nimport { DecodeError } from '../../utils'\nimport type { Params } from '../../../../server/request/params'\nimport { safeRouteMatcher } from './route-match-utils'\n\nexport interface RouteMatchFn {\n  (pathname: string): false | Params\n}\n\ntype RouteMatcherOptions = {\n  // We only use the exec method of the RegExp object. This helps us avoid using\n  // type assertions that the passed in properties are of the correct type.\n  re: Pick<RegExp, 'exec'>\n  groups: Record<string, Group>\n}\n\nexport function getRouteMatcher({\n  re,\n  groups,\n}: RouteMatcherOptions): RouteMatchFn {\n  const rawMatcher = (pathname: string) => {\n    const routeMatch = re.exec(pathname)\n    if (!routeMatch) return false\n\n    const decode = (param: string) => {\n      try {\n        return decodeURIComponent(param)\n      } catch {\n        throw new DecodeError('failed to decode param')\n      }\n    }\n\n    const params: Params = {}\n    for (const [key, group] of Object.entries(groups)) {\n      const match = routeMatch[group.pos]\n      if (match !== undefined) {\n        if (group.repeat) {\n          params[key] = match.split('/').map((entry) => decode(entry))\n        } else {\n          params[key] = decode(match)\n        }\n      }\n    }\n\n    return params\n  }\n\n  // Wrap with safe matcher to handle parameter cleaning\n  return safeRouteMatcher(rawMatcher)\n}\n"], "names": ["getRouteMatcher", "re", "groups", "rawMatcher", "pathname", "routeMatch", "exec", "decode", "param", "decodeURIComponent", "DecodeError", "params", "key", "group", "Object", "entries", "match", "pos", "undefined", "repeat", "split", "map", "entry", "safeRouteMatcher"], "mappings": ";;;+BAgBgBA,mBAAAA;;;eAAAA;;;uBAfY;iCAEK;AAa1B,SAASA,gBAAgB,KAGV;IAHU,IAAA,EAC9BC,EAAE,EACFC,MAAM,EACc,GAHU;IAI9B,MAAMC,aAAa,CAACC;QAClB,MAAMC,aAAaJ,GAAGK,IAAI,CAACF;QAC3B,IAAI,CAACC,YAAY,OAAO;QAExB,MAAME,SAAS,CAACC;YACd,IAAI;gBACF,OAAOC,mBAAmBD;YAC5B,EAAE,OAAA,GAAM;gBACN,MAAM,OAAA,cAAyC,CAAzC,IAAIE,OAAAA,WAAW,CAAC,2BAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;QACF;QAEA,MAAMC,SAAiB,CAAC;QACxB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACb,QAAS;YACjD,MAAMc,QAAQX,UAAU,CAACQ,MAAMI,GAAG,CAAC;YACnC,IAAID,UAAUE,WAAW;gBACvB,IAAIL,MAAMM,MAAM,EAAE;oBAChBR,MAAM,CAACC,IAAI,GAAGI,MAAMI,KAAK,CAAC,KAAKC,GAAG,CAAC,CAACC,QAAUf,OAAOe;gBACvD,OAAO;oBACLX,MAAM,CAACC,IAAI,GAAGL,OAAOS;gBACvB;YACF;QACF;QAEA,OAAOL;IACT;IAEA,sDAAsD;IACtD,OAAOY,CAAAA,GAAAA,iBAAAA,gBAAgB,EAACpB;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/escape-regexp.ts"], "sourcesContent": ["// regexp is based on https://github.com/sindresorhus/escape-string-regexp\nconst reHasRegExp = /[|\\\\{}()[\\]^$+*?.-]/\nconst reReplaceRegExp = /[|\\\\{}()[\\]^$+*?.-]/g\n\nexport function escapeStringRegexp(str: string) {\n  // see also: https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/escapeRegExp.js#L23\n  if (reHasRegExp.test(str)) {\n    return str.replace(reReplaceRegExp, '\\\\$&')\n  }\n  return str\n}\n"], "names": ["escapeStringRegexp", "reHasRegExp", "reReplaceRegExp", "str", "test", "replace"], "mappings": "AAAA,0EAA0E;;;;+BAI1DA,sBAAAA;;;eAAAA;;;AAHhB,MAAMC,cAAc;AACpB,MAAMC,kBAAkB;AAEjB,SAASF,mBAAmBG,GAAW;IAC5C,+GAA+G;IAC/G,IAAIF,YAAYG,IAAI,CAACD,MAAM;QACzB,OAAOA,IAAIE,OAAO,CAACH,iBAAiB;IACtC;IACA,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/get-dynamic-param.ts"], "sourcesContent": ["import type { DynamicParam } from '../../../../server/app-render/app-render'\nimport type { DynamicParamTypesShort } from '../../../../server/app-render/types'\nimport type { FallbackRouteParams } from '../../../../server/request/fallback-params'\n\n/**\n *\n * Shared logic on client and server for creating a dynamic param value.\n *\n * This code needs to be shared with the client so it can extract dynamic route\n * params from the URL without a server request.\n *\n * Because everything in this module is sent to the client, we should aim to\n * keep this code as simple as possible. The special case handling for catchall\n * and optional is, alas, unfortunate.\n */\nexport function getDynamicParam(\n  params: { [key: string]: any },\n  segmentKey: string,\n  dynamicParamType: DynamicParamTypesShort,\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): DynamicParam {\n  let value = params[segmentKey]\n\n  if (fallbackRouteParams && fallbackRouteParams.has(segmentKey)) {\n    value = fallbackRouteParams.get(segmentKey)\n  } else if (Array.isArray(value)) {\n    value = value.map((i) => encodeURIComponent(i))\n  } else if (typeof value === 'string') {\n    value = encodeURIComponent(value)\n  }\n\n  if (!value) {\n    const isCatchall = dynamicParamType === 'c'\n    const isOptionalCatchall = dynamicParamType === 'oc'\n\n    if (isCatchall || isOptionalCatchall) {\n      // handle the case where an optional catchall does not have a value,\n      // e.g. `/dashboard/[[...slug]]` when requesting `/dashboard`\n      if (isOptionalCatchall) {\n        return {\n          param: segmentKey,\n          value: null,\n          type: dynamicParamType,\n          treeSegment: [segmentKey, '', dynamicParamType],\n        }\n      }\n\n      // handle the case where a catchall or optional catchall does not have a value,\n      // e.g. `/foo/bar/hello` and `@slot/[...catchall]` or `@slot/[[...catchall]]` is matched\n      value = pagePath\n        .split('/')\n        // remove the first empty string\n        .slice(1)\n        // replace any dynamic params with the actual values\n        .flatMap((pathSegment) => {\n          const param = parseParameter(pathSegment)\n          // if the segment matches a param, return the param value\n          // otherwise, it's a static segment, so just return that\n          return params[param.key] ?? param.key\n        })\n\n      return {\n        param: segmentKey,\n        value,\n        type: dynamicParamType,\n        // This value always has to be a string.\n        treeSegment: [segmentKey, value.join('/'), dynamicParamType],\n      }\n    }\n  }\n\n  return {\n    param: segmentKey,\n    // The value that is passed to user code.\n    value: value,\n    // The value that is rendered in the router tree.\n    treeSegment: [\n      segmentKey,\n      Array.isArray(value) ? value.join('/') : value,\n      dynamicParamType,\n    ],\n    type: dynamicParamType,\n  }\n}\n\n/**\n * Regular expression pattern used to match route parameters.\n * Matches both single parameters and parameter groups.\n * Examples:\n *   - `[[...slug]]` matches parameter group with key 'slug', repeat: true, optional: true\n *   - `[...slug]` matches parameter group with key 'slug', repeat: true, optional: false\n *   - `[[foo]]` matches parameter with key 'foo', repeat: false, optional: true\n *   - `[bar]` matches parameter with key 'bar', repeat: false, optional: false\n */\nexport const PARAMETER_PATTERN = /^([^[]*)\\[((?:\\[[^\\]]*\\])|[^\\]]+)\\](.*)$/\n\n/**\n * Parses a given parameter from a route to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[[...slug]]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[[foo]]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `[bar]` -> `{ key: 'bar', repeat: false, optional: false }`\n *   - `fizz` -> `{ key: 'fizz', repeat: false, optional: false }`\n * @param param - The parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nexport function parseParameter(param: string) {\n  const match = param.match(PARAMETER_PATTERN)\n\n  if (!match) {\n    return parseMatchedParameter(param)\n  }\n\n  return parseMatchedParameter(match[2])\n}\n\n/**\n * Parses a matched parameter from the PARAMETER_PATTERN regex to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `...slug` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[foo]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `bar` -> `{ key: 'bar', repeat: false, optional: false }`\n * @param param - The matched parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nexport function parseMatchedParameter(param: string) {\n  const optional = param.startsWith('[') && param.endsWith(']')\n  if (optional) {\n    param = param.slice(1, -1)\n  }\n  const repeat = param.startsWith('...')\n  if (repeat) {\n    param = param.slice(3)\n  }\n  return { key: param, repeat, optional }\n}\n"], "names": ["PARAMETER_PATTERN", "getDynamicParam", "parseMatchedParameter", "parseParameter", "params", "segmentKey", "dynamicParamType", "pagePath", "fallbackRouteParams", "value", "has", "get", "Array", "isArray", "map", "i", "encodeURIComponent", "isCatchall", "isOptionalCatchall", "param", "type", "treeSegment", "split", "slice", "flatMap", "pathSegment", "key", "join", "match", "optional", "startsWith", "endsWith", "repeat"], "mappings": ";;;;;;;;;;;;;;;;IA+FaA,iBAAiB,EAAA;eAAjBA;;IAhFGC,eAAe,EAAA;eAAfA;;IAmHAC,qBAAqB,EAAA;eAArBA;;IArBAC,cAAc,EAAA;eAAdA;;;AA9FT,SAASF,gBACdG,MAA8B,EAC9BC,UAAkB,EAClBC,gBAAwC,EACxCC,QAAgB,EAChBC,mBAA+C;IAE/C,IAAIC,QAAQL,MAAM,CAACC,WAAW;IAE9B,IAAIG,uBAAuBA,oBAAoBE,GAAG,CAACL,aAAa;QAC9DI,QAAQD,oBAAoBG,GAAG,CAACN;IAClC,OAAO,IAAIO,MAAMC,OAAO,CAACJ,QAAQ;QAC/BA,QAAQA,MAAMK,GAAG,CAAC,CAACC,IAAMC,mBAAmBD;IAC9C,OAAO,IAAI,OAAON,UAAU,UAAU;QACpCA,QAAQO,mBAAmBP;IAC7B;IAEA,IAAI,CAACA,OAAO;QACV,MAAMQ,aAAaX,qBAAqB;QACxC,MAAMY,qBAAqBZ,qBAAqB;QAEhD,IAAIW,cAAcC,oBAAoB;YACpC,oEAAoE;YACpE,6DAA6D;YAC7D,IAAIA,oBAAoB;gBACtB,OAAO;oBACLC,OAAOd;oBACPI,OAAO;oBACPW,MAAMd;oBACNe,aAAa;wBAAChB;wBAAY;wBAAIC;qBAAiB;gBACjD;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxFG,QAAQF,SACLe,KAAK,CAAC,KACP,gCAAgC;aAC/BC,KAAK,CAAC,GACP,oDAAoD;aACnDC,OAAO,CAAC,CAACC;gBACR,MAAMN,QAAQhB,eAAesB;oBAGtBrB;gBAFP,yDAAyD;gBACzD,wDAAwD;gBACxD,OAAOA,CAAAA,oBAAAA,MAAM,CAACe,MAAMO,GAAG,CAAC,KAAA,OAAjBtB,oBAAqBe,MAAMO,GAAG;YACvC;YAEF,OAAO;gBACLP,OAAOd;gBACPI;gBACAW,MAAMd;gBACN,wCAAwC;gBACxCe,aAAa;oBAAChB;oBAAYI,MAAMkB,IAAI,CAAC;oBAAMrB;iBAAiB;YAC9D;QACF;IACF;IAEA,OAAO;QACLa,OAAOd;QACP,yCAAyC;QACzCI,OAAOA;QACP,iDAAiD;QACjDY,aAAa;YACXhB;YACAO,MAAMC,OAAO,CAACJ,SAASA,MAAMkB,IAAI,CAAC,OAAOlB;YACzCH;SACD;QACDc,MAAMd;IACR;AACF;AAWO,MAAMN,oBAAoB;AAc1B,SAASG,eAAegB,KAAa;IAC1C,MAAMS,QAAQT,MAAMS,KAAK,CAAC5B;IAE1B,IAAI,CAAC4B,OAAO;QACV,OAAO1B,sBAAsBiB;IAC/B;IAEA,OAAOjB,sBAAsB0B,KAAK,CAAC,EAAE;AACvC;AAaO,SAAS1B,sBAAsBiB,KAAa;IACjD,MAAMU,WAAWV,MAAMW,UAAU,CAAC,QAAQX,MAAMY,QAAQ,CAAC;IACzD,IAAIF,UAAU;QACZV,QAAQA,MAAMI,KAAK,CAAC,GAAG,CAAC;IAC1B;IACA,MAAMS,SAASb,MAAMW,UAAU,CAAC;IAChC,IAAIE,QAAQ;QACVb,QAAQA,MAAMI,KAAK,CAAC;IACtB;IACA,OAAO;QAAEG,KAAKP;QAAOa;QAAQH;IAAS;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/route-regex.ts"], "sourcesContent": ["import {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../../../lib/constants'\nimport { INTERCEPTION_ROUTE_MARKERS } from './interception-routes'\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { PARAMETER_PATTERN, parseMatchedParameter } from './get-dynamic-param'\n\nexport interface Group {\n  pos: number\n  repeat: boolean\n  optional: boolean\n}\n\nexport interface RouteRegex {\n  groups: { [groupName: string]: Group }\n  re: RegExp\n}\n\ntype GetNamedRouteRegexOptions = {\n  /**\n   * Whether to prefix the route keys with the NEXT_INTERCEPTION_MARKER_PREFIX\n   * or NEXT_QUERY_PARAM_PREFIX. This is only relevant when creating the\n   * routes-manifest during the build.\n   */\n  prefixRouteKeys: boolean\n\n  /**\n   * Whether to include the suffix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n\n  /**\n   * Whether to backtrack duplicate keys. This is only relevant when creating\n   * the routes-manifest during the build.\n   */\n  backreferenceDuplicateKeys?: boolean\n}\n\ntype GetRouteRegexOptions = {\n  /**\n   * Whether to include extra parts in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   * of adding this option.\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n}\n\nfunction getParametrizedRoute(\n  route: string,\n  includeSuffix: boolean,\n  includePrefix: boolean\n) {\n  const groups: { [groupName: string]: Group } = {}\n  let groupIndex = 1\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const markerMatch = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n      segment.startsWith(m)\n    )\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (markerMatch && paramMatches && paramMatches[2]) {\n      const { key, optional, repeat } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n      segments.push(`/${escapeStringRegexp(markerMatch)}([^/]+?)`)\n    } else if (paramMatches && paramMatches[2]) {\n      const { key, repeat, optional } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = repeat ? (optional ? '(?:/(.+?))?' : '/(.+?)') : '/([^/]+?)'\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    parameterizedRoute: segments.join(''),\n    groups,\n  }\n}\n\n/**\n * From a normalized route this function generates a regular expression and\n * a corresponding groups object intended to be used to store matching groups\n * from the regular expression.\n */\nexport function getRouteRegex(\n  normalizedRoute: string,\n  {\n    includeSuffix = false,\n    includePrefix = false,\n    excludeOptionalTrailingSlash = false,\n  }: GetRouteRegexOptions = {}\n): RouteRegex {\n  const { parameterizedRoute, groups } = getParametrizedRoute(\n    normalizedRoute,\n    includeSuffix,\n    includePrefix\n  )\n\n  let re = parameterizedRoute\n  if (!excludeOptionalTrailingSlash) {\n    re += '(?:/)?'\n  }\n\n  return {\n    re: new RegExp(`^${re}$`),\n    groups: groups,\n  }\n}\n\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */\nfunction buildGetSafeRouteKey() {\n  let i = 0\n\n  return () => {\n    let routeKey = ''\n    let j = ++i\n    while (j > 0) {\n      routeKey += String.fromCharCode(97 + ((j - 1) % 26))\n      j = Math.floor((j - 1) / 26)\n    }\n    return routeKey\n  }\n}\n\nfunction getSafeKeyFromSegment({\n  interceptionMarker,\n  getSafeRouteKey,\n  segment,\n  routeKeys,\n  keyPrefix,\n  backreferenceDuplicateKeys,\n}: {\n  interceptionMarker?: string\n  getSafeRouteKey: () => string\n  segment: string\n  routeKeys: Record<string, string>\n  keyPrefix?: string\n  backreferenceDuplicateKeys: boolean\n}) {\n  const { key, optional, repeat } = parseMatchedParameter(segment)\n\n  // replace any non-word characters since they can break\n  // the named regex\n  let cleanedKey = key.replace(/\\W/g, '')\n\n  if (keyPrefix) {\n    cleanedKey = `${keyPrefix}${cleanedKey}`\n  }\n  let invalidKey = false\n\n  // check if the key is still invalid and fallback to using a known\n  // safe key\n  if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n    invalidKey = true\n  }\n  if (!isNaN(parseInt(cleanedKey.slice(0, 1)))) {\n    invalidKey = true\n  }\n\n  if (invalidKey) {\n    cleanedKey = getSafeRouteKey()\n  }\n\n  const duplicateKey = cleanedKey in routeKeys\n\n  if (keyPrefix) {\n    routeKeys[cleanedKey] = `${keyPrefix}${key}`\n  } else {\n    routeKeys[cleanedKey] = key\n  }\n\n  // if the segment has an interception marker, make sure that's part of the regex pattern\n  // this is to ensure that the route with the interception marker doesn't incorrectly match\n  // the non-intercepted route (ie /app/(.)[username] should not match /app/[username])\n  const interceptionPrefix = interceptionMarker\n    ? escapeStringRegexp(interceptionMarker)\n    : ''\n\n  let pattern: string\n  if (duplicateKey && backreferenceDuplicateKeys) {\n    // Use a backreference to the key to ensure that the key is the same value\n    // in each of the placeholders.\n    pattern = `\\\\k<${cleanedKey}>`\n  } else if (repeat) {\n    pattern = `(?<${cleanedKey}>.+?)`\n  } else {\n    pattern = `(?<${cleanedKey}>[^/]+?)`\n  }\n\n  return optional\n    ? `(?:/${interceptionPrefix}${pattern})?`\n    : `/${interceptionPrefix}${pattern}`\n}\n\nfunction getNamedParametrizedRoute(\n  route: string,\n  prefixRouteKeys: boolean,\n  includeSuffix: boolean,\n  includePrefix: boolean,\n  backreferenceDuplicateKeys: boolean\n) {\n  const getSafeRouteKey = buildGetSafeRouteKey()\n  const routeKeys: { [named: string]: string } = {}\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const hasInterceptionMarker = INTERCEPTION_ROUTE_MARKERS.some((m) =>\n      segment.startsWith(m)\n    )\n\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (hasInterceptionMarker && paramMatches && paramMatches[2]) {\n      // If there's an interception marker, add it to the segments.\n      segments.push(\n        getSafeKeyFromSegment({\n          getSafeRouteKey,\n          interceptionMarker: paramMatches[1],\n          segment: paramMatches[2],\n          routeKeys,\n          keyPrefix: prefixRouteKeys\n            ? NEXT_INTERCEPTION_MARKER_PREFIX\n            : undefined,\n          backreferenceDuplicateKeys,\n        })\n      )\n    } else if (paramMatches && paramMatches[2]) {\n      // If there's a prefix, add it to the segments if it's enabled.\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = getSafeKeyFromSegment({\n        getSafeRouteKey,\n        segment: paramMatches[2],\n        routeKeys,\n        keyPrefix: prefixRouteKeys ? NEXT_QUERY_PARAM_PREFIX : undefined,\n        backreferenceDuplicateKeys,\n      })\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    namedParameterizedRoute: segments.join(''),\n    routeKeys,\n  }\n}\n\n/**\n * This function extends `getRouteRegex` generating also a named regexp where\n * each group is named along with a routeKeys object that indexes the assigned\n * named group with its corresponding key. When the routeKeys need to be\n * prefixed to uniquely identify internally the \"prefixRouteKey\" arg should\n * be \"true\" currently this is only the case when creating the routes-manifest\n * during the build\n */\nexport function getNamedRouteRegex(\n  normalizedRoute: string,\n  options: GetNamedRouteRegexOptions\n) {\n  const result = getNamedParametrizedRoute(\n    normalizedRoute,\n    options.prefixRouteKeys,\n    options.includeSuffix ?? false,\n    options.includePrefix ?? false,\n    options.backreferenceDuplicateKeys ?? false\n  )\n\n  let namedRegex = result.namedParameterizedRoute\n  if (!options.excludeOptionalTrailingSlash) {\n    namedRegex += '(?:/)?'\n  }\n\n  return {\n    ...getRouteRegex(normalizedRoute, options),\n    namedRegex: `^${namedRegex}$`,\n    routeKeys: result.routeKeys,\n  }\n}\n\n/**\n * Generates a named regexp.\n * This is intended to be using for build time only.\n */\nexport function getNamedMiddlewareRegex(\n  normalizedRoute: string,\n  options: {\n    catchAll?: boolean\n  }\n) {\n  const { parameterizedRoute } = getParametrizedRoute(\n    normalizedRoute,\n    false,\n    false\n  )\n  const { catchAll = true } = options\n  if (parameterizedRoute === '/') {\n    let catchAllRegex = catchAll ? '.*' : ''\n    return {\n      namedRegex: `^/${catchAllRegex}$`,\n    }\n  }\n\n  const { namedParameterizedRoute } = getNamedParametrizedRoute(\n    normalizedRoute,\n    false,\n    false,\n    false,\n    false\n  )\n  let catchAllGroupedRegex = catchAll ? '(?:(/.*)?)' : ''\n  return {\n    namedRegex: `^${namedParameterizedRoute}${catchAllGroupedRegex}$`,\n  }\n}\n"], "names": ["getNamedMiddlewareRegex", "getNamedRouteRegex", "getRouteRegex", "getParametrizedRoute", "route", "includeSuffix", "includePrefix", "groups", "groupIndex", "segments", "segment", "removeTrailingSlash", "slice", "split", "markerMatch", "INTERCEPTION_ROUTE_MARKERS", "find", "m", "startsWith", "paramMatch<PERSON>", "match", "PARAMETER_PATTERN", "key", "optional", "repeat", "parseMatchedParameter", "pos", "push", "escapeStringRegexp", "s", "substring", "parameterizedRoute", "join", "normalizedRoute", "excludeOptionalTrailingSlash", "re", "RegExp", "buildGetSafeRouteKey", "i", "routeKey", "j", "String", "fromCharCode", "Math", "floor", "getSafeKeyFromSegment", "<PERSON><PERSON><PERSON><PERSON>", "getSafeRouteKey", "routeKeys", "keyPrefix", "backreferenceDuplicateKeys", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "duplicate<PERSON>ey", "interceptionPrefix", "pattern", "getNamedParametrizedRoute", "prefixRouteKeys", "hasInterceptionMarker", "some", "NEXT_INTERCEPTION_MARKER_PREFIX", "undefined", "NEXT_QUERY_PARAM_PREFIX", "namedParameterizedRoute", "options", "result", "namedRegex", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": ";;;;;;;;;;;;;;;IAiWgBA,uBAAuB,EAAA;eAAvBA;;IA5BAC,kBAAkB,EAAA;eAAlBA;;IA7LAC,aAAa,EAAA;eAAbA;;;2BArIT;oCACoC;8BACR;qCACC;iCACqB;AAyEzD,SAASC,qBACPC,KAAa,EACbC,aAAsB,EACtBC,aAAsB;IAEtB,MAAMC,SAAyC,CAAC;IAChD,IAAIC,aAAa;IAEjB,MAAMC,WAAqB,EAAE;IAC7B,KAAK,MAAMC,WAAWC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACP,OAAOQ,KAAK,CAAC,GAAGC,KAAK,CAAC,KAAM;QACpE,MAAMC,cAAcC,oBAAAA,0BAA0B,CAACC,IAAI,CAAC,CAACC,IACnDP,QAAQQ,UAAU,CAACD;QAErB,MAAME,eAAeT,QAAQU,KAAK,CAACC,iBAAAA,iBAAiB,EAAE,uBAAuB;;QAE7E,IAAIP,eAAeK,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAClD,MAAM,EAAEG,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGC,CAAAA,GAAAA,iBAAAA,qBAAqB,EAACN,YAAY,CAAC,EAAE;YACvEZ,MAAM,CAACe,IAAI,GAAG;gBAAEI,KAAKlB;gBAAcgB;gBAAQD;YAAS;YACpDd,SAASkB,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACd,eAAa;QACpD,OAAO,IAAIK,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,MAAM,EAAEG,GAAG,EAAEE,MAAM,EAAED,QAAQ,EAAE,GAAGE,CAAAA,GAAAA,iBAAAA,qBAAqB,EAACN,YAAY,CAAC,EAAE;YACvEZ,MAAM,CAACe,IAAI,GAAG;gBAAEI,KAAKlB;gBAAcgB;gBAAQD;YAAS;YAEpD,IAAIjB,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCV,SAASkB,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACT,YAAY,CAAC,EAAE;YACtD;YAEA,IAAIU,IAAIL,SAAUD,WAAW,gBAAgB,WAAY;YAEzD,8DAA8D;YAC9D,IAAIjB,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCU,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEArB,SAASkB,IAAI,CAACE;QAChB,OAAO;YACLpB,SAASkB,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAAClB;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBc,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDV,SAASkB,IAAI,CAACC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACT,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLY,oBAAoBtB,SAASuB,IAAI,CAAC;QAClCzB;IACF;AACF;AAOO,SAASL,cACd+B,eAAuB,EACvB,KAAA;IAAA,IAAA,EACE5B,gBAAgB,KAAK,EACrBC,gBAAgB,KAAK,EACrB4B,+BAA+B,KAAK,EACf,GAJvB,UAAA,KAAA,IAI0B,CAAC,IAJ3B;IAMA,MAAM,EAAEH,kBAAkB,EAAExB,MAAM,EAAE,GAAGJ,qBACrC8B,iBACA5B,eACAC;IAGF,IAAI6B,KAAKJ;IACT,IAAI,CAACG,8BAA8B;QACjCC,MAAM;IACR;IAEA,OAAO;QACLA,IAAI,IAAIC,OAAQ,MAAGD,KAAG;QACtB5B,QAAQA;IACV;AACF;AAEA;;;CAGC,GACD,SAAS8B;IACP,IAAIC,IAAI;IAER,OAAO;QACL,IAAIC,WAAW;QACf,IAAIC,IAAI,EAAEF;QACV,MAAOE,IAAI,EAAG;YACZD,YAAYE,OAAOC,YAAY,CAAC,KAAOF,CAAAA,IAAI,CAAA,IAAK;YAChDA,IAAIG,KAAKC,KAAK,CAAEJ,CAAAA,IAAI,CAAA,IAAK;QAC3B;QACA,OAAOD;IACT;AACF;AAEA,SAASM,sBAAsB,KAc9B;IAd8B,IAAA,EAC7BC,kBAAkB,EAClBC,eAAe,EACfrC,OAAO,EACPsC,SAAS,EACTC,SAAS,EACTC,0BAA0B,EAQ3B,GAd8B;IAe7B,MAAM,EAAE5B,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGC,CAAAA,GAAAA,iBAAAA,qBAAqB,EAACf;IAExD,uDAAuD;IACvD,kBAAkB;IAClB,IAAIyC,aAAa7B,IAAI8B,OAAO,CAAC,OAAO;IAEpC,IAAIH,WAAW;QACbE,aAAc,KAAEF,YAAYE;IAC9B;IACA,IAAIE,aAAa;IAEjB,kEAAkE;IAClE,WAAW;IACX,IAAIF,WAAWG,MAAM,KAAK,KAAKH,WAAWG,MAAM,GAAG,IAAI;QACrDD,aAAa;IACf;IACA,IAAI,CAACE,MAAMC,SAASL,WAAWvC,KAAK,CAAC,GAAG,MAAM;QAC5CyC,aAAa;IACf;IAEA,IAAIA,YAAY;QACdF,aAAaJ;IACf;IAEA,MAAMU,eAAeN,cAAcH;IAEnC,IAAIC,WAAW;QACbD,SAAS,CAACG,WAAW,GAAI,KAAEF,YAAY3B;IACzC,OAAO;QACL0B,SAAS,CAACG,WAAW,GAAG7B;IAC1B;IAEA,wFAAwF;IACxF,0FAA0F;IAC1F,qFAAqF;IACrF,MAAMoC,qBAAqBZ,qBACvBlB,CAAAA,GAAAA,cAAAA,kBAAkB,EAACkB,sBACnB;IAEJ,IAAIa;IACJ,IAAIF,gBAAgBP,4BAA4B;QAC9C,0EAA0E;QAC1E,+BAA+B;QAC/BS,UAAW,SAAMR,aAAW;IAC9B,OAAO,IAAI3B,QAAQ;QACjBmC,UAAW,QAAKR,aAAW;IAC7B,OAAO;QACLQ,UAAW,QAAKR,aAAW;IAC7B;IAEA,OAAO5B,WACF,SAAMmC,qBAAqBC,UAAQ,OACnC,MAAGD,qBAAqBC;AAC/B;AAEA,SAASC,0BACPxD,KAAa,EACbyD,eAAwB,EACxBxD,aAAsB,EACtBC,aAAsB,EACtB4C,0BAAmC;IAEnC,MAAMH,kBAAkBV;IACxB,MAAMW,YAAyC,CAAC;IAEhD,MAAMvC,WAAqB,EAAE;IAC7B,KAAK,MAAMC,WAAWC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACP,OAAOQ,KAAK,CAAC,GAAGC,KAAK,CAAC,KAAM;QACpE,MAAMiD,wBAAwB/C,oBAAAA,0BAA0B,CAACgD,IAAI,CAAC,CAAC9C,IAC7DP,QAAQQ,UAAU,CAACD;QAGrB,MAAME,eAAeT,QAAQU,KAAK,CAACC,iBAAAA,iBAAiB,EAAE,uBAAuB;;QAE7E,IAAIyC,yBAAyB3C,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC5D,6DAA6D;YAC7DV,SAASkB,IAAI,CACXkB,sBAAsB;gBACpBE;gBACAD,oBAAoB3B,YAAY,CAAC,EAAE;gBACnCT,SAASS,YAAY,CAAC,EAAE;gBACxB6B;gBACAC,WAAWY,kBACPG,WAAAA,+BAA+B,GAC/BC;gBACJf;YACF;QAEJ,OAAO,IAAI/B,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,+DAA+D;YAC/D,IAAIb,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCV,SAASkB,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACT,YAAY,CAAC,EAAE;YACtD;YAEA,IAAIU,IAAIgB,sBAAsB;gBAC5BE;gBACArC,SAASS,YAAY,CAAC,EAAE;gBACxB6B;gBACAC,WAAWY,kBAAkBK,WAAAA,uBAAuB,GAAGD;gBACvDf;YACF;YAEA,8DAA8D;YAC9D,IAAI5C,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCU,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEArB,SAASkB,IAAI,CAACE;QAChB,OAAO;YACLpB,SAASkB,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAAClB;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBc,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDV,SAASkB,IAAI,CAACC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACT,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLgD,yBAAyB1D,SAASuB,IAAI,CAAC;QACvCgB;IACF;AACF;AAUO,SAAS/C,mBACdgC,eAAuB,EACvBmC,OAAkC;QAKhCA,wBACAA,wBACAA;IALF,MAAMC,SAAST,0BACb3B,iBACAmC,QAAQP,eAAe,EACvBO,CAAAA,yBAAAA,QAAQ/D,aAAa,KAAA,OAArB+D,yBAAyB,OACzBA,CAAAA,yBAAAA,QAAQ9D,aAAa,KAAA,OAArB8D,yBAAyB,OACzBA,CAAAA,sCAAAA,QAAQlB,0BAA0B,KAAA,OAAlCkB,sCAAsC;IAGxC,IAAIE,aAAaD,OAAOF,uBAAuB;IAC/C,IAAI,CAACC,QAAQlC,4BAA4B,EAAE;QACzCoC,cAAc;IAChB;IAEA,OAAO;QACL,GAAGpE,cAAc+B,iBAAiBmC,QAAQ;QAC1CE,YAAa,MAAGA,aAAW;QAC3BtB,WAAWqB,OAAOrB,SAAS;IAC7B;AACF;AAMO,SAAShD,wBACdiC,eAAuB,EACvBmC,OAEC;IAED,MAAM,EAAErC,kBAAkB,EAAE,GAAG5B,qBAC7B8B,iBACA,OACA;IAEF,MAAM,EAAEsC,WAAW,IAAI,EAAE,GAAGH;IAC5B,IAAIrC,uBAAuB,KAAK;QAC9B,IAAIyC,gBAAgBD,WAAW,OAAO;QACtC,OAAO;YACLD,YAAa,OAAIE,gBAAc;QACjC;IACF;IAEA,MAAM,EAAEL,uBAAuB,EAAE,GAAGP,0BAClC3B,iBACA,OACA,OACA,OACA;IAEF,IAAIwC,uBAAuBF,WAAW,eAAe;IACrD,OAAO;QACLD,YAAa,MAAGH,0BAA0BM,uBAAqB;IACjE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/interpolate-as.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nimport { getRouteMatcher } from './route-matcher'\nimport { getRouteRegex } from './route-regex'\n\nexport function interpolateAs(\n  route: string,\n  asPathname: string,\n  query: ParsedUrlQuery\n) {\n  let interpolatedRoute = ''\n\n  const dynamicRegex = getRouteRegex(route)\n  const dynamicGroups = dynamicRegex.groups\n  const dynamicMatches =\n    // Try to match the dynamic route against the asPath\n    (asPathname !== route ? getRouteMatcher(dynamicRegex)(asPathname) : '') ||\n    // Fall back to reading the values from the href\n    // TODO: should this take priority; also need to change in the router.\n    query\n\n  interpolatedRoute = route\n  const params = Object.keys(dynamicGroups)\n\n  if (\n    !params.every((param) => {\n      let value = dynamicMatches[param] || ''\n      const { repeat, optional } = dynamicGroups[param]\n\n      // support single-level catch-all\n      // TODO: more robust handling for user-error (passing `/`)\n      let replaced = `[${repeat ? '...' : ''}${param}]`\n      if (optional) {\n        replaced = `${!value ? '/' : ''}[${replaced}]`\n      }\n      if (repeat && !Array.isArray(value)) value = [value]\n\n      return (\n        (optional || param in dynamicMatches) &&\n        // Interpolate group into data URL if present\n        (interpolatedRoute =\n          interpolatedRoute!.replace(\n            replaced,\n            repeat\n              ? (value as string[])\n                  .map(\n                    // these values should be fully encoded instead of just\n                    // path delimiter escaped since they are being inserted\n                    // into the URL and we expect URL encoded segments\n                    // when parsing dynamic route params\n                    (segment) => encodeURIComponent(segment)\n                  )\n                  .join('/')\n              : encodeURIComponent(value as string)\n          ) || '/')\n      )\n    })\n  ) {\n    interpolatedRoute = '' // did not satisfy all requirements\n\n    // n.b. We ignore this error because we handle warning for this case in\n    // development in the `<Link>` component directly.\n  }\n  return {\n    params,\n    result: interpolatedRoute,\n  }\n}\n"], "names": ["interpolateAs", "route", "asPathname", "query", "interpolatedRoute", "dynamicRegex", "getRouteRegex", "dynamicGroups", "groups", "dynamicMatches", "getRouteMatcher", "params", "Object", "keys", "every", "param", "value", "repeat", "optional", "replaced", "Array", "isArray", "replace", "map", "segment", "encodeURIComponent", "join", "result"], "mappings": ";;;+BAKgBA,iBAAAA;;;eAAAA;;;8BAHgB;4BACF;AAEvB,SAASA,cACdC,KAAa,EACbC,UAAkB,EAClBC,KAAqB;IAErB,IAAIC,oBAAoB;IAExB,MAAMC,eAAeC,CAAAA,GAAAA,YAAAA,aAAa,EAACL;IACnC,MAAMM,gBAAgBF,aAAaG,MAAM;IACzC,MAAMC,iBAEJ,AADA,AACCP,CAAAA,eAAeD,QAAQS,CAAAA,GAAAA,cAAAA,UAD4B,KACb,EAACL,cAAcH,cAAc,EAAC,KACrE,gDAAgD;IAChD,sEAAsE;IACtEC;IAEFC,oBAAoBH;IACpB,MAAMU,SAASC,OAAOC,IAAI,CAACN;IAE3B,IACE,CAACI,OAAOG,KAAK,CAAC,CAACC;QACb,IAAIC,QAAQP,cAAc,CAACM,MAAM,IAAI;QACrC,MAAM,EAAEE,MAAM,EAAEC,QAAQ,EAAE,GAAGX,aAAa,CAACQ,MAAM;QAEjD,iCAAiC;QACjC,0DAA0D;QAC1D,IAAII,WAAY,MAAGF,CAAAA,SAAS,QAAQ,EAAC,IAAIF,QAAM;QAC/C,IAAIG,UAAU;YACZC,WAAc,CAAA,CAACH,QAAQ,MAAM,EAAC,IAAE,MAAGG,WAAS;QAC9C;QACA,IAAIF,UAAU,CAACG,MAAMC,OAAO,CAACL,QAAQA,QAAQ;YAACA;SAAM;QAEpD,OACGE,CAAAA,YAAYH,SAASN,cAAa,KACnC,6CAA6C;QAC5CL,CAAAA,oBACCA,kBAAmBkB,OAAO,CACxBH,UACAF,SACKD,MACEO,GAAG,CACF,AACA,uDAAuD,AADA;QAEvD,kDAAkD;QAClD,oCAAoC;QACpC,CAACC,UAAYC,mBAAmBD,UAEjCE,IAAI,CAAC,OACRD,mBAAmBT,WACpB,GAAE;IAEb,IACA;QACAZ,oBAAoB,GAAG,mCAAmC;;IAE1D,uEAAuE;IACvE,kDAAkD;IACpD;IACA,OAAO;QACLO;QACAgB,QAAQvB;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/get-asset-path-from-route.ts"], "sourcesContent": ["// Translates a logical route into its pages asset path (relative from a common prefix)\n// \"asset path\" being its javascript file, data file, prerendered html,...\nexport default function getAssetPathFromRoute(\n  route: string,\n  ext: string = ''\n): string {\n  const path =\n    route === '/'\n      ? '/index'\n      : /^\\/index(\\/|$)/.test(route)\n        ? `/index${route}`\n        : route\n  return path + ext\n}\n"], "names": ["getAssetPathFromRoute", "route", "ext", "path", "test"], "mappings": "AAAA,uFAAuF;AACvF,0EAA0E;;;;+BAC1E,WAAA;;;eAAwBA;;;AAAT,SAASA,sBACtBC,KAAa,EACbC,GAAgB;IAAhBA,IAAAA,QAAAA,KAAAA,GAAAA,MAAc;IAEd,MAAMC,OACJF,UAAU,MACN,WACA,iBAAiBG,IAAI,CAACH,SACnB,WAAQA,QACTA;IACR,OAAOE,OAAOD;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/parse-relative-url.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\nimport { getLocationOrigin } from '../../utils'\nimport { searchParamsToUrlQuery } from './querystring'\n\nexport interface ParsedRelativeUrl {\n  hash: string\n  href: string\n  pathname: string\n  query: ParsedUrlQuery\n  search: string\n  slashes: undefined\n}\n\n/**\n * Parses path-relative urls (e.g. `/hello/world?foo=bar`). If url isn't path-relative\n * (e.g. `./hello`) then at least base must be.\n * Absolute urls are rejected with one exception, in the browser, absolute urls that are on\n * the current origin will be parsed as relative\n */\nexport function parseRelativeUrl(\n  url: string,\n  base?: string,\n  parseQuery?: true\n): ParsedRelativeUrl\nexport function parseRelativeUrl(\n  url: string,\n  base: string | undefined,\n  parseQuery: false\n): Omit<ParsedRelativeUrl, 'query'>\nexport function parseRelativeUrl(\n  url: string,\n  base?: string,\n  parseQuery = true\n): ParsedRelativeUrl | Omit<ParsedRelativeUrl, 'query'> {\n  const globalBase = new URL(\n    typeof window === 'undefined' ? 'http://n' : getLocationOrigin()\n  )\n\n  const resolvedBase = base\n    ? new URL(base, globalBase)\n    : url.startsWith('.')\n      ? new URL(\n          typeof window === 'undefined' ? 'http://n' : window.location.href\n        )\n      : globalBase\n\n  const { pathname, searchParams, search, hash, href, origin } = new URL(\n    url,\n    resolvedBase\n  )\n\n  if (origin !== globalBase.origin) {\n    throw new Error(`invariant: invalid relative URL, router received ${url}`)\n  }\n\n  return {\n    pathname,\n    query: parseQuery ? searchParamsToUrlQuery(searchParams) : undefined,\n    search,\n    hash,\n    href: href.slice(origin.length),\n    // We don't know for relative URLs at this point since we set a custom, internal\n    // base that isn't surfaced to users.\n    slashes: undefined,\n  }\n}\n"], "names": ["parseRelativeUrl", "url", "base", "parse<PERSON><PERSON>y", "globalBase", "URL", "window", "getLocationOrigin", "resolvedBase", "startsWith", "location", "href", "pathname", "searchParams", "search", "hash", "origin", "Error", "query", "searchParamsToUrlQuery", "undefined", "slice", "length", "slashes"], "mappings": ";;;+BA6BgBA,oBAAAA;;;eAAAA;;;uBA5BkB;6BACK;AA2BhC,SAASA,iBACdC,GAAW,EACXC,IAAa,EACbC,UAAiB;IAAjBA,IAAAA,eAAAA,KAAAA,GAAAA,aAAa;IAEb,MAAMC,aAAa,IAAIC,IACrB,OAAOC,WAAW,cAAc,aAAaC,CAAAA,GAAAA,OAAAA,iBAAiB;IAGhE,MAAMC,eAAeN,OACjB,IAAIG,IAAIH,MAAME,cACdH,IAAIQ,UAAU,CAAC,OACb,IAAIJ,IACF,OAAOC,WAAW,cAAc,aAAaA,OAAOI,QAAQ,CAACC,IAAI,IAEnEP;IAEN,MAAM,EAAEQ,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,IAAI,EAAEJ,IAAI,EAAEK,MAAM,EAAE,GAAG,IAAIX,IACjEJ,KACAO;IAGF,IAAIQ,WAAWZ,WAAWY,MAAM,EAAE;QAChC,MAAM,OAAA,cAAoE,CAApE,IAAIC,MAAO,sDAAmDhB,MAA9D,qBAAA;mBAAA;wBAAA;0BAAA;QAAmE;IAC3E;IAEA,OAAO;QACLW;QACAM,OAAOf,aAAagB,CAAAA,GAAAA,aAAAA,sBAAsB,EAACN,gBAAgBO;QAC3DN;QACAC;QACAJ,MAAMA,KAAKU,KAAK,CAACL,OAAOM,MAAM;QAC9B,gFAAgF;QAChF,qCAAqC;QACrCC,SAASH;IACX;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/encode-uri-path.ts"], "sourcesContent": ["export function encodeURIPath(file: string) {\n  return file\n    .split('/')\n    .map((p) => encodeURIComponent(p))\n    .join('/')\n}\n"], "names": ["encodeURIPath", "file", "split", "map", "p", "encodeURIComponent", "join"], "mappings": ";;;+BAAgBA,iBAAAA;;;eAAAA;;;AAAT,SAASA,cAAcC,IAAY;IACxC,OAAOA,KACJC,KAAK,CAAC,KACNC,GAAG,CAAC,CAACC,IAAMC,mBAAmBD,IAC9BE,IAAI,CAAC;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/modern-browserslist-target.js"], "sourcesContent": ["// Note: This file is JS because it's used by the taskfile-swc.js file, which is JS.\n// Keep file changes in sync with the corresponding `.d.ts` files.\n/**\n * These are the browser versions that support all of the following:\n * static import: https://caniuse.com/es6-module\n * dynamic import: https://caniuse.com/es6-module-dynamic-import\n * import.meta: https://caniuse.com/mdn-javascript_operators_import_meta\n */\nconst MODERN_BROWSERSLIST_TARGET = [\n  'chrome 64',\n  'edge 79',\n  'firefox 67',\n  'opera 51',\n  'safari 12',\n]\n\nmodule.exports = MODERN_BROWSERSLIST_TARGET\n"], "names": ["MODERN_BROWSERSLIST_TARGET", "module", "exports"], "mappings": "AAAA,oFAAoF;AACpF,kEAAkE;AAClE;;;;;CAKC,GACD,MAAMA,6BAA6B;IACjC;IACA;IACA;IACA;IACA;CACD;AAEDC,OAAOC,OAAO,GAAGF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1497, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/constants.ts"], "sourcesContent": ["import MODERN_BROWSERSLIST_TARGET from './modern-browserslist-target'\n\nexport { MODERN_BROWSERSLIST_TARGET }\n\nexport type ValueOf<T> = Required<T>[keyof T]\n\nexport const COMPILER_NAMES = {\n  client: 'client',\n  server: 'server',\n  edgeServer: 'edge-server',\n} as const\n\nexport type CompilerNameValues = ValueOf<typeof COMPILER_NAMES>\n\nexport enum AdapterOutputType {\n  /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */\n  PAGES = 'PAGES',\n\n  /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */\n  PAGES_API = 'PAGES_API',\n  /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */\n  APP_PAGE = 'APP_PAGE',\n\n  /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */\n  APP_ROUTE = 'APP_ROUTE',\n\n  /**\n   * `PRERENDER` represents an ISR enabled route that might\n   * have a seeded cache entry or fallback generated during build\n   */\n  PRERENDER = 'PRERENDER',\n\n  /**\n   * `STATIC_FILE` represents a static file (ie /_next/static)\n   */\n  STATIC_FILE = 'STATIC_FILE',\n\n  /**\n   * `MIDDLEWARE` represents the middleware output if present\n   */\n  MIDDLEWARE = 'MIDDLEWARE',\n}\n\nexport const COMPILER_INDEXES: {\n  [compilerKey in CompilerNameValues]: number\n} = {\n  [COMPILER_NAMES.client]: 0,\n  [COMPILER_NAMES.server]: 1,\n  [COMPILER_NAMES.edgeServer]: 2,\n} as const\n\nexport const UNDERSCORE_NOT_FOUND_ROUTE = '/_not-found'\nexport const UNDERSCORE_NOT_FOUND_ROUTE_ENTRY = `${UNDERSCORE_NOT_FOUND_ROUTE}/page`\nexport const PHASE_EXPORT = 'phase-export'\nexport const PHASE_PRODUCTION_BUILD = 'phase-production-build'\nexport const PHASE_PRODUCTION_SERVER = 'phase-production-server'\nexport const PHASE_DEVELOPMENT_SERVER = 'phase-development-server'\nexport const PHASE_TEST = 'phase-test'\nexport const PHASE_INFO = 'phase-info'\nexport const PAGES_MANIFEST = 'pages-manifest.json'\nexport const WEBPACK_STATS = 'webpack-stats.json'\nexport const APP_PATHS_MANIFEST = 'app-paths-manifest.json'\nexport const APP_PATH_ROUTES_MANIFEST = 'app-path-routes-manifest.json'\nexport const BUILD_MANIFEST = 'build-manifest.json'\nexport const APP_BUILD_MANIFEST = 'app-build-manifest.json'\nexport const FUNCTIONS_CONFIG_MANIFEST = 'functions-config-manifest.json'\nexport const SUBRESOURCE_INTEGRITY_MANIFEST = 'subresource-integrity-manifest'\nexport const NEXT_FONT_MANIFEST = 'next-font-manifest'\nexport const EXPORT_MARKER = 'export-marker.json'\nexport const EXPORT_DETAIL = 'export-detail.json'\nexport const PRERENDER_MANIFEST = 'prerender-manifest.json'\nexport const ROUTES_MANIFEST = 'routes-manifest.json'\nexport const IMAGES_MANIFEST = 'images-manifest.json'\nexport const SERVER_FILES_MANIFEST = 'required-server-files.json'\nexport const DEV_CLIENT_PAGES_MANIFEST = '_devPagesManifest.json'\nexport const MIDDLEWARE_MANIFEST = 'middleware-manifest.json'\nexport const TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST =\n  '_clientMiddlewareManifest.json'\nexport const TURBOPACK_CLIENT_BUILD_MANIFEST = 'client-build-manifest.json'\nexport const DEV_CLIENT_MIDDLEWARE_MANIFEST = '_devMiddlewareManifest.json'\nexport const REACT_LOADABLE_MANIFEST = 'react-loadable-manifest.json'\nexport const SERVER_DIRECTORY = 'server'\nexport const CONFIG_FILES = [\n  'next.config.js',\n  'next.config.mjs',\n  'next.config.ts',\n]\nexport const BUILD_ID_FILE = 'BUILD_ID'\nexport const BLOCKED_PAGES = ['/_document', '/_app', '/_error']\nexport const CLIENT_PUBLIC_FILES_PATH = 'public'\nexport const CLIENT_STATIC_FILES_PATH = 'static'\nexport const STRING_LITERAL_DROP_BUNDLE = '__NEXT_DROP_CLIENT_FILE__'\nexport const NEXT_BUILTIN_DOCUMENT = '__NEXT_BUILTIN_DOCUMENT__'\nexport const BARREL_OPTIMIZATION_PREFIX = '__barrel_optimize__'\n\n// server/[entry]/page_client-reference-manifest.js\nexport const CLIENT_REFERENCE_MANIFEST = 'client-reference-manifest'\n// server/server-reference-manifest\nexport const SERVER_REFERENCE_MANIFEST = 'server-reference-manifest'\n// server/middleware-build-manifest.js\nexport const MIDDLEWARE_BUILD_MANIFEST = 'middleware-build-manifest'\n// server/middleware-react-loadable-manifest.js\nexport const MIDDLEWARE_REACT_LOADABLE_MANIFEST =\n  'middleware-react-loadable-manifest'\n// server/interception-route-rewrite-manifest.js\nexport const INTERCEPTION_ROUTE_REWRITE_MANIFEST =\n  'interception-route-rewrite-manifest'\n// server/dynamic-css-manifest.js\nexport const DYNAMIC_CSS_MANIFEST = 'dynamic-css-manifest'\n\n// static/runtime/main.js\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN = `main`\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN_APP = `${CLIENT_STATIC_FILES_RUNTIME_MAIN}-app`\n// next internal client components chunk for layouts\nexport const APP_CLIENT_INTERNALS = 'app-pages-internals'\n// static/runtime/react-refresh.js\nexport const CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH = `react-refresh`\n// static/runtime/amp.js\nexport const CLIENT_STATIC_FILES_RUNTIME_AMP = `amp`\n// static/runtime/webpack.js\nexport const CLIENT_STATIC_FILES_RUNTIME_WEBPACK = `webpack`\n// static/runtime/polyfills.js\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS = 'polyfills'\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL = Symbol(\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS\n)\nexport const DEFAULT_RUNTIME_WEBPACK = 'webpack-runtime'\nexport const EDGE_RUNTIME_WEBPACK = 'edge-runtime-webpack'\nexport const STATIC_PROPS_ID = '__N_SSG'\nexport const SERVER_PROPS_ID = '__N_SSP'\nexport const DEFAULT_SERIF_FONT = {\n  name: 'Times New Roman',\n  xAvgCharWidth: 821,\n  azAvgWidth: 854.3953488372093,\n  unitsPerEm: 2048,\n}\nexport const DEFAULT_SANS_SERIF_FONT = {\n  name: 'Arial',\n  xAvgCharWidth: 904,\n  azAvgWidth: 934.5116279069767,\n  unitsPerEm: 2048,\n}\nexport const STATIC_STATUS_PAGES = ['/500']\nexport const TRACE_OUTPUT_VERSION = 1\n// in `MB`\nexport const TURBO_TRACE_DEFAULT_MEMORY_LIMIT = 6000\n\nexport const RSC_MODULE_TYPES = {\n  client: 'client',\n  server: 'server',\n} as const\n\n// comparing\n// https://nextjs.org/docs/api-reference/edge-runtime\n// with\n// https://nodejs.org/docs/latest/api/globals.html\nexport const EDGE_UNSUPPORTED_NODE_APIS = [\n  'clearImmediate',\n  'setImmediate',\n  'BroadcastChannel',\n  'ByteLengthQueuingStrategy',\n  'CompressionStream',\n  'CountQueuingStrategy',\n  'DecompressionStream',\n  'DomException',\n  'MessageChannel',\n  'MessageEvent',\n  'MessagePort',\n  'ReadableByteStreamController',\n  'ReadableStreamBYOBRequest',\n  'ReadableStreamDefaultController',\n  'TransformStreamDefaultController',\n  'WritableStreamDefaultController',\n]\n\nexport const SYSTEM_ENTRYPOINTS = new Set<string>([\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n])\n"], "names": ["APP_BUILD_MANIFEST", "APP_CLIENT_INTERNALS", "APP_PATHS_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "AdapterOutputType", "BARREL_OPTIMIZATION_PREFIX", "BLOCKED_PAGES", "BUILD_ID_FILE", "BUILD_MANIFEST", "CLIENT_PUBLIC_FILES_PATH", "CLIENT_REFERENCE_MANIFEST", "CLIENT_STATIC_FILES_PATH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "COMPILER_INDEXES", "COMPILER_NAMES", "CONFIG_FILES", "DEFAULT_RUNTIME_WEBPACK", "DEFAULT_SANS_SERIF_FONT", "DEFAULT_SERIF_FONT", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "DEV_CLIENT_PAGES_MANIFEST", "DYNAMIC_CSS_MANIFEST", "EDGE_RUNTIME_WEBPACK", "EDGE_UNSUPPORTED_NODE_APIS", "EXPORT_DETAIL", "EXPORT_MARKER", "FUNCTIONS_CONFIG_MANIFEST", "IMAGES_MANIFEST", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "MODERN_BROWSERSLIST_TARGET", "NEXT_BUILTIN_DOCUMENT", "NEXT_FONT_MANIFEST", "PAGES_MANIFEST", "PHASE_DEVELOPMENT_SERVER", "PHASE_EXPORT", "PHASE_INFO", "PHASE_PRODUCTION_BUILD", "PHASE_PRODUCTION_SERVER", "PHASE_TEST", "PRERENDER_MANIFEST", "REACT_LOADABLE_MANIFEST", "ROUTES_MANIFEST", "RSC_MODULE_TYPES", "SERVER_DIRECTORY", "SERVER_FILES_MANIFEST", "SERVER_PROPS_ID", "SERVER_REFERENCE_MANIFEST", "STATIC_PROPS_ID", "STATIC_STATUS_PAGES", "STRING_LITERAL_DROP_BUNDLE", "SUBRESOURCE_INTEGRITY_MANIFEST", "SYSTEM_ENTRYPOINTS", "TRACE_OUTPUT_VERSION", "TURBOPACK_CLIENT_BUILD_MANIFEST", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "TURBO_TRACE_DEFAULT_MEMORY_LIMIT", "UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "WEBPACK_STATS", "client", "server", "edgeServer", "Symbol", "name", "xAvgCharWidth", "azAvgWidth", "unitsPerEm", "Set"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0EaA,kBAAkB,EAAA;eAAlBA;;IAkDAC,oBAAoB,EAAA;eAApBA;;IArDAC,kBAAkB,EAAA;eAAlBA;;IACAC,wBAAwB,EAAA;eAAxBA;;IA1DDC,iBAAiB,EAAA;eAAjBA;;IAyFCC,0BAA0B,EAAA;eAA1BA;;IALAC,aAAa,EAAA;eAAbA;;IADAC,aAAa,EAAA;eAAbA;;IAxBAC,cAAc,EAAA;eAAdA;;IA0BAC,wBAAwB,EAAA;eAAxBA;;IAOAC,yBAAyB,EAAA;eAAzBA;;IANAC,wBAAwB,EAAA;eAAxBA;;IA4BAC,+BAA+B,EAAA;eAA/BA;;IAPAC,gCAAgC,EAAA;eAAhCA;;IACAC,oCAAoC,EAAA;eAApCA;;IAUAC,qCAAqC,EAAA;eAArCA;;IACAC,4CAA4C,EAAA;eAA5CA;;IAPAC,yCAAyC,EAAA;eAAzCA;;IAIAC,mCAAmC,EAAA;eAAnCA;;IA7EAC,gBAAgB,EAAA;eAAhBA;;IA/CAC,cAAc,EAAA;eAAdA;;IAsFAC,YAAY,EAAA;eAAZA;;IA4CAC,uBAAuB,EAAA;eAAvBA;;IAUAC,uBAAuB,EAAA;eAAvBA;;IANAC,kBAAkB,EAAA;eAAlBA;;IAnDAC,8BAA8B,EAAA;eAA9BA;;IALAC,yBAAyB,EAAA;eAAzBA;;IAkCAC,oBAAoB,EAAA;eAApBA;;IAmBAC,oBAAoB,EAAA;eAApBA;;IA6BAC,0BAA0B,EAAA;eAA1BA;;IAvFAC,aAAa,EAAA;eAAbA;;IADAC,aAAa,EAAA;eAAbA;;IAHAC,yBAAyB,EAAA;eAAzBA;;IAOAC,eAAe,EAAA;eAAfA;;IAiCAC,mCAAmC,EAAA;eAAnCA;;IALAC,yBAAyB,EAAA;eAAzBA;;IAzBAC,mBAAmB,EAAA;eAAnBA;;IA2BAC,kCAAkC,EAAA;eAAlCA;;IA9GJC,0BAA0B,EAAA;eAA1BA,0BAAAA,OAA0B;;IAoGtBC,qBAAqB,EAAA;eAArBA;;IAzBAC,kBAAkB,EAAA;eAAlBA;;IARAC,cAAc,EAAA;eAAdA;;IAHAC,wBAAwB,EAAA;eAAxBA;;IAHAC,YAAY,EAAA;eAAZA;;IAKAC,UAAU,EAAA;eAAVA;;IAJAC,sBAAsB,EAAA;eAAtBA;;IACAC,uBAAuB,EAAA;eAAvBA;;IAEAC,UAAU,EAAA;eAAVA;;IAaAC,kBAAkB,EAAA;eAAlBA;;IAUAC,uBAAuB,EAAA;eAAvBA;;IATAC,eAAe,EAAA;eAAfA;;IA4EAC,gBAAgB,EAAA;eAAhBA;;IAlEAC,gBAAgB,EAAA;eAAhBA;;IARAC,qBAAqB,EAAA;eAArBA;;IAwDAC,eAAe,EAAA;eAAfA;;IA/BAC,yBAAyB,EAAA;eAAzBA;;IA8BAC,eAAe,EAAA;eAAfA;;IAcAC,mBAAmB,EAAA;eAAnBA;;IAnDAC,0BAA0B,EAAA;eAA1BA;;IAzBAC,8BAA8B,EAAA;eAA9BA;;IA6GAC,kBAAkB,EAAA;eAAlBA;;IAhCAC,oBAAoB,EAAA;eAApBA;;IAjEAC,+BAA+B,EAAA;eAA/BA;;IAFAC,oCAAoC,EAAA;eAApCA;;IAqEAC,gCAAgC,EAAA;eAAhCA;;IA9FAC,0BAA0B,EAAA;eAA1BA;;IACAC,gCAAgC,EAAA;eAAhCA;;IAQAC,aAAa,EAAA;eAAbA;;;;mFAtE0B;AAMhC,MAAM/C,iBAAiB;IAC5BgD,QAAQ;IACRC,QAAQ;IACRC,YAAY;AACd;AAIO,IAAKlE,oBAAAA,WAAAA,GAAAA,SAAAA,iBAAAA;IACV;;GAEC,GAAA,iBAAA,CAAA,QAAA,GAAA;IAGD;;GAEC,GAAA,iBAAA,CAAA,YAAA,GAAA;IAED;;;GAGC,GAAA,iBAAA,CAAA,WAAA,GAAA;IAGD;;;GAGC,GAAA,iBAAA,CAAA,YAAA,GAAA;IAGD;;;GAGC,GAAA,iBAAA,CAAA,YAAA,GAAA;IAGD;;GAEC,GAAA,iBAAA,CAAA,cAAA,GAAA;IAGD;;GAEC,GAAA,iBAAA,CAAA,aAAA,GAAA;WAnCSA;;AAuCL,MAAMe,mBAET;IACF,CAACC,eAAegD,MAAM,CAAC,EAAE;IACzB,CAAChD,eAAeiD,MAAM,CAAC,EAAE;IACzB,CAACjD,eAAekD,UAAU,CAAC,EAAE;AAC/B;AAEO,MAAML,6BAA6B;AACnC,MAAMC,mCAAoC,KAAED,6BAA2B;AACvE,MAAMtB,eAAe;AACrB,MAAME,yBAAyB;AAC/B,MAAMC,0BAA0B;AAChC,MAAMJ,2BAA2B;AACjC,MAAMK,aAAa;AACnB,MAAMH,aAAa;AACnB,MAAMH,iBAAiB;AACvB,MAAM0B,gBAAgB;AACtB,MAAMjE,qBAAqB;AAC3B,MAAMC,2BAA2B;AACjC,MAAMK,iBAAiB;AACvB,MAAMR,qBAAqB;AAC3B,MAAMgC,4BAA4B;AAClC,MAAM2B,iCAAiC;AACvC,MAAMnB,qBAAqB;AAC3B,MAAMT,gBAAgB;AACtB,MAAMD,gBAAgB;AACtB,MAAMkB,qBAAqB;AAC3B,MAAME,kBAAkB;AACxB,MAAMjB,kBAAkB;AACxB,MAAMoB,wBAAwB;AAC9B,MAAM3B,4BAA4B;AAClC,MAAMU,sBAAsB;AAC5B,MAAM2B,uCACX;AACK,MAAMD,kCAAkC;AACxC,MAAMrC,iCAAiC;AACvC,MAAMwB,0BAA0B;AAChC,MAAMG,mBAAmB;AACzB,MAAM/B,eAAe;IAC1B;IACA;IACA;CACD;AACM,MAAMd,gBAAgB;AACtB,MAAMD,gBAAgB;IAAC;IAAc;IAAS;CAAU;AACxD,MAAMG,2BAA2B;AACjC,MAAME,2BAA2B;AACjC,MAAM+C,6BAA6B;AACnC,MAAMnB,wBAAwB;AAC9B,MAAMlC,6BAA6B;AAGnC,MAAMK,4BAA4B;AAElC,MAAM6C,4BAA4B;AAElC,MAAMpB,4BAA4B;AAElC,MAAME,qCACX;AAEK,MAAMH,sCACX;AAEK,MAAMP,uBAAuB;AAG7B,MAAMd,mCAAoC;AAC1C,MAAMC,uCAAwC,KAAED,mCAAiC;AAEjF,MAAMZ,uBAAuB;AAE7B,MAAMgB,4CAA6C;AAEnD,MAAML,kCAAmC;AAEzC,MAAMM,sCAAuC;AAE7C,MAAMH,wCAAwC;AAC9C,MAAMC,+CAA+CuD,OAC1DxD;AAEK,MAAMO,0BAA0B;AAChC,MAAMM,uBAAuB;AAC7B,MAAM4B,kBAAkB;AACxB,MAAMF,kBAAkB;AACxB,MAAM9B,qBAAqB;IAChCgD,MAAM;IACNC,eAAe;IACfC,YAAY;IACZC,YAAY;AACd;AACO,MAAMpD,0BAA0B;IACrCiD,MAAM;IACNC,eAAe;IACfC,YAAY;IACZC,YAAY;AACd;AACO,MAAMlB,sBAAsB;IAAC;CAAO;AACpC,MAAMI,uBAAuB;AAE7B,MAAMG,mCAAmC;AAEzC,MAAMb,mBAAmB;IAC9BiB,QAAQ;IACRC,QAAQ;AACV;AAMO,MAAMxC,6BAA6B;IACxC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM+B,qBAAqB,IAAIgB,IAAY;IAChD/D;IACAI;IACAL;IACAE;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/bloom-filter.ts"], "sourcesContent": ["// minimal implementation MurmurHash2 hash function\nfunction murmurhash2(str: string) {\n  let h = 0\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i)\n    h = Math.imul(h ^ c, 0x5bd1e995)\n    h ^= h >>> 13\n    h = Math.imul(h, 0x5bd1e995)\n  }\n  return h >>> 0\n}\n\n// default to 0.01% error rate as the filter compresses very well\nconst DEFAULT_ERROR_RATE = 0.0001\n\nexport class BloomFilter {\n  numItems: number\n  errorRate: number\n  numBits: number\n  numHashes: number\n  bitArray: number[]\n\n  constructor(numItems: number, errorRate: number = DEFAULT_ERROR_RATE) {\n    this.numItems = numItems\n    this.errorRate = errorRate\n    this.numBits = Math.ceil(\n      -(numItems * Math.log(errorRate)) / (Math.log(2) * Math.log(2))\n    )\n    this.numHashes = Math.ceil((this.numBits / numItems) * Math.log(2))\n    this.bitArray = new Array(this.numBits).fill(0)\n  }\n\n  static from(items: string[], errorRate = DEFAULT_ERROR_RATE) {\n    const filter = new BloomFilter(items.length, errorRate)\n\n    for (const item of items) {\n      filter.add(item)\n    }\n    return filter\n  }\n\n  export() {\n    const data = {\n      numItems: this.numItems,\n      errorRate: this.errorRate,\n      numBits: this.numBits,\n      numHashes: this.numHashes,\n      bitArray: this.bitArray,\n    }\n\n    if (process.env.NEXT_RUNTIME === 'nodejs') {\n      if (this.errorRate < DEFAULT_ERROR_RATE) {\n        const filterData = JSON.stringify(data)\n        const gzipSize = (\n          require('next/dist/compiled/gzip-size') as typeof import('next/dist/compiled/gzip-size')\n        ).sync(filterData)\n\n        if (gzipSize > 1024) {\n          console.warn(\n            `Creating filter with error rate less than 0.1% (0.001) can increase the size dramatically proceed with caution. Received error rate ${this.errorRate} resulted in size ${filterData.length} bytes, ${gzipSize} bytes (gzip)`\n          )\n        }\n      }\n    }\n\n    return data\n  }\n\n  import(data: ReturnType<(typeof this)['export']>) {\n    this.numItems = data.numItems\n    this.errorRate = data.errorRate\n    this.numBits = data.numBits\n    this.numHashes = data.numHashes\n    this.bitArray = data.bitArray\n  }\n\n  add(item: string) {\n    const hashValues = this.getHashValues(item)\n    hashValues.forEach((hash) => {\n      this.bitArray[hash] = 1\n    })\n  }\n\n  contains(item: string) {\n    const hashValues = this.getHashValues(item)\n    return hashValues.every((hash) => this.bitArray[hash])\n  }\n\n  getHashValues(item: string) {\n    const hashValues = []\n    for (let i = 1; i <= this.numHashes; i++) {\n      const hash = murmurhash2(`${item}${i}`) % this.numBits\n      hashValues.push(hash)\n    }\n    return hashValues\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "murmurhash2", "str", "h", "i", "length", "c", "charCodeAt", "Math", "imul", "DEFAULT_ERROR_RATE", "from", "items", "errorRate", "filter", "item", "add", "export", "data", "numItems", "numBits", "numHashes", "bitArray", "process", "env", "NEXT_RUNTIME", "filterData", "JSON", "stringify", "gzipSize", "require", "sync", "console", "warn", "import", "hashValues", "getHashValues", "for<PERSON>ach", "hash", "contains", "every", "push", "constructor", "ceil", "log", "Array", "fill"], "mappings": "AAAA,mDAAmD;AAkD3CuB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAU;;;;;+BAnClCzB,eAAAA;;;eAAAA;;;AAdb,SAASC,YAAYC,GAAW;IAC9B,IAAIC,IAAI;IACR,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,MAAME,IAAIJ,IAAIK,UAAU,CAACH;QACzBD,IAAIK,KAAKC,IAAI,CAACN,IAAIG,GAAG;QACrBH,KAAKA,MAAM;QACXA,IAAIK,KAAKC,IAAI,CAACN,GAAG;IACnB;IACA,OAAOA,MAAM;AACf;AAEA,iEAAiE;AACjE,MAAMO,qBAAqB;AAEpB,MAAMV;IAiBX,OAAOW,KAAKC,KAAe,EAAEC,SAA8B,EAAE;QAAhCA,IAAAA,cAAAA,KAAAA,GAAAA,YAAYH;QACvC,MAAMI,SAAS,IAAId,YAAYY,MAAMP,MAAM,EAAEQ;QAE7C,KAAK,MAAME,QAAQH,MAAO;YACxBE,OAAOE,GAAG,CAACD;QACb;QACA,OAAOD;IACT;IAEAG,SAAS;QACP,MAAMC,OAAO;YACXC,UAAU,IAAI,CAACA,QAAQ;YACvBN,WAAW,IAAI,CAACA,SAAS;YACzBO,SAAS,IAAI,CAACA,OAAO;YACrBC,WAAW,IAAI,CAACA,SAAS;YACzBC,UAAU,IAAI,CAACA,QAAQ;QACzB;QAEA;;QAeA,OAAOJ;IACT;IAEAgB,OAAOhB,IAAyC,EAAE;QAChD,IAAI,CAACC,QAAQ,GAAGD,KAAKC,QAAQ;QAC7B,IAAI,CAACN,SAAS,GAAGK,KAAKL,SAAS;QAC/B,IAAI,CAACO,OAAO,GAAGF,KAAKE,OAAO;QAC3B,IAAI,CAACC,SAAS,GAAGH,KAAKG,SAAS;QAC/B,IAAI,CAACC,QAAQ,GAAGJ,KAAKI,QAAQ;IAC/B;IAEAN,IAAID,IAAY,EAAE;QAChB,MAAMoB,aAAa,IAAI,CAACC,aAAa,CAACrB;QACtCoB,WAAWE,OAAO,CAAC,CAACC;YAClB,IAAI,CAAChB,QAAQ,CAACgB,KAAK,GAAG;QACxB;IACF;IAEAC,SAASxB,IAAY,EAAE;QACrB,MAAMoB,aAAa,IAAI,CAACC,aAAa,CAACrB;QACtC,OAAOoB,WAAWK,KAAK,CAAC,CAACF,OAAS,IAAI,CAAChB,QAAQ,CAACgB,KAAK;IACvD;IAEAF,cAAcrB,IAAY,EAAE;QAC1B,MAAMoB,aAAa,EAAE;QACrB,IAAK,IAAI/B,IAAI,GAAGA,KAAK,IAAI,CAACiB,SAAS,EAAEjB,IAAK;YACxC,MAAMkC,OAAOrC,YAAa,KAAEc,OAAOX,KAAO,IAAI,CAACgB,OAAO;YACtDe,WAAWM,IAAI,CAACH;QAClB;QACA,OAAOH;IACT;IAzEAO,YAAYvB,QAAgB,EAAEN,YAAoBH,kBAAkB,CAAE;QACpE,IAAI,CAACS,QAAQ,GAAGA;QAChB,IAAI,CAACN,SAAS,GAAGA;QACjB,IAAI,CAACO,OAAO,GAAGZ,KAAKmC,IAAI,CACtB,CAAExB,CAAAA,WAAWX,KAAKoC,GAAG,CAAC/B,UAAS,IAAML,CAAAA,KAAKoC,GAAG,CAAC,KAAKpC,KAAKoC,GAAG,CAAC,EAAC;QAE/D,IAAI,CAACvB,SAAS,GAAGb,KAAKmC,IAAI,CAAE,IAAI,CAACvB,OAAO,GAAGD,WAAYX,KAAKoC,GAAG,CAAC;QAChE,IAAI,CAACtB,QAAQ,GAAG,IAAIuB,MAAM,IAAI,CAACzB,OAAO,EAAE0B,IAAI,CAAC;IAC/C;AAkEF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2023, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/is-plain-object.ts"], "sourcesContent": ["export function getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value)\n}\n\nexport function isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n\n  /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */\n  return prototype === null || prototype.hasOwnProperty('isPrototypeOf')\n}\n"], "names": ["getObjectClassLabel", "isPlainObject", "value", "Object", "prototype", "toString", "call", "getPrototypeOf", "hasOwnProperty"], "mappings": ";;;;;;;;;;;;;;IAAgBA,mBAAmB,EAAA;eAAnBA;;IAIAC,aAAa,EAAA;eAAbA;;;AAJT,SAASD,oBAAoBE,KAAU;IAC5C,OAAOC,OAAOC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ;AACxC;AAEO,SAASD,cAAcC,KAAU;IACtC,IAAIF,oBAAoBE,WAAW,mBAAmB;QACpD,OAAO;IACT;IAEA,MAAME,YAAYD,OAAOI,cAAc,CAACL;IAExC;;;;;;;;GAQC,GACD,OAAOE,cAAc,QAAQA,UAAUI,cAAc,CAAC;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/sorted-routes.ts"], "sourcesContent": ["class UrlNode {\n  placeholder: boolean = true\n  children: Map<string, UrlNode> = new Map()\n  slugName: string | null = null\n  restSlugName: string | null = null\n  optionalRestSlugName: string | null = null\n\n  insert(urlPath: string): void {\n    this._insert(urlPath.split('/').filter(Boolean), [], false)\n  }\n\n  smoosh(): string[] {\n    return this._smoosh()\n  }\n\n  private _smoosh(prefix: string = '/'): string[] {\n    const childrenPaths = [...this.children.keys()].sort()\n    if (this.slugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[]'), 1)\n    }\n    if (this.restSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[...]'), 1)\n    }\n    if (this.optionalRestSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[[...]]'), 1)\n    }\n\n    const routes = childrenPaths\n      .map((c) => this.children.get(c)!._smoosh(`${prefix}${c}/`))\n      .reduce((prev, curr) => [...prev, ...curr], [])\n\n    if (this.slugName !== null) {\n      routes.push(\n        ...this.children.get('[]')!._smoosh(`${prefix}[${this.slugName}]/`)\n      )\n    }\n\n    if (!this.placeholder) {\n      const r = prefix === '/' ? '/' : prefix.slice(0, -1)\n      if (this.optionalRestSlugName != null) {\n        throw new Error(\n          `You cannot define a route with the same specificity as a optional catch-all route (\"${r}\" and \"${r}[[...${this.optionalRestSlugName}]]\").`\n        )\n      }\n\n      routes.unshift(r)\n    }\n\n    if (this.restSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[...]')!\n          ._smoosh(`${prefix}[...${this.restSlugName}]/`)\n      )\n    }\n\n    if (this.optionalRestSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[[...]]')!\n          ._smoosh(`${prefix}[[...${this.optionalRestSlugName}]]/`)\n      )\n    }\n\n    return routes\n  }\n\n  private _insert(\n    urlPaths: string[],\n    slugNames: string[],\n    isCatchAll: boolean\n  ): void {\n    if (urlPaths.length === 0) {\n      this.placeholder = false\n      return\n    }\n\n    if (isCatchAll) {\n      throw new Error(`Catch-all must be the last part of the URL.`)\n    }\n\n    // The next segment in the urlPaths list\n    let nextSegment = urlPaths[0]\n\n    // Check if the segment matches `[something]`\n    if (nextSegment.startsWith('[') && nextSegment.endsWith(']')) {\n      // Strip `[` and `]`, leaving only `something`\n      let segmentName = nextSegment.slice(1, -1)\n\n      let isOptional = false\n      if (segmentName.startsWith('[') && segmentName.endsWith(']')) {\n        // Strip optional `[` and `]`, leaving only `something`\n        segmentName = segmentName.slice(1, -1)\n        isOptional = true\n      }\n\n      if (segmentName.startsWith('…')) {\n        throw new Error(\n          `Detected a three-dot character ('…') at ('${segmentName}'). Did you mean ('...')?`\n        )\n      }\n\n      if (segmentName.startsWith('...')) {\n        // Strip `...`, leaving only `something`\n        segmentName = segmentName.substring(3)\n        isCatchAll = true\n      }\n\n      if (segmentName.startsWith('[') || segmentName.endsWith(']')) {\n        throw new Error(\n          `Segment names may not start or end with extra brackets ('${segmentName}').`\n        )\n      }\n\n      if (segmentName.startsWith('.')) {\n        throw new Error(\n          `Segment names may not start with erroneous periods ('${segmentName}').`\n        )\n      }\n\n      function handleSlug(previousSlug: string | null, nextSlug: string) {\n        if (previousSlug !== null) {\n          // If the specific segment already has a slug but the slug is not `something`\n          // This prevents collisions like:\n          // pages/[post]/index.js\n          // pages/[id]/index.js\n          // Because currently multiple dynamic params on the same segment level are not supported\n          if (previousSlug !== nextSlug) {\n            // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n            throw new Error(\n              `You cannot use different slug names for the same dynamic path ('${previousSlug}' !== '${nextSlug}').`\n            )\n          }\n        }\n\n        slugNames.forEach((slug) => {\n          if (slug === nextSlug) {\n            throw new Error(\n              `You cannot have the same slug name \"${nextSlug}\" repeat within a single dynamic path`\n            )\n          }\n\n          if (slug.replace(/\\W/g, '') === nextSegment.replace(/\\W/g, '')) {\n            throw new Error(\n              `You cannot have the slug names \"${slug}\" and \"${nextSlug}\" differ only by non-word symbols within a single dynamic path`\n            )\n          }\n        })\n\n        slugNames.push(nextSlug)\n      }\n\n      if (isCatchAll) {\n        if (isOptional) {\n          if (this.restSlugName != null) {\n            throw new Error(\n              `You cannot use both an required and optional catch-all route at the same level (\"[...${this.restSlugName}]\" and \"${urlPaths[0]}\" ).`\n            )\n          }\n\n          handleSlug(this.optionalRestSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.optionalRestSlugName = segmentName\n          // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n          nextSegment = '[[...]]'\n        } else {\n          if (this.optionalRestSlugName != null) {\n            throw new Error(\n              `You cannot use both an optional and required catch-all route at the same level (\"[[...${this.optionalRestSlugName}]]\" and \"${urlPaths[0]}\").`\n            )\n          }\n\n          handleSlug(this.restSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.restSlugName = segmentName\n          // nextSegment is overwritten to [...] so that it can later be sorted specifically\n          nextSegment = '[...]'\n        }\n      } else {\n        if (isOptional) {\n          throw new Error(\n            `Optional route parameters are not yet supported (\"${urlPaths[0]}\").`\n          )\n        }\n        handleSlug(this.slugName, segmentName)\n        // slugName is kept as it can only be one particular slugName\n        this.slugName = segmentName\n        // nextSegment is overwritten to [] so that it can later be sorted specifically\n        nextSegment = '[]'\n      }\n    }\n\n    // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n    if (!this.children.has(nextSegment)) {\n      this.children.set(nextSegment, new UrlNode())\n    }\n\n    this.children\n      .get(nextSegment)!\n      ._insert(urlPaths.slice(1), slugNames, isCatchAll)\n  }\n}\n\n/**\n * @deprecated Use `sortSortableRoutes` or `sortPages` instead.\n */\nexport function getSortedRoutes(\n  normalizedPages: ReadonlyArray<string>\n): string[] {\n  // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n  // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n  // Only 1 dynamic segment per nesting level\n\n  // So in the case that is test/integration/dynamic-routing it'll be this:\n  // pages/[post]/comments.js\n  // pages/blog/[post]/comment/[id].js\n  // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n  // So in this case `UrlNode` created here has `this.slugName === 'post'`\n  // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n  // Instead what has to be passed through is the upwards path's dynamic names\n  const root = new UrlNode()\n\n  // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n  normalizedPages.forEach((pagePath) => root.insert(pagePath))\n  // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n  return root.smoosh()\n}\n\n/**\n * @deprecated Use `sortSortableRouteObjects` or `sortPageObjects` instead.\n */\nexport function getSortedRouteObjects<T>(\n  objects: T[],\n  getter: (obj: T) => string\n): T[] {\n  // We're assuming here that all the pathnames are unique, that way we can\n  // sort the list and use the index as the key.\n  const indexes: Record<string, number> = {}\n  const pathnames: string[] = []\n  for (let i = 0; i < objects.length; i++) {\n    const pathname = getter(objects[i])\n    indexes[pathname] = i\n    pathnames[i] = pathname\n  }\n\n  // Sort the pathnames.\n  const sorted = getSortedRoutes(pathnames)\n\n  // Map the sorted pathnames back to the original objects using the new sorted\n  // index.\n  return sorted.map((pathname) => objects[indexes[pathname]])\n}\n"], "names": ["getSortedRouteObjects", "getSortedRoutes", "UrlNode", "insert", "url<PERSON><PERSON>", "_insert", "split", "filter", "Boolean", "smoosh", "_smoosh", "prefix", "childrenPaths", "children", "keys", "sort", "slug<PERSON><PERSON>", "splice", "indexOf", "restSlugName", "optionalRestSlugName", "routes", "map", "c", "get", "reduce", "prev", "curr", "push", "placeholder", "r", "slice", "Error", "unshift", "url<PERSON><PERSON>s", "slug<PERSON><PERSON><PERSON>", "isCatchAll", "length", "nextSegment", "startsWith", "endsWith", "segmentName", "isOptional", "substring", "handleSlug", "previousSlug", "nextSlug", "for<PERSON>ach", "slug", "replace", "has", "set", "Map", "normalizedPages", "root", "pagePath", "objects", "getter", "indexes", "pathnames", "i", "pathname", "sorted"], "mappings": ";;;;;;;;;;;;;;IAuOgBA,qBAAqB,EAAA;eAArBA;;IAzBAC,eAAe,EAAA;eAAfA;;;AA9MhB,MAAMC;IAOJC,OAAOC,OAAe,EAAQ;QAC5B,IAAI,CAACC,OAAO,CAACD,QAAQE,KAAK,CAAC,KAAKC,MAAM,CAACC,UAAU,EAAE,EAAE;IACvD;IAEAC,SAAmB;QACjB,OAAO,IAAI,CAACC,OAAO;IACrB;IAEQA,QAAQC,MAAoB,EAAY;QAAhCA,IAAAA,WAAAA,KAAAA,GAAAA,SAAiB;QAC/B,MAAMC,gBAAgB;eAAI,IAAI,CAACC,QAAQ,CAACC,IAAI;SAAG,CAACC,IAAI;QACpD,IAAI,IAAI,CAACC,QAAQ,KAAK,MAAM;YAC1BJ,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,OAAO;QACpD;QACA,IAAI,IAAI,CAACC,YAAY,KAAK,MAAM;YAC9BP,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,UAAU;QACvD;QACA,IAAI,IAAI,CAACE,oBAAoB,KAAK,MAAM;YACtCR,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,YAAY;QACzD;QAEA,MAAMG,SAAST,cACZU,GAAG,CAAC,CAACC,IAAM,IAAI,CAACV,QAAQ,CAACW,GAAG,CAACD,GAAIb,OAAO,CAAE,KAAEC,SAASY,IAAE,MACvDE,MAAM,CAAC,CAACC,MAAMC,OAAS;mBAAID;mBAASC;aAAK,EAAE,EAAE;QAEhD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1BK,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CAACW,GAAG,CAAC,MAAOd,OAAO,CAAIC,SAAO,MAAG,IAAI,CAACK,QAAQ,GAAC;QAEnE;QAEA,IAAI,CAAC,IAAI,CAACa,WAAW,EAAE;YACrB,MAAMC,IAAInB,WAAW,MAAM,MAAMA,OAAOoB,KAAK,CAAC,GAAG,CAAC;YAClD,IAAI,IAAI,CAACX,oBAAoB,IAAI,MAAM;gBACrC,MAAM,OAAA,cAEL,CAFK,IAAIY,MACP,yFAAsFF,IAAE,YAASA,IAAE,UAAO,IAAI,CAACV,oBAAoB,GAAC,UADjI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAC,OAAOY,OAAO,CAACH;QACjB;QAEA,IAAI,IAAI,CAACX,YAAY,KAAK,MAAM;YAC9BE,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CACbW,GAAG,CAAC,SACJd,OAAO,CAAIC,SAAO,SAAM,IAAI,CAACQ,YAAY,GAAC;QAEjD;QAEA,IAAI,IAAI,CAACC,oBAAoB,KAAK,MAAM;YACtCC,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CACbW,GAAG,CAAC,WACJd,OAAO,CAAIC,SAAO,UAAO,IAAI,CAACS,oBAAoB,GAAC;QAE1D;QAEA,OAAOC;IACT;IAEQhB,QACN6B,QAAkB,EAClBC,SAAmB,EACnBC,UAAmB,EACb;QACN,IAAIF,SAASG,MAAM,KAAK,GAAG;YACzB,IAAI,CAACR,WAAW,GAAG;YACnB;QACF;QAEA,IAAIO,YAAY;YACd,MAAM,OAAA,cAAwD,CAAxD,IAAIJ,MAAO,gDAAX,qBAAA;uBAAA;4BAAA;8BAAA;YAAuD;QAC/D;QAEA,wCAAwC;QACxC,IAAIM,cAAcJ,QAAQ,CAAC,EAAE;QAE7B,6CAA6C;QAC7C,IAAII,YAAYC,UAAU,CAAC,QAAQD,YAAYE,QAAQ,CAAC,MAAM;YAC5D,8CAA8C;YAC9C,IAAIC,cAAcH,YAAYP,KAAK,CAAC,GAAG,CAAC;YAExC,IAAIW,aAAa;YACjB,IAAID,YAAYF,UAAU,CAAC,QAAQE,YAAYD,QAAQ,CAAC,MAAM;gBAC5D,uDAAuD;gBACvDC,cAAcA,YAAYV,KAAK,CAAC,GAAG,CAAC;gBACpCW,aAAa;YACf;YAEA,IAAID,YAAYF,UAAU,CAAC,MAAM;gBAC/B,MAAM,OAAA,cAEL,CAFK,IAAIP,MACP,+CAA4CS,cAAY,8BADrD,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIA,YAAYF,UAAU,CAAC,QAAQ;gBACjC,wCAAwC;gBACxCE,cAAcA,YAAYE,SAAS,CAAC;gBACpCP,aAAa;YACf;YAEA,IAAIK,YAAYF,UAAU,CAAC,QAAQE,YAAYD,QAAQ,CAAC,MAAM;gBAC5D,MAAM,OAAA,cAEL,CAFK,IAAIR,MACP,8DAA2DS,cAAY,QADpE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIA,YAAYF,UAAU,CAAC,MAAM;gBAC/B,MAAM,OAAA,cAEL,CAFK,IAAIP,MACP,0DAAuDS,cAAY,QADhE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,SAASG,WAAWC,YAA2B,EAAEC,QAAgB;gBAC/D,IAAID,iBAAiB,MAAM;oBACzB,6EAA6E;oBAC7E,iCAAiC;oBACjC,wBAAwB;oBACxB,sBAAsB;oBACtB,wFAAwF;oBACxF,IAAIA,iBAAiBC,UAAU;wBAC7B,wHAAwH;wBACxH,MAAM,OAAA,cAEL,CAFK,IAAId,MACP,qEAAkEa,eAAa,YAASC,WAAS,QAD9F,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAX,UAAUY,OAAO,CAAC,CAACC;oBACjB,IAAIA,SAASF,UAAU;wBACrB,MAAM,OAAA,cAEL,CAFK,IAAId,MACP,yCAAsCc,WAAS,0CAD5C,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,IAAIE,KAAKC,OAAO,CAAC,OAAO,QAAQX,YAAYW,OAAO,CAAC,OAAO,KAAK;wBAC9D,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,qCAAkCgB,OAAK,YAASF,WAAS,mEADtD,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAX,UAAUP,IAAI,CAACkB;YACjB;YAEA,IAAIV,YAAY;gBACd,IAAIM,YAAY;oBACd,IAAI,IAAI,CAACvB,YAAY,IAAI,MAAM;wBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIa,MACP,0FAAuF,IAAI,CAACb,YAAY,GAAC,aAAUe,QAAQ,CAAC,EAAE,GAAC,SAD5H,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAU,WAAW,IAAI,CAACxB,oBAAoB,EAAEqB;oBACtC,6DAA6D;oBAC7D,IAAI,CAACrB,oBAAoB,GAAGqB;oBAC5B,oFAAoF;oBACpFH,cAAc;gBAChB,OAAO;oBACL,IAAI,IAAI,CAAClB,oBAAoB,IAAI,MAAM;wBACrC,MAAM,OAAA,cAEL,CAFK,IAAIY,MACP,2FAAwF,IAAI,CAACZ,oBAAoB,GAAC,cAAWc,QAAQ,CAAC,EAAE,GAAC,QADtI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAU,WAAW,IAAI,CAACzB,YAAY,EAAEsB;oBAC9B,6DAA6D;oBAC7D,IAAI,CAACtB,YAAY,GAAGsB;oBACpB,kFAAkF;oBAClFH,cAAc;gBAChB;YACF,OAAO;gBACL,IAAII,YAAY;oBACd,MAAM,OAAA,cAEL,CAFK,IAAIV,MACP,uDAAoDE,QAAQ,CAAC,EAAE,GAAC,QAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACAU,WAAW,IAAI,CAAC5B,QAAQ,EAAEyB;gBAC1B,6DAA6D;gBAC7D,IAAI,CAACzB,QAAQ,GAAGyB;gBAChB,+EAA+E;gBAC/EH,cAAc;YAChB;QACF;QAEA,iFAAiF;QACjF,IAAI,CAAC,IAAI,CAACzB,QAAQ,CAACqC,GAAG,CAACZ,cAAc;YACnC,IAAI,CAACzB,QAAQ,CAACsC,GAAG,CAACb,aAAa,IAAIpC;QACrC;QAEA,IAAI,CAACW,QAAQ,CACVW,GAAG,CAACc,aACJjC,OAAO,CAAC6B,SAASH,KAAK,CAAC,IAAII,WAAWC;IAC3C;;aAvMAP,WAAAA,GAAuB;aACvBhB,QAAAA,GAAiC,IAAIuC;aACrCpC,QAAAA,GAA0B;aAC1BG,YAAAA,GAA8B;aAC9BC,oBAAAA,GAAsC;;AAoMxC;AAKO,SAASnB,gBACdoD,eAAsC;IAEtC,kFAAkF;IAClF,4EAA4E;IAC5E,2CAA2C;IAE3C,yEAAyE;IACzE,2BAA2B;IAC3B,oCAAoC;IACpC,8EAA8E;IAC9E,wEAAwE;IACxE,gHAAgH;IAChH,4EAA4E;IAC5E,MAAMC,OAAO,IAAIpD;IAEjB,6FAA6F;IAC7FmD,gBAAgBN,OAAO,CAAC,CAACQ,WAAaD,KAAKnD,MAAM,CAACoD;IAClD,4GAA4G;IAC5G,OAAOD,KAAK7C,MAAM;AACpB;AAKO,SAAST,sBACdwD,OAAY,EACZC,MAA0B;IAE1B,yEAAyE;IACzE,8CAA8C;IAC9C,MAAMC,UAAkC,CAAC;IACzC,MAAMC,YAAsB,EAAE;IAC9B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,QAAQnB,MAAM,EAAEuB,IAAK;QACvC,MAAMC,WAAWJ,OAAOD,OAAO,CAACI,EAAE;QAClCF,OAAO,CAACG,SAAS,GAAGD;QACpBD,SAAS,CAACC,EAAE,GAAGC;IACjB;IAEA,sBAAsB;IACtB,MAAMC,SAAS7D,gBAAgB0D;IAE/B,6EAA6E;IAC7E,SAAS;IACT,OAAOG,OAAOxC,GAAG,CAAC,CAACuC,WAAaL,OAAO,CAACE,OAAO,CAACG,SAAS,CAAC;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/index.ts"], "sourcesContent": ["export { getSortedRoutes, getSortedRouteObjects } from './sorted-routes'\nexport { isDynamicRoute } from './is-dynamic'\n"], "names": ["getSortedRouteObjects", "getSortedRoutes", "isDynamicRoute"], "mappings": ";;;;;;;;;;;;;;;IAA0BA,qBAAqB,EAAA;eAArBA,cAAAA,qBAAqB;;IAAtCC,eAAe,EAAA;eAAfA,cAAAA,eAAe;;IACfC,cAAc,EAAA;eAAdA,WAAAA,cAAc;;;8BADgC;2BACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/page-path/normalize-path-sep.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is no backslash\n * escaping slashes in the path. Example:\n *  - `foo\\/bar\\/baz` -> `foo/bar/baz`\n */\nexport function normalizePathSep(path: string): string {\n  return path.replace(/\\\\/g, '/')\n}\n"], "names": ["normalizePathSep", "path", "replace"], "mappings": "AAAA;;;;CAIC;;;+BACeA,oBAAAA;;;eAAAA;;;AAAT,SAASA,iBAAiBC,IAAY;IAC3C,OAAOA,KAAKC,OAAO,CAAC,OAAO;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/page-path/denormalize-page-path.ts"], "sourcesContent": ["import { isDynamicRoute } from '../router/utils'\nimport { normalizePathSep } from './normalize-path-sep'\n\n/**\n * Performs the opposite transformation of `normalizePagePath`. Note that\n * this function is not idempotent either in cases where there are multiple\n * leading `/index` for the page. Examples:\n *  - `/index` -> `/`\n *  - `/index/foo` -> `/foo`\n *  - `/index/index` -> `/index`\n */\nexport function denormalizePagePath(page: string) {\n  let _page = normalizePathSep(page)\n  return _page.startsWith('/index/') && !isDynamicRoute(_page)\n    ? _page.slice(6)\n    : _page !== '/index'\n      ? _page\n      : '/'\n}\n"], "names": ["denormalizePagePath", "page", "_page", "normalizePathSep", "startsWith", "isDynamicRoute", "slice"], "mappings": ";;;+BAWgBA,uBAAAA;;;eAAAA;;;uBAXe;kCACE;AAU1B,SAASA,oBAAoBC,IAAY;IAC9C,IAAIC,QAAQC,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACF;IAC7B,OAAOC,MAAME,UAAU,CAAC,cAAc,CAACC,CAAAA,GAAAA,OAAAA,cAAc,EAACH,SAClDA,MAAMI,KAAK,CAAC,KACZJ,UAAU,WACRA,QACA;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/i18n/normalize-locale-path.ts"], "sourcesContent": ["export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * A cache of lowercased locales for each list of locales. This is stored as a\n * WeakMap so if the locales are garbage collected, the cache entry will be\n * removed as well.\n */\nconst cache = new WeakMap<readonly string[], readonly string[]>()\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: readonly string[]\n): PathLocale {\n  // If locales is undefined, return the pathname as is.\n  if (!locales) return { pathname }\n\n  // Get the cached lowercased locales or create a new cache entry.\n  let lowercasedLocales = cache.get(locales)\n  if (!lowercasedLocales) {\n    lowercasedLocales = locales.map((locale) => locale.toLowerCase())\n    cache.set(locales, lowercasedLocales)\n  }\n\n  let detectedLocale: string | undefined\n\n  // The first segment will be empty, because it has a leading `/`. If\n  // there is no further segment, there is no locale (or it's the default).\n  const segments = pathname.split('/', 2)\n\n  // If there's no second segment (ie, the pathname is just `/`), there's no\n  // locale.\n  if (!segments[1]) return { pathname }\n\n  // The second segment will contain the locale part if any.\n  const segment = segments[1].toLowerCase()\n\n  // See if the segment matches one of the locales. If it doesn't, there is\n  // no locale (or it's the default).\n  const index = lowercasedLocales.indexOf(segment)\n  if (index < 0) return { pathname }\n\n  // Return the case-sensitive locale.\n  detectedLocale = locales[index]\n\n  // Remove the `/${locale}` part of the pathname.\n  pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n  return { pathname, detectedLocale }\n}\n"], "names": ["normalizeLocalePath", "cache", "WeakMap", "pathname", "locales", "lowercasedLocales", "get", "map", "locale", "toLowerCase", "set", "detectedLocale", "segments", "split", "segment", "index", "indexOf", "slice", "length"], "mappings": ";;;+BAqBgBA,uBAAAA;;;eAAAA;;;AAhBhB;;;;CAIC,GACD,MAAMC,QAAQ,IAAIC;AAWX,SAASF,oBACdG,QAAgB,EAChBC,OAA2B;IAE3B,sDAAsD;IACtD,IAAI,CAACA,SAAS,OAAO;QAAED;IAAS;IAEhC,iEAAiE;IACjE,IAAIE,oBAAoBJ,MAAMK,GAAG,CAACF;IAClC,IAAI,CAACC,mBAAmB;QACtBA,oBAAoBD,QAAQG,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;QAC9DR,MAAMS,GAAG,CAACN,SAASC;IACrB;IAEA,IAAIM;IAEJ,oEAAoE;IACpE,yEAAyE;IACzE,MAAMC,WAAWT,SAASU,KAAK,CAAC,KAAK;IAErC,0EAA0E;IAC1E,UAAU;IACV,IAAI,CAACD,QAAQ,CAAC,EAAE,EAAE,OAAO;QAAET;IAAS;IAEpC,0DAA0D;IAC1D,MAAMW,UAAUF,QAAQ,CAAC,EAAE,CAACH,WAAW;IAEvC,yEAAyE;IACzE,mCAAmC;IACnC,MAAMM,QAAQV,kBAAkBW,OAAO,CAACF;IACxC,IAAIC,QAAQ,GAAG,OAAO;QAAEZ;IAAS;IAEjC,oCAAoC;IACpCQ,iBAAiBP,OAAO,CAACW,MAAM;IAE/B,gDAAgD;IAChDZ,WAAWA,SAASc,KAAK,CAACN,eAAeO,MAAM,GAAG,MAAM;IAExD,OAAO;QAAEf;QAAUQ;IAAe;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAsEnCwB,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;IA9Df1B,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,wCAA4C;QAC1C,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/path-has-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n"], "names": ["pathHasPrefix", "path", "prefix", "pathname", "parsePath", "startsWith"], "mappings": ";;;+BASgBA,iBAAAA;;;eAAAA;;;2BATU;AASnB,SAASA,cAAcC,IAAY,EAAEC,MAAc;IACxD,IAAI,OAAOD,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,MAAM,EAAEE,QAAQ,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACH;IAC/B,OAAOE,aAAaD,UAAUC,SAASE,UAAU,CAACH,SAAS;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/omit.ts"], "sourcesContent": ["export function omit<T extends { [key: string]: unknown }, K extends keyof T>(\n  object: T,\n  keys: K[]\n): Omit<T, K> {\n  const omitted: { [key: string]: unknown } = {}\n  Object.keys(object).forEach((key) => {\n    if (!keys.includes(key as K)) {\n      omitted[key] = object[key]\n    }\n  })\n  return omitted as Omit<T, K>\n}\n"], "names": ["omit", "object", "keys", "omitted", "Object", "for<PERSON>ach", "key", "includes"], "mappings": ";;;+BAAgBA,QAAAA;;;eAAAA;;;AAAT,SAASA,KACdC,MAAS,EACTC,IAAS;IAET,MAAMC,UAAsC,CAAC;IAC7CC,OAAOF,IAAI,CAACD,QAAQI,OAAO,CAAC,CAACC;QAC3B,IAAI,CAACJ,KAAKK,QAAQ,CAACD,MAAW;YAC5BH,OAAO,CAACG,IAAI,GAAGL,MAAM,CAACK,IAAI;QAC5B;IACF;IACA,OAAOH;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2594, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/remove-path-prefix.ts"], "sourcesContent": ["import { pathHasPrefix } from './path-has-prefix'\n\n/**\n * Given a path and a prefix it will remove the prefix when it exists in the\n * given path. It ensures it matches exactly without containing extra chars\n * and if the prefix is not there it will be noop.\n *\n * @param path The path to remove the prefix from.\n * @param prefix The prefix to be removed.\n */\nexport function removePathPrefix(path: string, prefix: string): string {\n  // If the path doesn't start with the prefix we can return it as is. This\n  // protects us from situations where the prefix is a substring of the path\n  // prefix such as:\n  //\n  // For prefix: /blog\n  //\n  //   /blog -> true\n  //   /blog/ -> true\n  //   /blog/1 -> true\n  //   /blogging -> false\n  //   /blogging/ -> false\n  //   /blogging/1 -> false\n  if (!pathHasPrefix(path, prefix)) {\n    return path\n  }\n\n  // Remove the prefix from the path via slicing.\n  const withoutPrefix = path.slice(prefix.length)\n\n  // If the path without the prefix starts with a `/` we can return it as is.\n  if (withoutPrefix.startsWith('/')) {\n    return withoutPrefix\n  }\n\n  // If the path without the prefix doesn't start with a `/` we need to add it\n  // back to the path to make sure it's a valid path.\n  return `/${withoutPrefix}`\n}\n"], "names": ["removePathPrefix", "path", "prefix", "pathHasPrefix", "withoutPrefix", "slice", "length", "startsWith"], "mappings": ";;;+BAUgBA,oBAAAA;;;eAAAA;;;+BAVc;AAUvB,SAASA,iBAAiBC,IAAY,EAAEC,MAAc;IAC3D,yEAAyE;IACzE,0EAA0E;IAC1E,kBAAkB;IAClB,EAAE;IACF,oBAAoB;IACpB,EAAE;IACF,kBAAkB;IAClB,mBAAmB;IACnB,oBAAoB;IACpB,uBAAuB;IACvB,wBAAwB;IACxB,yBAAyB;IACzB,IAAI,CAACC,CAAAA,GAAAA,eAAAA,aAAa,EAACF,MAAMC,SAAS;QAChC,OAAOD;IACT;IAEA,+CAA+C;IAC/C,MAAMG,gBAAgBH,KAAKI,KAAK,CAACH,OAAOI,MAAM;IAE9C,2EAA2E;IAC3E,IAAIF,cAAcG,UAAU,CAAC,MAAM;QACjC,OAAOH;IACT;IAEA,4EAA4E;IAC5E,mDAAmD;IACnD,OAAQ,MAAGA;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2661, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/get-next-pathname-info.ts"], "sourcesContent": ["import { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removePathPrefix } from './remove-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\nimport type { I18NProvider } from '../../../../server/lib/i18n-provider'\n\nexport interface NextPathnameInfo {\n  /**\n   * The base path in case the pathname included it.\n   */\n  basePath?: string\n  /**\n   * The buildId for when the parsed URL is a data URL. Parsing it can be\n   * disabled with the `parseData` option.\n   */\n  buildId?: string\n  /**\n   * If there was a locale in the pathname, this will hold its value.\n   */\n  locale?: string\n  /**\n   * The processed pathname without a base path, locale, or data URL elements\n   * when parsing it is enabled.\n   */\n  pathname: string\n  /**\n   * A boolean telling if the pathname had a trailingSlash. This can be only\n   * true if trailingSlash is enabled.\n   */\n  trailingSlash?: boolean\n}\n\ninterface Options {\n  /**\n   * When passed to true, this function will also parse Nextjs data URLs.\n   */\n  parseData?: boolean\n  /**\n   * A partial of the Next.js configuration to parse the URL.\n   */\n  nextConfig?: {\n    basePath?: string\n    i18n?: { locales?: readonly string[] } | null\n    trailingSlash?: boolean\n  }\n\n  /**\n   * If provided, this normalizer will be used to detect the locale instead of\n   * the default locale detection.\n   */\n  i18nProvider?: I18NProvider\n}\n\nexport function getNextPathnameInfo(\n  pathname: string,\n  options: Options\n): NextPathnameInfo {\n  const { basePath, i18n, trailingSlash } = options.nextConfig ?? {}\n  const info: NextPathnameInfo = {\n    pathname,\n    trailingSlash: pathname !== '/' ? pathname.endsWith('/') : trailingSlash,\n  }\n\n  if (basePath && pathHasPrefix(info.pathname, basePath)) {\n    info.pathname = removePathPrefix(info.pathname, basePath)\n    info.basePath = basePath\n  }\n  let pathnameNoDataPrefix = info.pathname\n\n  if (\n    info.pathname.startsWith('/_next/data/') &&\n    info.pathname.endsWith('.json')\n  ) {\n    const paths = info.pathname\n      .replace(/^\\/_next\\/data\\//, '')\n      .replace(/\\.json$/, '')\n      .split('/')\n\n    const buildId = paths[0]\n    info.buildId = buildId\n    pathnameNoDataPrefix =\n      paths[1] !== 'index' ? `/${paths.slice(1).join('/')}` : '/'\n\n    // update pathname with normalized if enabled although\n    // we use normalized to populate locale info still\n    if (options.parseData === true) {\n      info.pathname = pathnameNoDataPrefix\n    }\n  }\n\n  // If provided, use the locale route normalizer to detect the locale instead\n  // of the function below.\n  if (i18n) {\n    let result = options.i18nProvider\n      ? options.i18nProvider.analyze(info.pathname)\n      : normalizeLocalePath(info.pathname, i18n.locales)\n\n    info.locale = result.detectedLocale\n    info.pathname = result.pathname ?? info.pathname\n\n    if (!result.detectedLocale && info.buildId) {\n      result = options.i18nProvider\n        ? options.i18nProvider.analyze(pathnameNoDataPrefix)\n        : normalizeLocalePath(pathnameNoDataPrefix, i18n.locales)\n\n      if (result.detectedLocale) {\n        info.locale = result.detectedLocale\n      }\n    }\n  }\n  return info\n}\n"], "names": ["getNextPathnameInfo", "pathname", "options", "basePath", "i18n", "trailingSlash", "nextConfig", "info", "endsWith", "pathHasPrefix", "removePathPrefix", "pathnameNoDataPrefix", "startsWith", "paths", "replace", "split", "buildId", "slice", "join", "parseData", "result", "i18nProvider", "analyze", "normalizeLocalePath", "locales", "locale", "detectedLocale"], "mappings": ";;;+BAoDgBA,uBAAAA;;;eAAAA;;;qCApDoB;kCACH;+BACH;AAkDvB,SAASA,oBACdC,QAAgB,EAChBC,OAAgB;QAE0BA;IAA1C,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,EAAE,GAAGH,CAAAA,sBAAAA,QAAQI,UAAU,KAAA,OAAlBJ,sBAAsB,CAAC;IACjE,MAAMK,OAAyB;QAC7BN;QACAI,eAAeJ,aAAa,MAAMA,SAASO,QAAQ,CAAC,OAAOH;IAC7D;IAEA,IAAIF,YAAYM,CAAAA,GAAAA,eAAAA,aAAa,EAACF,KAAKN,QAAQ,EAAEE,WAAW;QACtDI,KAAKN,QAAQ,GAAGS,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACH,KAAKN,QAAQ,EAAEE;QAChDI,KAAKJ,QAAQ,GAAGA;IAClB;IACA,IAAIQ,uBAAuBJ,KAAKN,QAAQ;IAExC,IACEM,KAAKN,QAAQ,CAACW,UAAU,CAAC,mBACzBL,KAAKN,QAAQ,CAACO,QAAQ,CAAC,UACvB;QACA,MAAMK,QAAQN,KAAKN,QAAQ,CACxBa,OAAO,CAAC,oBAAoB,IAC5BA,OAAO,CAAC,WAAW,IACnBC,KAAK,CAAC;QAET,MAAMC,UAAUH,KAAK,CAAC,EAAE;QACxBN,KAAKS,OAAO,GAAGA;QACfL,uBACEE,KAAK,CAAC,EAAE,KAAK,UAAW,MAAGA,MAAMI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS;QAE1D,sDAAsD;QACtD,kDAAkD;QAClD,IAAIhB,QAAQiB,SAAS,KAAK,MAAM;YAC9BZ,KAAKN,QAAQ,GAAGU;QAClB;IACF;IAEA,4EAA4E;IAC5E,yBAAyB;IACzB,IAAIP,MAAM;QACR,IAAIgB,SAASlB,QAAQmB,YAAY,GAC7BnB,QAAQmB,YAAY,CAACC,OAAO,CAACf,KAAKN,QAAQ,IAC1CsB,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAChB,KAAKN,QAAQ,EAAEG,KAAKoB,OAAO;QAEnDjB,KAAKkB,MAAM,GAAGL,OAAOM,cAAc;YACnBN;QAAhBb,KAAKN,QAAQ,GAAGmB,CAAAA,mBAAAA,OAAOnB,QAAQ,KAAA,OAAfmB,mBAAmBb,KAAKN,QAAQ;QAEhD,IAAI,CAACmB,OAAOM,cAAc,IAAInB,KAAKS,OAAO,EAAE;YAC1CI,SAASlB,QAAQmB,YAAY,GACzBnB,QAAQmB,YAAY,CAACC,OAAO,CAACX,wBAC7BY,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACZ,sBAAsBP,KAAKoB,OAAO;YAE1D,IAAIJ,OAAOM,cAAc,EAAE;gBACzBnB,KAAKkB,MAAM,GAAGL,OAAOM,cAAc;YACrC;QACF;IACF;IACA,OAAOnB;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2716, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/add-path-suffix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Similarly to `addPathPrefix`, this function adds a suffix at the end on the\n * provided path. It also works only for paths ensuring the argument starts\n * with a slash.\n */\nexport function addPathSuffix(path: string, suffix?: string) {\n  if (!path.startsWith('/') || !suffix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${pathname}${suffix}${query}${hash}`\n}\n"], "names": ["addPathSuffix", "path", "suffix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;+BAOgBA,iBAAAA;;;eAAAA;;;2BAPU;AAOnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACN;IAC5C,OAAQ,KAAEG,WAAWF,SAASG,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/add-locale.ts"], "sourcesContent": ["import { addPathPrefix } from './add-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\n\n/**\n * For a given path and a locale, if the locale is given, it will prefix the\n * locale. The path shouldn't be an API path. If a default locale is given the\n * prefix will be omitted if the locale is already the default locale.\n */\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string,\n  ignorePrefix?: boolean\n) {\n  // If no locale was given or the locale is the default locale, we don't need\n  // to prefix the path.\n  if (!locale || locale === defaultLocale) return path\n\n  const lower = path.toLowerCase()\n\n  // If the path is an API path or the path already has the locale prefix, we\n  // don't need to prefix the path.\n  if (!ignorePrefix) {\n    if (pathHasPrefix(lower, '/api')) return path\n    if (pathHasPrefix(lower, `/${locale.toLowerCase()}`)) return path\n  }\n\n  // Add the locale prefix to the path.\n  return addPathPrefix(path, `/${locale}`)\n}\n"], "names": ["addLocale", "path", "locale", "defaultLocale", "ignorePrefix", "lower", "toLowerCase", "pathHasPrefix", "addPathPrefix"], "mappings": ";;;+BAQg<PERSON>,aAAAA;;;eAAAA;;;+BARc;+BACA;AAOvB,SAASA,UACdC,IAAY,EACZC,MAAuB,EACvBC,aAAsB,EACtBC,YAAsB;IAEtB,4EAA4E;IAC5E,sBAAsB;IACtB,IAAI,CAACF,UAAUA,WAAWC,eAAe,OAAOF;IAEhD,MAAMI,QAAQJ,KAAKK,WAAW;IAE9B,2EAA2E;IAC3E,iCAAiC;IACjC,IAAI,CAACF,cAAc;QACjB,IAAIG,CAAAA,GAAAA,eAAAA,aAAa,EAACF,OAAO,SAAS,OAAOJ;QACzC,IAAIM,CAAAA,GAAAA,eAAAA,aAAa,EAACF,OAAQ,MAAGH,OAAOI,WAAW,KAAO,OAAOL;IAC/D;IAEA,qCAAqC;IACrC,OAAOO,CAAAA,GAAAA,eAAAA,aAAa,EAACP,MAAO,MAAGC;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/format-next-pathname-info.ts"], "sourcesContent": ["import type { NextPathnameInfo } from './get-next-pathname-info'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { addPathPrefix } from './add-path-prefix'\nimport { addPathSuffix } from './add-path-suffix'\nimport { addLocale } from './add-locale'\n\ninterface ExtendedInfo extends NextPathnameInfo {\n  defaultLocale?: string\n  ignorePrefix?: boolean\n}\n\nexport function formatNextPathnameInfo(info: ExtendedInfo) {\n  let pathname = addLocale(\n    info.pathname,\n    info.locale,\n    info.buildId ? undefined : info.defaultLocale,\n    info.ignorePrefix\n  )\n\n  if (info.buildId || !info.trailingSlash) {\n    pathname = removeTrailingSlash(pathname)\n  }\n\n  if (info.buildId) {\n    pathname = addPathSuffix(\n      addPathPrefix(pathname, `/_next/data/${info.buildId}`),\n      info.pathname === '/' ? 'index.json' : '.json'\n    )\n  }\n\n  pathname = addPathPrefix(pathname, info.basePath)\n  return !info.buildId && info.trailingSlash\n    ? !pathname.endsWith('/')\n      ? addPathSuffix(pathname, '/')\n      : pathname\n    : removeTrailingSlash(pathname)\n}\n"], "names": ["formatNextPathnameInfo", "info", "pathname", "addLocale", "locale", "buildId", "undefined", "defaultLocale", "ignorePrefix", "trailingSlash", "removeTrailingSlash", "addPathSuffix", "addPathPrefix", "basePath", "endsWith"], "mappings": ";;;+BAWg<PERSON>,0BAAAA;;;eAAAA;;;qCAVoB;+BACN;+BACA;2BACJ;AAOnB,SAASA,uBAAuBC,IAAkB;IACvD,IAAIC,WAAWC,CAAAA,GAAAA,WAAAA,SAAS,EACtBF,KAAKC,QAAQ,EACbD,KAAKG,MAAM,EACXH,KAAKI,OAAO,GAAGC,YAAYL,KAAKM,aAAa,EAC7CN,KAAKO,YAAY;IAGnB,IAAIP,KAAKI,OAAO,IAAI,CAACJ,KAAKQ,aAAa,EAAE;QACvCP,WAAWQ,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACR;IACjC;IAEA,IAAID,KAAKI,OAAO,EAAE;QAChBH,WAAWS,CAAAA,GAAAA,eAAAA,aAAa,EACtBC,CAAAA,GAAAA,eAAAA,aAAa,EAACV,UAAW,iBAAcD,KAAKI,OAAO,GACnDJ,KAAKC,QAAQ,KAAK,MAAM,eAAe;IAE3C;IAEAA,WAAWU,CAAAA,GAAAA,eAAAA,aAAa,EAACV,UAAUD,KAAKY,QAAQ;IAChD,OAAO,CAACZ,KAAKI,OAAO,IAAIJ,KAAKQ,aAAa,GACtC,CAACP,SAASY,QAAQ,CAAC,OACjBH,CAAAA,GAAAA,eAAAA,aAAa,EAACT,UAAU,OACxBA,WACFQ,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACR;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2794, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/compare-states.ts"], "sourcesContent": ["import type { default as Router } from '../router'\n\nexport function compareRouterStates(a: Router['state'], b: Router['state']) {\n  const stateKeys = Object.keys(a)\n  if (stateKeys.length !== Object.keys(b).length) return false\n\n  for (let i = stateKeys.length; i--; ) {\n    const key = stateKeys[i]\n    if (key === 'query') {\n      const queryKeys = Object.keys(a.query)\n      if (queryKeys.length !== Object.keys(b.query).length) {\n        return false\n      }\n      for (let j = queryKeys.length; j--; ) {\n        const queryKey = queryKeys[j]\n        if (\n          !b.query.hasOwnProperty(queryKey) ||\n          a.query[queryKey] !== b.query[queryKey]\n        ) {\n          return false\n        }\n      }\n    } else if (\n      !b.hasOwnProperty(key) ||\n      a[key as keyof Router['state']] !== b[key as keyof Router['state']]\n    ) {\n      return false\n    }\n  }\n\n  return true\n}\n"], "names": ["compareRouterStates", "a", "b", "stateKeys", "Object", "keys", "length", "i", "key", "query<PERSON>eys", "query", "j", "query<PERSON><PERSON>", "hasOwnProperty"], "mappings": ";;;+BAEg<PERSON>,uBAAAA;;;eAAAA;;;AAAT,SAASA,oBAAoBC,CAAkB,EAAEC,CAAkB;IACxE,MAAMC,YAAYC,OAAOC,IAAI,CAACJ;IAC9B,IAAIE,UAAUG,MAAM,KAAKF,OAAOC,IAAI,CAACH,GAAGI,MAAM,EAAE,OAAO;IAEvD,IAAK,IAAIC,IAAIJ,UAAUG,MAAM,EAAEC,KAAO;QACpC,MAAMC,MAAML,SAAS,CAACI,EAAE;QACxB,IAAIC,QAAQ,SAAS;YACnB,MAAMC,YAAYL,OAAOC,IAAI,CAACJ,EAAES,KAAK;YACrC,IAAID,UAAUH,MAAM,KAAKF,OAAOC,IAAI,CAACH,EAAEQ,KAAK,EAAEJ,MAAM,EAAE;gBACpD,OAAO;YACT;YACA,IAAK,IAAIK,IAAIF,UAAUH,MAAM,EAAEK,KAAO;gBACpC,MAAMC,WAAWH,SAAS,CAACE,EAAE;gBAC7B,IACE,CAACT,EAAEQ,KAAK,CAACG,cAAc,CAACD,aACxBX,EAAES,KAAK,CAACE,SAAS,KAAKV,EAAEQ,KAAK,CAACE,SAAS,EACvC;oBACA,OAAO;gBACT;YACF;QACF,OAAO,IACL,CAACV,EAAEW,cAAc,CAACL,QAClBP,CAAC,CAACO,IAA6B,KAAKN,CAAC,CAACM,IAA6B,EACnE;YACA,OAAO;QACT;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/html-bots.ts"], "sourcesContent": ["// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\n// Note: The pattern [\\w-]+-Google captures all Google crawlers with \"-Google\" suffix (e.g., Mediapartners-Google, AdsBot-Google, Storebot-Google)\n// as well as crawlers starting with \"Google-\" (e.g., Google-PageRenderer, Google-InspectionTool)\nexport const HTML_LIMITED_BOT_UA_RE =\n  /[\\w-]+-Google|Google-[\\w-]+|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti|googleweblight/i\n"], "names": ["HTML_LIMITED_BOT_UA_RE"], "mappings": "AAAA,6GAA6G;AAC7G,sKAAsK;AACtK,kJAAkJ;AAClJ,iGAAiG;;;;+BACpFA,0BAAAA;;;eAAAA;;;AAAN,MAAMA,yBACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2847, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/is-bot.ts"], "sourcesContent": ["import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS.\n// Only the main Googlebot search crawler executes JavaScript, not other Google crawlers.\n// x-ref: https://developers.google.com/search/docs/crawling-indexing/google-common-crawlers\n// This regex specifically matches \"Googlebot\" but NOT \"Mediapartners-Google\", \"AdsBot-Google\", etc.\nconst HEADLESS_BROWSER_BOT_UA_RE = /Googlebot(?!-)|Googlebot$/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n"], "names": ["HTML_LIMITED_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "getBotType", "isBot", "HEADLESS_BROWSER_BOT_UA_RE", "source", "isDomBotUA", "userAgent", "test", "isHtmlLimitedBotUA", "undefined"], "mappings": ";;;;;;;;;;;;;;;;IAUSA,sBAAsB,EAAA;eAAtBA,UAAAA,sBAAsB;;IAFlBC,6BAA6B,EAAA;eAA7BA;;IAgBGC,UAAU,EAAA;eAAVA;;IAJAC,KAAK,EAAA;eAALA;;;0BApBuB;AAEvC,mEAAmE;AACnE,yFAAyF;AACzF,4FAA4F;AAC5F,oGAAoG;AACpG,MAAMC,6BAA6B;AAE5B,MAAMH,gCAAgCD,UAAAA,sBAAsB,CAACK,MAAM;AAI1E,SAASC,WAAWC,SAAiB;IACnC,OAAOH,2BAA2BI,IAAI,CAACD;AACzC;AAEA,SAASE,mBAAmBF,SAAiB;IAC3C,OAAOP,UAAAA,sBAAsB,CAACQ,IAAI,CAACD;AACrC;AAEO,SAASJ,MAAMI,SAAiB;IACrC,OAAOD,WAAWC,cAAcE,mBAAmBF;AACrD;AAEO,SAASL,WAAWK,SAAiB;IAC1C,IAAID,WAAWC,YAAY;QACzB,OAAO;IACT;IACA,IAAIE,mBAAmBF,YAAY;QACjC,OAAO;IACT;IACA,OAAOG;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2905, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/router.ts"], "sourcesContent": ["// tslint:disable:no-console\nimport type { ComponentType } from 'react'\nimport type { DomainLocale } from '../../../server/config'\nimport type { MittEmitter } from '../mitt'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RouterEvent } from '../../../client/router'\nimport type { StyleSheetTuple } from '../../../client/page-loader'\nimport type { UrlObject } from 'url'\nimport type PageLoader from '../../../client/page-loader'\nimport type { AppContextType, NextPageContext, NEXT_DATA } from '../utils'\nimport { removeTrailingSlash } from './utils/remove-trailing-slash'\nimport {\n  getClientBuildManifest,\n  isAssetError,\n  markAssetError,\n} from '../../../client/route-loader'\nimport { handleClientScriptLoad } from '../../../client/script'\nimport isError, { getProperError } from '../../../lib/is-error'\nimport { denormalizePagePath } from '../page-path/denormalize-page-path'\nimport { normalizeLocalePath } from '../i18n/normalize-locale-path'\nimport mitt from '../mitt'\nimport { getLocationOrigin, getURL, loadGetInitialProps, ST } from '../utils'\nimport { isDynamicRoute } from './utils/is-dynamic'\nimport { parseRelativeUrl } from './utils/parse-relative-url'\nimport { getRouteMatcher } from './utils/route-matcher'\nimport { getRouteRegex } from './utils/route-regex'\nimport { formatWithValidation } from './utils/format-url'\nimport { detectDomainLocale } from '../../../client/detect-domain-locale'\nimport { parsePath } from './utils/parse-path'\nimport { addLocale } from '../../../client/add-locale'\nimport { removeLocale } from '../../../client/remove-locale'\nimport { removeBasePath } from '../../../client/remove-base-path'\nimport { addBasePath } from '../../../client/add-base-path'\nimport { hasBasePath } from '../../../client/has-base-path'\nimport { resolveHref } from '../../../client/resolve-href'\nimport { isAPIRoute } from '../../../lib/is-api-route'\nimport { getNextPathnameInfo } from './utils/get-next-pathname-info'\nimport { formatNextPathnameInfo } from './utils/format-next-pathname-info'\nimport { compareRouterStates } from './utils/compare-states'\nimport { isLocalURL } from './utils/is-local-url'\nimport { isBot } from './utils/is-bot'\nimport { omit } from './utils/omit'\nimport { interpolateAs } from './utils/interpolate-as'\nimport { disableSmoothScrollDuringRouteTransition } from './utils/disable-smooth-scroll'\nimport type { Params } from '../../../server/request/params'\nimport { MATCHED_PATH_HEADER } from '../../../lib/constants'\n\nlet resolveRewrites: typeof import('./utils/resolve-rewrites').default\nif (process.env.__NEXT_HAS_REWRITES) {\n  resolveRewrites = (\n    require('./utils/resolve-rewrites') as typeof import('./utils/resolve-rewrites')\n  ).default\n}\n\ndeclare global {\n  interface Window {\n    /* prod */\n    __NEXT_DATA__: NEXT_DATA\n  }\n}\n\ninterface RouteProperties {\n  shallow: boolean\n}\n\ninterface TransitionOptions {\n  shallow?: boolean\n  locale?: string | false\n  scroll?: boolean\n  unstable_skipClientCache?: boolean\n}\n\ninterface NextHistoryState {\n  url: string\n  as: string\n  options: TransitionOptions\n}\n\nexport type HistoryState =\n  | null\n  | { __NA: true; __N?: false }\n  | { __N: false; __NA?: false }\n  | ({ __NA?: false; __N: true; key: string } & NextHistoryState)\n\nfunction buildCancellationError() {\n  return Object.assign(new Error('Route Cancelled'), {\n    cancelled: true,\n  })\n}\n\ninterface MiddlewareEffectParams<T extends FetchDataOutput> {\n  fetchData?: () => Promise<T>\n  locale?: string\n  asPath: string\n  router: Router\n}\n\nexport async function matchesMiddleware<T extends FetchDataOutput>(\n  options: MiddlewareEffectParams<T>\n): Promise<boolean> {\n  const matchers = await Promise.resolve(\n    options.router.pageLoader.getMiddleware()\n  )\n  if (!matchers) return false\n\n  const { pathname: asPathname } = parsePath(options.asPath)\n  // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n  const cleanedAs = hasBasePath(asPathname)\n    ? removeBasePath(asPathname)\n    : asPathname\n  const asWithBasePathAndLocale = addBasePath(\n    addLocale(cleanedAs, options.locale)\n  )\n\n  // Check only path match on client. Matching \"has\" should be done on server\n  // where we can access more info such as headers, HttpOnly cookie, etc.\n  return matchers.some((m) =>\n    new RegExp(m.regexp).test(asWithBasePathAndLocale)\n  )\n}\n\nfunction stripOrigin(url: string) {\n  const origin = getLocationOrigin()\n\n  return url.startsWith(origin) ? url.substring(origin.length) : url\n}\n\nfunction prepareUrlAs(router: NextRouter, url: Url, as?: Url) {\n  // If url and as provided as an object representation,\n  // we'll format them into the string version here.\n  let [resolvedHref, resolvedAs] = resolveHref(router, url, true)\n  const origin = getLocationOrigin()\n  const hrefWasAbsolute = resolvedHref.startsWith(origin)\n  const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin)\n\n  resolvedHref = stripOrigin(resolvedHref)\n  resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs\n\n  const preparedUrl = hrefWasAbsolute ? resolvedHref : addBasePath(resolvedHref)\n  const preparedAs = as\n    ? stripOrigin(resolveHref(router, as))\n    : resolvedAs || resolvedHref\n\n  return {\n    url: preparedUrl,\n    as: asWasAbsolute ? preparedAs : addBasePath(preparedAs),\n  }\n}\n\nfunction resolveDynamicRoute(pathname: string, pages: string[]) {\n  const cleanPathname = removeTrailingSlash(denormalizePagePath(pathname))\n  if (cleanPathname === '/404' || cleanPathname === '/_error') {\n    return pathname\n  }\n\n  // handle resolving href for dynamic routes\n  if (!pages.includes(cleanPathname)) {\n    // eslint-disable-next-line array-callback-return\n    pages.some((page) => {\n      if (isDynamicRoute(page) && getRouteRegex(page).re.test(cleanPathname)) {\n        pathname = page\n        return true\n      }\n    })\n  }\n  return removeTrailingSlash(pathname)\n}\n\nfunction getMiddlewareData<T extends FetchDataOutput>(\n  source: string,\n  response: Response,\n  options: MiddlewareEffectParams<T>\n) {\n  const nextConfig = {\n    basePath: options.router.basePath,\n    i18n: { locales: options.router.locales },\n    trailingSlash: Boolean(process.env.__NEXT_TRAILING_SLASH),\n  }\n  const rewriteHeader = response.headers.get('x-nextjs-rewrite')\n\n  let rewriteTarget =\n    rewriteHeader || response.headers.get('x-nextjs-matched-path')\n\n  const matchedPath = response.headers.get(MATCHED_PATH_HEADER)\n\n  if (\n    matchedPath &&\n    !rewriteTarget &&\n    !matchedPath.includes('__next_data_catchall') &&\n    !matchedPath.includes('/_error') &&\n    !matchedPath.includes('/404')\n  ) {\n    // leverage x-matched-path to detect next.config.js rewrites\n    rewriteTarget = matchedPath\n  }\n\n  if (rewriteTarget) {\n    if (\n      rewriteTarget.startsWith('/') ||\n      process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE\n    ) {\n      const parsedRewriteTarget = parseRelativeUrl(rewriteTarget)\n      const pathnameInfo = getNextPathnameInfo(parsedRewriteTarget.pathname, {\n        nextConfig,\n        parseData: true,\n      })\n\n      let fsPathname = removeTrailingSlash(pathnameInfo.pathname)\n      return Promise.all([\n        options.router.pageLoader.getPageList(),\n        getClientBuildManifest(),\n      ]).then(([pages, { __rewrites: rewrites }]: any) => {\n        let as = addLocale(pathnameInfo.pathname, pathnameInfo.locale)\n\n        if (\n          isDynamicRoute(as) ||\n          (!rewriteHeader &&\n            pages.includes(\n              normalizeLocalePath(removeBasePath(as), options.router.locales)\n                .pathname\n            ))\n        ) {\n          const parsedSource = getNextPathnameInfo(\n            parseRelativeUrl(source).pathname,\n            {\n              nextConfig: process.env.__NEXT_HAS_REWRITES\n                ? undefined\n                : nextConfig,\n              parseData: true,\n            }\n          )\n\n          as = addBasePath(parsedSource.pathname)\n          parsedRewriteTarget.pathname = as\n        }\n\n        if (process.env.__NEXT_HAS_REWRITES) {\n          const result = resolveRewrites(\n            as,\n            pages,\n            rewrites,\n            parsedRewriteTarget.query,\n            (path: string) => resolveDynamicRoute(path, pages),\n            options.router.locales\n          )\n\n          if (result.matchedPage) {\n            parsedRewriteTarget.pathname = result.parsedAs.pathname\n            as = parsedRewriteTarget.pathname\n            Object.assign(parsedRewriteTarget.query, result.parsedAs.query)\n          }\n        } else if (!pages.includes(fsPathname)) {\n          const resolvedPathname = resolveDynamicRoute(fsPathname, pages)\n\n          if (resolvedPathname !== fsPathname) {\n            fsPathname = resolvedPathname\n          }\n        }\n\n        const resolvedHref = !pages.includes(fsPathname)\n          ? resolveDynamicRoute(\n              normalizeLocalePath(\n                removeBasePath(parsedRewriteTarget.pathname),\n                options.router.locales\n              ).pathname,\n              pages\n            )\n          : fsPathname\n\n        if (isDynamicRoute(resolvedHref)) {\n          const matches = getRouteMatcher(getRouteRegex(resolvedHref))(as)\n          Object.assign(parsedRewriteTarget.query, matches || {})\n        }\n\n        return {\n          type: 'rewrite' as const,\n          parsedAs: parsedRewriteTarget,\n          resolvedHref,\n        }\n      })\n    }\n    const src = parsePath(source)\n    const pathname = formatNextPathnameInfo({\n      ...getNextPathnameInfo(src.pathname, { nextConfig, parseData: true }),\n      defaultLocale: options.router.defaultLocale,\n      buildId: '',\n    })\n\n    return Promise.resolve({\n      type: 'redirect-external' as const,\n      destination: `${pathname}${src.query}${src.hash}`,\n    })\n  }\n\n  const redirectTarget = response.headers.get('x-nextjs-redirect')\n\n  if (redirectTarget) {\n    if (redirectTarget.startsWith('/')) {\n      const src = parsePath(redirectTarget)\n      const pathname = formatNextPathnameInfo({\n        ...getNextPathnameInfo(src.pathname, { nextConfig, parseData: true }),\n        defaultLocale: options.router.defaultLocale,\n        buildId: '',\n      })\n\n      return Promise.resolve({\n        type: 'redirect-internal' as const,\n        newAs: `${pathname}${src.query}${src.hash}`,\n        newUrl: `${pathname}${src.query}${src.hash}`,\n      })\n    }\n\n    return Promise.resolve({\n      type: 'redirect-external' as const,\n      destination: redirectTarget,\n    })\n  }\n\n  return Promise.resolve({ type: 'next' as const })\n}\n\ninterface WithMiddlewareEffectsOutput extends FetchDataOutput {\n  effect: Awaited<ReturnType<typeof getMiddlewareData>>\n}\n\nasync function withMiddlewareEffects<T extends FetchDataOutput>(\n  options: MiddlewareEffectParams<T>\n): Promise<WithMiddlewareEffectsOutput | null> {\n  const matches = await matchesMiddleware(options)\n  if (!matches || !options.fetchData) {\n    return null\n  }\n\n  const data = await options.fetchData()\n\n  const effect = await getMiddlewareData(data.dataHref, data.response, options)\n\n  return {\n    dataHref: data.dataHref,\n    json: data.json,\n    response: data.response,\n    text: data.text,\n    cacheKey: data.cacheKey,\n    effect,\n  }\n}\n\nexport type Url = UrlObject | string\n\nexport type BaseRouter = {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  locale?: string | undefined\n  locales?: readonly string[] | undefined\n  defaultLocale?: string | undefined\n  domainLocales?: readonly DomainLocale[] | undefined\n  isLocaleDomain: boolean\n}\n\nexport type NextRouter = BaseRouter &\n  Pick<\n    Router,\n    | 'push'\n    | 'replace'\n    | 'reload'\n    | 'back'\n    | 'forward'\n    | 'prefetch'\n    | 'beforePopState'\n    | 'events'\n    | 'isFallback'\n    | 'isReady'\n    | 'isPreview'\n  >\n\nexport type PrefetchOptions = {\n  priority?: boolean\n  locale?: string | false\n  unstable_skipClientCache?: boolean\n}\n\nexport type PrivateRouteInfo =\n  | (Omit<CompletePrivateRouteInfo, 'styleSheets'> & { initial: true })\n  | CompletePrivateRouteInfo\n\nexport type CompletePrivateRouteInfo = {\n  Component: ComponentType\n  styleSheets: StyleSheetTuple[]\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n  props?: Record<string, any>\n  err?: Error\n  error?: any\n  route?: string\n  resolvedAs?: string\n  query?: ParsedUrlQuery\n}\n\nexport type AppProps = Pick<CompletePrivateRouteInfo, 'Component' | 'err'> & {\n  router: Router\n} & Record<string, any>\nexport type AppComponent = ComponentType<AppProps>\n\ntype Subscription = (\n  data: PrivateRouteInfo,\n  App: AppComponent,\n  resetScroll: { x: number; y: number } | null\n) => Promise<void>\n\ntype BeforePopStateCallback = (state: NextHistoryState) => boolean\n\ntype ComponentLoadCancel = (() => void) | null\n\ntype HistoryMethod = 'replaceState' | 'pushState'\n\nconst manualScrollRestoration =\n  process.env.__NEXT_SCROLL_RESTORATION &&\n  typeof window !== 'undefined' &&\n  'scrollRestoration' in window.history &&\n  !!(function () {\n    try {\n      let v = '__next'\n      // eslint-disable-next-line no-sequences\n      return sessionStorage.setItem(v, v), sessionStorage.removeItem(v), true\n    } catch (n) {}\n  })()\n\nconst SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND')\n\nfunction fetchRetry(\n  url: string,\n  attempts: number,\n  options: Pick<RequestInit, 'method' | 'headers'>\n): Promise<Response> {\n  return fetch(url, {\n    // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n    // Cookies may also be required for `getServerSideProps`.\n    //\n    // > `fetch` won’t send cookies, unless you set the credentials init\n    // > option.\n    // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n    //\n    // > For maximum browser compatibility when it comes to sending &\n    // > receiving cookies, always supply the `credentials: 'same-origin'`\n    // > option instead of relying on the default.\n    // https://github.com/github/fetch#caveats\n    credentials: 'same-origin',\n    method: options.method || 'GET',\n    headers: Object.assign({}, options.headers, {\n      'x-nextjs-data': '1',\n    }),\n  }).then((response) => {\n    return !response.ok && attempts > 1 && response.status >= 500\n      ? fetchRetry(url, attempts - 1, options)\n      : response\n  })\n}\n\ninterface FetchDataOutput {\n  dataHref: string\n  json: Record<string, any> | null\n  response: Response\n  text: string\n  cacheKey: string\n}\n\ninterface FetchNextDataParams {\n  dataHref: string\n  isServerRender: boolean\n  parseJSON: boolean | undefined\n  hasMiddleware?: boolean\n  inflightCache: NextDataCache\n  persistCache: boolean\n  isPrefetch: boolean\n  isBackground?: boolean\n  unstable_skipClientCache?: boolean\n}\n\nfunction tryToParseAsJSON(text: string) {\n  try {\n    return JSON.parse(text)\n  } catch (error) {\n    return null\n  }\n}\n\nfunction fetchNextData({\n  dataHref,\n  inflightCache,\n  isPrefetch,\n  hasMiddleware,\n  isServerRender,\n  parseJSON,\n  persistCache,\n  isBackground,\n  unstable_skipClientCache,\n}: FetchNextDataParams): Promise<FetchDataOutput> {\n  const { href: cacheKey } = new URL(dataHref, window.location.href)\n  const getData = (params?: { method?: 'HEAD' | 'GET' }) =>\n    fetchRetry(dataHref, isServerRender ? 3 : 1, {\n      headers: Object.assign(\n        {} as HeadersInit,\n        isPrefetch ? { purpose: 'prefetch' } : {},\n        isPrefetch && hasMiddleware ? { 'x-middleware-prefetch': '1' } : {},\n        process.env.NEXT_DEPLOYMENT_ID\n          ? { 'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID }\n          : {}\n      ),\n      method: params?.method ?? 'GET',\n    })\n      .then((response) => {\n        if (response.ok && params?.method === 'HEAD') {\n          return { dataHref, response, text: '', json: {}, cacheKey }\n        }\n\n        return response.text().then((text) => {\n          if (!response.ok) {\n            /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */\n            if (\n              hasMiddleware &&\n              [301, 302, 307, 308].includes(response.status)\n            ) {\n              return { dataHref, response, text, json: {}, cacheKey }\n            }\n\n            if (response.status === 404) {\n              if (tryToParseAsJSON(text)?.notFound) {\n                return {\n                  dataHref,\n                  json: { notFound: SSG_DATA_NOT_FOUND },\n                  response,\n                  text,\n                  cacheKey,\n                }\n              }\n            }\n\n            const error = new Error(`Failed to load static props`)\n\n            /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */\n            if (!isServerRender) {\n              markAssetError(error)\n            }\n\n            throw error\n          }\n\n          return {\n            dataHref,\n            json: parseJSON ? tryToParseAsJSON(text) : null,\n            response,\n            text,\n            cacheKey,\n          }\n        })\n      })\n      .then((data) => {\n        if (\n          !persistCache ||\n          process.env.NODE_ENV !== 'production' ||\n          data.response.headers.get('x-middleware-cache') === 'no-cache'\n        ) {\n          delete inflightCache[cacheKey]\n        }\n        return data\n      })\n      .catch((err) => {\n        if (!unstable_skipClientCache) {\n          delete inflightCache[cacheKey]\n        }\n        if (\n          // chrome\n          err.message === 'Failed to fetch' ||\n          // firefox\n          err.message === 'NetworkError when attempting to fetch resource.' ||\n          // safari\n          err.message === 'Load failed'\n        ) {\n          markAssetError(err)\n        }\n        throw err\n      })\n\n  // when skipping client cache we wait to update\n  // inflight cache until successful data response\n  // this allows racing click event with fetching newer data\n  // without blocking navigation when stale data is available\n  if (unstable_skipClientCache && persistCache) {\n    return getData({}).then((data) => {\n      if (data.response.headers.get('x-middleware-cache') !== 'no-cache') {\n        // only update cache if not marked as no-cache\n        inflightCache[cacheKey] = Promise.resolve(data)\n      }\n\n      return data\n    })\n  }\n\n  if (inflightCache[cacheKey] !== undefined) {\n    return inflightCache[cacheKey]\n  }\n  return (inflightCache[cacheKey] = getData(\n    isBackground ? { method: 'HEAD' } : {}\n  ))\n}\n\ninterface NextDataCache {\n  [asPath: string]: Promise<FetchDataOutput>\n}\n\nexport function createKey() {\n  return Math.random().toString(36).slice(2, 10)\n}\n\nfunction handleHardNavigation({\n  url,\n  router,\n}: {\n  url: string\n  router: Router\n}) {\n  // ensure we don't trigger a hard navigation to the same\n  // URL as this can end up with an infinite refresh\n  if (url === addBasePath(addLocale(router.asPath, router.locale))) {\n    throw new Error(\n      `Invariant: attempted to hard navigate to the same URL ${url} ${location.href}`\n    )\n  }\n  window.location.href = url\n}\n\nconst getCancelledHandler = ({\n  route,\n  router,\n}: {\n  route: string\n  router: Router\n}) => {\n  let cancelled = false\n  const cancel = (router.clc = () => {\n    cancelled = true\n  })\n\n  const handleCancelled = () => {\n    if (cancelled) {\n      const error: any = new Error(\n        `Abort fetching component for route: \"${route}\"`\n      )\n      error.cancelled = true\n      throw error\n    }\n\n    if (cancel === router.clc) {\n      router.clc = null\n    }\n  }\n  return handleCancelled\n}\n\nexport default class Router implements BaseRouter {\n  basePath: string\n\n  /**\n   * Map of all components loaded in `Router`\n   */\n  components: { [pathname: string]: PrivateRouteInfo }\n  // Server Data Cache (full data requests)\n  sdc: NextDataCache = {}\n  // Server Background Cache (HEAD requests)\n  sbc: NextDataCache = {}\n\n  sub: Subscription\n  clc: ComponentLoadCancel\n  pageLoader: PageLoader\n  _bps: BeforePopStateCallback | undefined\n  events: MittEmitter<RouterEvent>\n  _wrapApp: (App: AppComponent) => any\n  isSsr: boolean\n  _inFlightRoute?: string | undefined\n  _shallow?: boolean | undefined\n  locales?: readonly string[] | undefined\n  defaultLocale?: string | undefined\n  domainLocales?: readonly DomainLocale[] | undefined\n  isReady: boolean\n  isLocaleDomain: boolean\n  isFirstPopStateEvent = true\n  _initialMatchesMiddlewarePromise: Promise<boolean>\n  // static entries filter\n  _bfl_s?: import('../../lib/bloom-filter').BloomFilter\n  // dynamic entires filter\n  _bfl_d?: import('../../lib/bloom-filter').BloomFilter\n\n  private state: Readonly<{\n    route: string\n    pathname: string\n    query: ParsedUrlQuery\n    asPath: string\n    locale: string | undefined\n    isFallback: boolean\n    isPreview: boolean\n  }>\n\n  private _key: string = createKey()\n\n  static events: MittEmitter<RouterEvent> = mitt()\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    {\n      initialProps,\n      pageLoader,\n      App,\n      wrapApp,\n      Component,\n      err,\n      subscription,\n      isFallback,\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview,\n    }: {\n      subscription: Subscription\n      initialProps: any\n      pageLoader: any\n      Component: ComponentType\n      App: AppComponent\n      wrapApp: (WrapAppComponent: AppComponent) => any\n      err?: Error\n      isFallback: boolean\n      locale?: string\n      locales?: readonly string[]\n      defaultLocale?: string\n      domainLocales?: readonly DomainLocale[]\n      isPreview?: boolean\n    }\n  ) {\n    // represents the current component key\n    const route = removeTrailingSlash(pathname)\n\n    // set up the component cache (by route keys)\n    this.components = {}\n    // We should not keep the cache, if there's an error\n    // Otherwise, this cause issues when when going back and\n    // come again to the errored page.\n    if (pathname !== '/_error') {\n      this.components[route] = {\n        Component,\n        initial: true,\n        props: initialProps,\n        err,\n        __N_SSG: initialProps && initialProps.__N_SSG,\n        __N_SSP: initialProps && initialProps.__N_SSP,\n      }\n    }\n\n    this.components['/_app'] = {\n      Component: App as ComponentType,\n      styleSheets: [\n        /* /_app does not need its stylesheets managed */\n      ],\n    }\n\n    // Backwards compat for Router.router.events\n    // TODO: Should be remove the following major version as it was never documented\n    this.events = Router.events\n\n    this.pageLoader = pageLoader\n    // if auto prerendered and dynamic route wait to update asPath\n    // until after mount to prevent hydration mismatch\n    const autoExportDynamic =\n      isDynamicRoute(pathname) && self.__NEXT_DATA__.autoExport\n\n    this.basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\n    this.sub = subscription\n    this.clc = null\n    this._wrapApp = wrapApp\n    // make sure to ignore extra popState in safari on navigating\n    // back from external site\n    this.isSsr = true\n    this.isLocaleDomain = false\n    this.isReady = !!(\n      self.__NEXT_DATA__.gssp ||\n      self.__NEXT_DATA__.gip ||\n      self.__NEXT_DATA__.isExperimentalCompile ||\n      (self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp) ||\n      (!autoExportDynamic &&\n        !self.location.search &&\n        !process.env.__NEXT_HAS_REWRITES)\n    )\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      this.locales = locales\n      this.defaultLocale = defaultLocale\n      this.domainLocales = domainLocales\n      this.isLocaleDomain = !!detectDomainLocale(\n        domainLocales,\n        self.location.hostname\n      )\n    }\n\n    this.state = {\n      route,\n      pathname,\n      query,\n      asPath: autoExportDynamic ? pathname : as,\n      isPreview: !!isPreview,\n      locale: process.env.__NEXT_I18N_SUPPORT ? locale : undefined,\n      isFallback,\n    }\n\n    this._initialMatchesMiddlewarePromise = Promise.resolve(false)\n\n    if (typeof window !== 'undefined') {\n      // make sure \"as\" doesn't start with double slashes or else it can\n      // throw an error as it's considered invalid\n      if (!as.startsWith('//')) {\n        // in order for `e.state` to work on the `onpopstate` event\n        // we have to register the initial route upon initialization\n        const options: TransitionOptions = { locale }\n        const asPath = getURL()\n\n        this._initialMatchesMiddlewarePromise = matchesMiddleware({\n          router: this,\n          locale,\n          asPath,\n        }).then((matches) => {\n          // if middleware matches we leave resolving to the change function\n          // as the server needs to resolve for correct priority\n          ;(options as any)._shouldResolveHref = as !== pathname\n\n          this.changeState(\n            'replaceState',\n            matches\n              ? asPath\n              : formatWithValidation({\n                  pathname: addBasePath(pathname),\n                  query,\n                }),\n            asPath,\n            options\n          )\n          return matches\n        })\n      }\n\n      window.addEventListener('popstate', this.onPopState)\n\n      // enable custom scroll restoration handling when available\n      // otherwise fallback to browser's default handling\n      if (process.env.__NEXT_SCROLL_RESTORATION) {\n        if (manualScrollRestoration) {\n          window.history.scrollRestoration = 'manual'\n        }\n      }\n    }\n  }\n\n  onPopState = (e: PopStateEvent): void => {\n    const { isFirstPopStateEvent } = this\n    this.isFirstPopStateEvent = false\n\n    const state = e.state as HistoryState\n\n    if (!state) {\n      // We get state as undefined for two reasons.\n      //  1. With older safari (< 8) and older chrome (< 34)\n      //  2. When the URL changed with #\n      //\n      // In the both cases, we don't need to proceed and change the route.\n      // (as it's already changed)\n      // But we can simply replace the state with the new changes.\n      // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n      // So, doing the following for (1) does no harm.\n      const { pathname, query } = this\n      this.changeState(\n        'replaceState',\n        formatWithValidation({ pathname: addBasePath(pathname), query }),\n        getURL()\n      )\n      return\n    }\n\n    // __NA is used to identify if the history entry can be handled by the app-router.\n    if (state.__NA) {\n      window.location.reload()\n      return\n    }\n\n    if (!state.__N) {\n      return\n    }\n\n    // Safari fires popstateevent when reopening the browser.\n    if (\n      isFirstPopStateEvent &&\n      this.locale === state.options.locale &&\n      state.as === this.asPath\n    ) {\n      return\n    }\n\n    let forcedScroll: { x: number; y: number } | undefined\n    const { url, as, options, key } = state\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      if (manualScrollRestoration) {\n        if (this._key !== key) {\n          // Snapshot current scroll position:\n          try {\n            sessionStorage.setItem(\n              '__next_scroll_' + this._key,\n              JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n            )\n          } catch {}\n\n          // Restore old scroll position:\n          try {\n            const v = sessionStorage.getItem('__next_scroll_' + key)\n            forcedScroll = JSON.parse(v!)\n          } catch {\n            forcedScroll = { x: 0, y: 0 }\n          }\n        }\n      }\n    }\n    this._key = key\n\n    const { pathname } = parseRelativeUrl(url)\n\n    // Make sure we don't re-render on initial load,\n    // can be caused by navigating back from an external site\n    if (\n      this.isSsr &&\n      as === addBasePath(this.asPath) &&\n      pathname === addBasePath(this.pathname)\n    ) {\n      return\n    }\n\n    // If the downstream application returns falsy, return.\n    // They will then be responsible for handling the event.\n    if (this._bps && !this._bps(state)) {\n      return\n    }\n\n    this.change(\n      'replaceState',\n      url,\n      as,\n      Object.assign<{}, TransitionOptions, TransitionOptions>({}, options, {\n        shallow: options.shallow && this._shallow,\n        locale: options.locale || this.defaultLocale,\n        // @ts-ignore internal value not exposed on types\n        _h: 0,\n      }),\n      forcedScroll\n    )\n  }\n\n  reload(): void {\n    window.location.reload()\n  }\n\n  /**\n   * Go back in history\n   */\n  back() {\n    window.history.back()\n  }\n\n  /**\n   * Go forward in history\n   */\n  forward() {\n    window.history.forward()\n  }\n\n  /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  push(url: Url, as?: Url, options: TransitionOptions = {}) {\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      // TODO: remove in the future when we update history before route change\n      // is complete, as the popstate event should handle this capture.\n      if (manualScrollRestoration) {\n        try {\n          // Snapshot scroll position right before navigating to a new page:\n          sessionStorage.setItem(\n            '__next_scroll_' + this._key,\n            JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n          )\n        } catch {}\n      }\n    }\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('pushState', url, as, options)\n  }\n\n  /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  replace(url: Url, as?: Url, options: TransitionOptions = {}) {\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('replaceState', url, as, options)\n  }\n\n  async _bfl(\n    as: string,\n    resolvedAs?: string,\n    locale?: string | false,\n    skipNavigate?: boolean\n  ) {\n    if (process.env.__NEXT_CLIENT_ROUTER_FILTER_ENABLED) {\n      if (!this._bfl_s && !this._bfl_d) {\n        const { BloomFilter } =\n          require('../../lib/bloom-filter') as typeof import('../../lib/bloom-filter')\n\n        type Filter = ReturnType<\n          import('../../lib/bloom-filter').BloomFilter['export']\n        >\n        let staticFilterData: Filter | undefined\n        let dynamicFilterData: Filter | undefined\n\n        try {\n          ;({\n            __routerFilterStatic: staticFilterData,\n            __routerFilterDynamic: dynamicFilterData,\n          } = (await getClientBuildManifest()) as any as {\n            __routerFilterStatic?: Filter\n            __routerFilterDynamic?: Filter\n          })\n        } catch (err) {\n          // failed to load build manifest hard navigate\n          // to be safe\n          console.error(err)\n          if (skipNavigate) {\n            return true\n          }\n          handleHardNavigation({\n            url: addBasePath(\n              addLocale(as, locale || this.locale, this.defaultLocale)\n            ),\n            router: this,\n          })\n          return new Promise(() => {})\n        }\n\n        const routerFilterSValue: Filter | false = process.env\n          .__NEXT_CLIENT_ROUTER_S_FILTER as any\n\n        if (!staticFilterData && routerFilterSValue) {\n          staticFilterData = routerFilterSValue ? routerFilterSValue : undefined\n        }\n\n        const routerFilterDValue: Filter | false = process.env\n          .__NEXT_CLIENT_ROUTER_D_FILTER as any\n\n        if (!dynamicFilterData && routerFilterDValue) {\n          dynamicFilterData = routerFilterDValue\n            ? routerFilterDValue\n            : undefined\n        }\n\n        if (staticFilterData?.numHashes) {\n          this._bfl_s = new BloomFilter(\n            staticFilterData.numItems,\n            staticFilterData.errorRate\n          )\n          this._bfl_s.import(staticFilterData)\n        }\n\n        if (dynamicFilterData?.numHashes) {\n          this._bfl_d = new BloomFilter(\n            dynamicFilterData.numItems,\n            dynamicFilterData.errorRate\n          )\n          this._bfl_d.import(dynamicFilterData)\n        }\n      }\n\n      let matchesBflStatic = false\n      let matchesBflDynamic = false\n      const pathsToCheck: Array<{ as?: string; allowMatchCurrent?: boolean }> =\n        [{ as }, { as: resolvedAs }]\n\n      for (const { as: curAs, allowMatchCurrent } of pathsToCheck) {\n        if (curAs) {\n          const asNoSlash = removeTrailingSlash(\n            new URL(curAs, 'http://n').pathname\n          )\n          const asNoSlashLocale = addBasePath(\n            addLocale(asNoSlash, locale || this.locale)\n          )\n\n          if (\n            allowMatchCurrent ||\n            asNoSlash !==\n              removeTrailingSlash(new URL(this.asPath, 'http://n').pathname)\n          ) {\n            matchesBflStatic =\n              matchesBflStatic ||\n              !!this._bfl_s?.contains(asNoSlash) ||\n              !!this._bfl_s?.contains(asNoSlashLocale)\n\n            for (const normalizedAS of [asNoSlash, asNoSlashLocale]) {\n              // if any sub-path of as matches a dynamic filter path\n              // it should be hard navigated\n              const curAsParts = normalizedAS.split('/')\n              for (\n                let i = 0;\n                !matchesBflDynamic && i < curAsParts.length + 1;\n                i++\n              ) {\n                const currentPart = curAsParts.slice(0, i).join('/')\n                if (currentPart && this._bfl_d?.contains(currentPart)) {\n                  matchesBflDynamic = true\n                  break\n                }\n              }\n            }\n\n            // if the client router filter is matched then we trigger\n            // a hard navigation\n            if (matchesBflStatic || matchesBflDynamic) {\n              if (skipNavigate) {\n                return true\n              }\n              handleHardNavigation({\n                url: addBasePath(\n                  addLocale(as, locale || this.locale, this.defaultLocale)\n                ),\n                router: this,\n              })\n              return new Promise(() => {})\n            }\n          }\n        }\n      }\n    }\n    return false\n  }\n\n  private async change(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions,\n    forcedScroll?: { x: number; y: number }\n  ): Promise<boolean> {\n    if (!isLocalURL(url)) {\n      handleHardNavigation({ url, router: this })\n      return false\n    }\n    // WARNING: `_h` is an internal option for handing Next.js client-side\n    // hydration. Your app should _never_ use this property. It may change at\n    // any time without notice.\n    const isQueryUpdating = (options as any)._h === 1\n\n    if (!isQueryUpdating && !options.shallow) {\n      await this._bfl(as, undefined, options.locale)\n    }\n\n    let shouldResolveHref =\n      isQueryUpdating ||\n      (options as any)._shouldResolveHref ||\n      parsePath(url).pathname === parsePath(as).pathname\n\n    const nextState = {\n      ...this.state,\n    }\n\n    // for static pages with query params in the URL we delay\n    // marking the router ready until after the query is updated\n    // or a navigation has occurred\n    const readyStateChange = this.isReady !== true\n    this.isReady = true\n    const isSsr = this.isSsr\n\n    if (!isQueryUpdating) {\n      this.isSsr = false\n    }\n\n    // if a route transition is already in progress before\n    // the query updating is triggered ignore query updating\n    if (isQueryUpdating && this.clc) {\n      return false\n    }\n\n    const prevLocale = nextState.locale\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      nextState.locale =\n        options.locale === false\n          ? this.defaultLocale\n          : options.locale || nextState.locale\n\n      if (typeof options.locale === 'undefined') {\n        options.locale = nextState.locale\n      }\n\n      const parsedAs = parseRelativeUrl(\n        hasBasePath(as) ? removeBasePath(as) : as\n      )\n      const localePathResult = normalizeLocalePath(\n        parsedAs.pathname,\n        this.locales\n      )\n\n      if (localePathResult.detectedLocale) {\n        nextState.locale = localePathResult.detectedLocale\n        parsedAs.pathname = addBasePath(parsedAs.pathname)\n        as = formatWithValidation(parsedAs)\n        url = addBasePath(\n          normalizeLocalePath(\n            hasBasePath(url) ? removeBasePath(url) : url,\n            this.locales\n          ).pathname\n        )\n      }\n      let didNavigate = false\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if the locale isn't configured hard navigate to show 404 page\n        if (!this.locales?.includes(nextState.locale!)) {\n          parsedAs.pathname = addLocale(parsedAs.pathname, nextState.locale)\n          handleHardNavigation({\n            url: formatWithValidation(parsedAs),\n            router: this,\n          })\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      const detectedDomain = detectDomainLocale(\n        this.domainLocales,\n        undefined,\n        nextState.locale\n      )\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if we are navigating to a domain locale ensure we redirect to the\n        // correct domain\n        if (\n          !didNavigate &&\n          detectedDomain &&\n          this.isLocaleDomain &&\n          self.location.hostname !== detectedDomain.domain\n        ) {\n          const asNoBasePath = removeBasePath(as)\n          handleHardNavigation({\n            url: `http${detectedDomain.http ? '' : 's'}://${\n              detectedDomain.domain\n            }${addBasePath(\n              `${\n                nextState.locale === detectedDomain.defaultLocale\n                  ? ''\n                  : `/${nextState.locale}`\n              }${asNoBasePath === '/' ? '' : asNoBasePath}` || '/'\n            )}`,\n            router: this,\n          })\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      if (didNavigate) {\n        return new Promise(() => {})\n      }\n    }\n\n    // marking route changes as a navigation start entry\n    if (ST) {\n      performance.mark('routeChange')\n    }\n\n    const { shallow = false, scroll = true } = options\n    const routeProps = { shallow }\n\n    if (this._inFlightRoute && this.clc) {\n      if (!isSsr) {\n        Router.events.emit(\n          'routeChangeError',\n          buildCancellationError(),\n          this._inFlightRoute,\n          routeProps\n        )\n      }\n      this.clc()\n      this.clc = null\n    }\n\n    as = addBasePath(\n      addLocale(\n        hasBasePath(as) ? removeBasePath(as) : as,\n        options.locale,\n        this.defaultLocale\n      )\n    )\n    const cleanedAs = removeLocale(\n      hasBasePath(as) ? removeBasePath(as) : as,\n      nextState.locale\n    )\n    this._inFlightRoute = as\n\n    const localeChange = prevLocale !== nextState.locale\n\n    // If the url change is only related to a hash change\n    // We should not proceed. We should only change the state.\n\n    if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n      nextState.asPath = cleanedAs\n      Router.events.emit('hashChangeStart', as, routeProps)\n      // TODO: do we need the resolved href when only a hash change?\n      this.changeState(method, url, as, {\n        ...options,\n        scroll: false,\n      })\n      if (scroll) {\n        this.scrollToHash(cleanedAs)\n      }\n      try {\n        await this.set(nextState, this.components[nextState.route], null)\n      } catch (err) {\n        if (isError(err) && err.cancelled) {\n          Router.events.emit('routeChangeError', err, cleanedAs, routeProps)\n        }\n        throw err\n      }\n\n      Router.events.emit('hashChangeComplete', as, routeProps)\n      return true\n    }\n\n    let parsed = parseRelativeUrl(url)\n    let { pathname, query } = parsed\n\n    // The build manifest needs to be loaded before auto-static dynamic pages\n    // get their query parameters to allow ensuring they can be parsed properly\n    // when rewritten to\n    let pages: string[], rewrites: any\n    try {\n      ;[pages, { __rewrites: rewrites }] = await Promise.all([\n        this.pageLoader.getPageList(),\n        getClientBuildManifest(),\n        this.pageLoader.getMiddleware(),\n      ])\n    } catch (err) {\n      // If we fail to resolve the page list or client-build manifest, we must\n      // do a server-side transition:\n      handleHardNavigation({ url: as, router: this })\n      return false\n    }\n\n    // If asked to change the current URL we should reload the current page\n    // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n    // We also need to set the method = replaceState always\n    // as this should not go into the history (That's how browsers work)\n    // We should compare the new asPath to the current asPath, not the url\n    if (!this.urlIsNew(cleanedAs) && !localeChange) {\n      method = 'replaceState'\n    }\n\n    // we need to resolve the as value using rewrites for dynamic SSG\n    // pages to allow building the data URL correctly\n    let resolvedAs = as\n\n    // url and as should always be prefixed with basePath by this\n    // point by either next/link or router.push/replace so strip the\n    // basePath from the pathname to match the pages dir 1-to-1\n    pathname = pathname\n      ? removeTrailingSlash(removeBasePath(pathname))\n      : pathname\n\n    let route = removeTrailingSlash(pathname)\n    const parsedAsPathname = as.startsWith('/') && parseRelativeUrl(as).pathname\n\n    // if we detected the path as app route during prefetching\n    // trigger hard navigation\n    if ((this.components[pathname] as any)?.__appRouter) {\n      handleHardNavigation({ url: as, router: this })\n      return new Promise(() => {})\n    }\n\n    const isMiddlewareRewrite = !!(\n      parsedAsPathname &&\n      route !== parsedAsPathname &&\n      (!isDynamicRoute(route) ||\n        !getRouteMatcher(getRouteRegex(route))(parsedAsPathname))\n    )\n\n    // we don't attempt resolve asPath when we need to execute\n    // middleware as the resolving will occur server-side\n    const isMiddlewareMatch =\n      !options.shallow &&\n      (await matchesMiddleware({\n        asPath: as,\n        locale: nextState.locale,\n        router: this,\n      }))\n\n    if (isQueryUpdating && isMiddlewareMatch) {\n      shouldResolveHref = false\n    }\n\n    if (shouldResolveHref && pathname !== '/_error') {\n      ;(options as any)._shouldResolveHref = true\n\n      if (process.env.__NEXT_HAS_REWRITES && as.startsWith('/')) {\n        const rewritesResult = resolveRewrites(\n          addBasePath(addLocale(cleanedAs, nextState.locale), true),\n          pages,\n          rewrites,\n          query,\n          (p: string) => resolveDynamicRoute(p, pages),\n          this.locales\n        )\n\n        if (rewritesResult.externalDest) {\n          handleHardNavigation({ url: as, router: this })\n          return true\n        }\n        if (!isMiddlewareMatch) {\n          resolvedAs = rewritesResult.asPath\n        }\n\n        if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n          // if this directly matches a page we need to update the href to\n          // allow the correct page chunk to be loaded\n          pathname = rewritesResult.resolvedHref\n          parsed.pathname = addBasePath(pathname)\n\n          if (!isMiddlewareMatch) {\n            url = formatWithValidation(parsed)\n          }\n        }\n      } else {\n        parsed.pathname = resolveDynamicRoute(pathname, pages)\n\n        if (parsed.pathname !== pathname) {\n          pathname = parsed.pathname\n          parsed.pathname = addBasePath(pathname)\n\n          if (!isMiddlewareMatch) {\n            url = formatWithValidation(parsed)\n          }\n        }\n      }\n    }\n\n    if (!isLocalURL(as)) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error(\n          `Invalid href: \"${url}\" and as: \"${as}\", received relative href and external as` +\n            `\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as`\n        )\n      }\n      handleHardNavigation({ url: as, router: this })\n      return false\n    }\n\n    resolvedAs = removeLocale(removeBasePath(resolvedAs), nextState.locale)\n\n    route = removeTrailingSlash(pathname)\n    let routeMatch: Params | false = false\n\n    if (isDynamicRoute(route)) {\n      const parsedAs = parseRelativeUrl(resolvedAs)\n      const asPathname = parsedAs.pathname\n\n      const routeRegex = getRouteRegex(route)\n      routeMatch = getRouteMatcher(routeRegex)(asPathname)\n      const shouldInterpolate = route === asPathname\n      const interpolatedAs = shouldInterpolate\n        ? interpolateAs(route, asPathname, query)\n        : ({} as { result: undefined; params: undefined })\n\n      if (!routeMatch || (shouldInterpolate && !interpolatedAs.result)) {\n        const missingParams = Object.keys(routeRegex.groups).filter(\n          (param) => !query[param] && !routeRegex.groups[param].optional\n        )\n\n        if (missingParams.length > 0 && !isMiddlewareMatch) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\n              `${\n                shouldInterpolate\n                  ? `Interpolating href`\n                  : `Mismatching \\`as\\` and \\`href\\``\n              } failed to manually provide ` +\n                `the params: ${missingParams.join(\n                  ', '\n                )} in the \\`href\\`'s \\`query\\``\n            )\n          }\n\n          throw new Error(\n            (shouldInterpolate\n              ? `The provided \\`href\\` (${url}) value is missing query values (${missingParams.join(\n                  ', '\n                )}) to be interpolated properly. `\n              : `The provided \\`as\\` value (${asPathname}) is incompatible with the \\`href\\` value (${route}). `) +\n              `Read more: https://nextjs.org/docs/messages/${\n                shouldInterpolate\n                  ? 'href-interpolation-failed'\n                  : 'incompatible-href-as'\n              }`\n          )\n        }\n      } else if (shouldInterpolate) {\n        as = formatWithValidation(\n          Object.assign({}, parsedAs, {\n            pathname: interpolatedAs.result,\n            query: omit(query, interpolatedAs.params!),\n          })\n        )\n      } else {\n        // Merge params into `query`, overwriting any specified in search\n        Object.assign(query, routeMatch)\n      }\n    }\n\n    if (!isQueryUpdating) {\n      Router.events.emit('routeChangeStart', as, routeProps)\n    }\n\n    const isErrorRoute = this.pathname === '/404' || this.pathname === '/_error'\n\n    try {\n      let routeInfo = await this.getRouteInfo({\n        route,\n        pathname,\n        query,\n        as,\n        resolvedAs,\n        routeProps,\n        locale: nextState.locale,\n        isPreview: nextState.isPreview,\n        hasMiddleware: isMiddlewareMatch,\n        unstable_skipClientCache: options.unstable_skipClientCache,\n        isQueryUpdating: isQueryUpdating && !this.isFallback,\n        isMiddlewareRewrite,\n      })\n\n      if (!isQueryUpdating && !options.shallow) {\n        await this._bfl(\n          as,\n          'resolvedAs' in routeInfo ? routeInfo.resolvedAs : undefined,\n          nextState.locale\n        )\n      }\n\n      if ('route' in routeInfo && isMiddlewareMatch) {\n        pathname = routeInfo.route || route\n        route = pathname\n\n        if (!routeProps.shallow) {\n          query = Object.assign({}, routeInfo.query || {}, query)\n        }\n\n        const cleanedParsedPathname = hasBasePath(parsed.pathname)\n          ? removeBasePath(parsed.pathname)\n          : parsed.pathname\n\n        if (routeMatch && pathname !== cleanedParsedPathname) {\n          Object.keys(routeMatch).forEach((key) => {\n            if (routeMatch && query[key] === routeMatch[key]) {\n              delete query[key]\n            }\n          })\n        }\n\n        if (isDynamicRoute(pathname)) {\n          const prefixedAs =\n            !routeProps.shallow && routeInfo.resolvedAs\n              ? routeInfo.resolvedAs\n              : addBasePath(\n                  addLocale(\n                    new URL(as, location.href).pathname,\n                    nextState.locale\n                  ),\n                  true\n                )\n\n          let rewriteAs = prefixedAs\n\n          if (hasBasePath(rewriteAs)) {\n            rewriteAs = removeBasePath(rewriteAs)\n          }\n\n          if (process.env.__NEXT_I18N_SUPPORT) {\n            const localeResult = normalizeLocalePath(rewriteAs, this.locales)\n            nextState.locale = localeResult.detectedLocale || nextState.locale\n            rewriteAs = localeResult.pathname\n          }\n          const routeRegex = getRouteRegex(pathname)\n          const curRouteMatch = getRouteMatcher(routeRegex)(\n            new URL(rewriteAs, location.href).pathname\n          )\n\n          if (curRouteMatch) {\n            Object.assign(query, curRouteMatch)\n          }\n        }\n      }\n\n      // If the routeInfo brings a redirect we simply apply it.\n      if ('type' in routeInfo) {\n        if (routeInfo.type === 'redirect-internal') {\n          return this.change(method, routeInfo.newUrl, routeInfo.newAs, options)\n        } else {\n          handleHardNavigation({ url: routeInfo.destination, router: this })\n          return new Promise(() => {})\n        }\n      }\n\n      const component: any = routeInfo.Component\n      if (component && component.unstable_scriptLoader) {\n        const scripts = [].concat(component.unstable_scriptLoader())\n\n        scripts.forEach((script: any) => {\n          handleClientScriptLoad(script.props)\n        })\n      }\n\n      // handle redirect on client-transition\n      if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n        if (\n          routeInfo.props.pageProps &&\n          routeInfo.props.pageProps.__N_REDIRECT\n        ) {\n          // Use the destination from redirect without adding locale\n          options.locale = false\n\n          const destination = routeInfo.props.pageProps.__N_REDIRECT\n\n          // check if destination is internal (resolves to a page) and attempt\n          // client-navigation if it is falling back to hard navigation if\n          // it's not\n          if (\n            destination.startsWith('/') &&\n            routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false\n          ) {\n            const parsedHref = parseRelativeUrl(destination)\n            parsedHref.pathname = resolveDynamicRoute(\n              parsedHref.pathname,\n              pages\n            )\n\n            const { url: newUrl, as: newAs } = prepareUrlAs(\n              this,\n              destination,\n              destination\n            )\n            return this.change(method, newUrl, newAs, options)\n          }\n          handleHardNavigation({ url: destination, router: this })\n          return new Promise(() => {})\n        }\n\n        nextState.isPreview = !!routeInfo.props.__N_PREVIEW\n\n        // handle SSG data 404\n        if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n          let notFoundRoute\n\n          try {\n            await this.fetchComponent('/404')\n            notFoundRoute = '/404'\n          } catch (_) {\n            notFoundRoute = '/_error'\n          }\n\n          routeInfo = await this.getRouteInfo({\n            route: notFoundRoute,\n            pathname: notFoundRoute,\n            query,\n            as,\n            resolvedAs,\n            routeProps: { shallow: false },\n            locale: nextState.locale,\n            isPreview: nextState.isPreview,\n            isNotFound: true,\n          })\n\n          if ('type' in routeInfo) {\n            throw new Error(`Unexpected middleware effect on /404`)\n          }\n        }\n      }\n\n      if (\n        isQueryUpdating &&\n        this.pathname === '/_error' &&\n        self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n        routeInfo.props?.pageProps\n      ) {\n        // ensure statusCode is still correct for static 500 page\n        // when updating query information\n        routeInfo.props.pageProps.statusCode = 500\n      }\n\n      // shallow routing is only allowed for same page URL changes.\n      const isValidShallowRoute =\n        options.shallow && nextState.route === (routeInfo.route ?? route)\n\n      const shouldScroll =\n        options.scroll ?? (!isQueryUpdating && !isValidShallowRoute)\n      const resetScroll = shouldScroll ? { x: 0, y: 0 } : null\n      const upcomingScrollState = forcedScroll ?? resetScroll\n\n      // the new state that the router gonna set\n      const upcomingRouterState = {\n        ...nextState,\n        route,\n        pathname,\n        query,\n        asPath: cleanedAs,\n        isFallback: false,\n      }\n\n      // When the page being rendered is the 404 page, we should only update the\n      // query parameters. Route changes here might add the basePath when it\n      // wasn't originally present. This is also why this block is before the\n      // below `changeState` call which updates the browser's history (changing\n      // the URL).\n      if (isQueryUpdating && isErrorRoute) {\n        routeInfo = await this.getRouteInfo({\n          route: this.pathname,\n          pathname: this.pathname,\n          query,\n          as,\n          resolvedAs,\n          routeProps: { shallow: false },\n          locale: nextState.locale,\n          isPreview: nextState.isPreview,\n          isQueryUpdating: isQueryUpdating && !this.isFallback,\n        })\n\n        if ('type' in routeInfo) {\n          throw new Error(`Unexpected middleware effect on ${this.pathname}`)\n        }\n\n        if (\n          this.pathname === '/_error' &&\n          self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n          routeInfo.props?.pageProps\n        ) {\n          // ensure statusCode is still correct for static 500 page\n          // when updating query information\n          routeInfo.props.pageProps.statusCode = 500\n        }\n\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n        } catch (err) {\n          if (isError(err) && err.cancelled) {\n            Router.events.emit('routeChangeError', err, cleanedAs, routeProps)\n          }\n          throw err\n        }\n\n        return true\n      }\n\n      Router.events.emit('beforeHistoryChange', as, routeProps)\n      this.changeState(method, url, as, options)\n\n      // for query updates we can skip it if the state is unchanged and we don't\n      // need to scroll\n      // https://github.com/vercel/next.js/issues/37139\n      const canSkipUpdating =\n        isQueryUpdating &&\n        !upcomingScrollState &&\n        !readyStateChange &&\n        !localeChange &&\n        compareRouterStates(upcomingRouterState, this.state)\n\n      if (!canSkipUpdating) {\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n        } catch (e: any) {\n          if (e.cancelled) routeInfo.error = routeInfo.error || e\n          else throw e\n        }\n\n        if (routeInfo.error) {\n          if (!isQueryUpdating) {\n            Router.events.emit(\n              'routeChangeError',\n              routeInfo.error,\n              cleanedAs,\n              routeProps\n            )\n          }\n\n          throw routeInfo.error\n        }\n\n        if (process.env.__NEXT_I18N_SUPPORT) {\n          if (nextState.locale) {\n            document.documentElement.lang = nextState.locale\n          }\n        }\n\n        if (!isQueryUpdating) {\n          Router.events.emit('routeChangeComplete', as, routeProps)\n        }\n\n        // A hash mark # is the optional last part of a URL\n        const hashRegex = /#.+$/\n        if (shouldScroll && hashRegex.test(as)) {\n          this.scrollToHash(as)\n        }\n      }\n\n      return true\n    } catch (err) {\n      if (isError(err) && err.cancelled) {\n        return false\n      }\n      throw err\n    }\n  }\n\n  changeState(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions = {}\n  ): void {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof window.history === 'undefined') {\n        console.error(`Warning: window.history is not available.`)\n        return\n      }\n\n      if (typeof window.history[method] === 'undefined') {\n        console.error(`Warning: window.history.${method} is not available`)\n        return\n      }\n    }\n\n    if (method !== 'pushState' || getURL() !== as) {\n      this._shallow = options.shallow\n      window.history[method](\n        {\n          url,\n          as,\n          options,\n          __N: true,\n          key: (this._key = method !== 'pushState' ? this._key : createKey()),\n        } as HistoryState,\n        // Most browsers currently ignores this parameter, although they may use it in the future.\n        // Passing the empty string here should be safe against future changes to the method.\n        // https://developer.mozilla.org/docs/Web/API/History/replaceState\n        '',\n        as\n      )\n    }\n  }\n\n  async handleRouteInfoError(\n    err: Error & { code?: any; cancelled?: boolean },\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    routeProps: RouteProperties,\n    loadErrorFail?: boolean\n  ): Promise<CompletePrivateRouteInfo> {\n    if (err.cancelled) {\n      // bubble up cancellation errors\n      throw err\n    }\n\n    if (isAssetError(err) || loadErrorFail) {\n      Router.events.emit('routeChangeError', err, as, routeProps)\n\n      // If we can't load the page it could be one of following reasons\n      //  1. Page doesn't exists\n      //  2. Page does exist in a different zone\n      //  3. Internal error while loading the page\n\n      // So, doing a hard reload is the proper way to deal with this.\n      handleHardNavigation({\n        url: as,\n        router: this,\n      })\n\n      // Changing the URL doesn't block executing the current code path.\n      // So let's throw a cancellation error stop the routing logic.\n      throw buildCancellationError()\n    }\n\n    console.error(err)\n\n    try {\n      let props: Record<string, any> | undefined\n      const { page: Component, styleSheets } =\n        await this.fetchComponent('/_error')\n\n      const routeInfo: CompletePrivateRouteInfo = {\n        props,\n        Component,\n        styleSheets,\n        err,\n        error: err,\n      }\n\n      if (!routeInfo.props) {\n        try {\n          routeInfo.props = await this.getInitialProps(Component, {\n            err,\n            pathname,\n            query,\n          } as any)\n        } catch (gipErr) {\n          console.error('Error in error page `getInitialProps`: ', gipErr)\n          routeInfo.props = {}\n        }\n      }\n\n      return routeInfo\n    } catch (routeInfoErr) {\n      return this.handleRouteInfoError(\n        isError(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + ''),\n        pathname,\n        query,\n        as,\n        routeProps,\n        true\n      )\n    }\n  }\n\n  async getRouteInfo({\n    route: requestedRoute,\n    pathname,\n    query,\n    as,\n    resolvedAs,\n    routeProps,\n    locale,\n    hasMiddleware,\n    isPreview,\n    unstable_skipClientCache,\n    isQueryUpdating,\n    isMiddlewareRewrite,\n    isNotFound,\n  }: {\n    route: string\n    pathname: string\n    query: ParsedUrlQuery\n    as: string\n    resolvedAs: string\n    hasMiddleware?: boolean\n    routeProps: RouteProperties\n    locale: string | undefined\n    isPreview: boolean\n    unstable_skipClientCache?: boolean\n    isQueryUpdating?: boolean\n    isMiddlewareRewrite?: boolean\n    isNotFound?: boolean\n  }) {\n    /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */\n    let route = requestedRoute\n\n    try {\n      let existingInfo: PrivateRouteInfo | undefined = this.components[route]\n      if (routeProps.shallow && existingInfo && this.route === route) {\n        return existingInfo\n      }\n\n      const handleCancelled = getCancelledHandler({ route, router: this })\n\n      if (hasMiddleware) {\n        existingInfo = undefined\n      }\n\n      let cachedRouteInfo =\n        existingInfo &&\n        !('initial' in existingInfo) &&\n        process.env.NODE_ENV !== 'development'\n          ? existingInfo\n          : undefined\n\n      const isBackground = isQueryUpdating\n      const fetchNextDataParams: FetchNextDataParams = {\n        dataHref: this.pageLoader.getDataHref({\n          href: formatWithValidation({ pathname, query }),\n          skipInterpolation: true,\n          asPath: isNotFound ? '/404' : resolvedAs,\n          locale,\n        }),\n        hasMiddleware: true,\n        isServerRender: this.isSsr,\n        parseJSON: true,\n        inflightCache: isBackground ? this.sbc : this.sdc,\n        persistCache: !isPreview,\n        isPrefetch: false,\n        unstable_skipClientCache,\n        isBackground,\n      }\n\n      let data:\n        | WithMiddlewareEffectsOutput\n        | (Pick<WithMiddlewareEffectsOutput, 'json'> &\n            Omit<Partial<WithMiddlewareEffectsOutput>, 'json'>)\n        | null =\n        isQueryUpdating && !isMiddlewareRewrite\n          ? null\n          : await withMiddlewareEffects({\n              fetchData: () => fetchNextData(fetchNextDataParams),\n              asPath: isNotFound ? '/404' : resolvedAs,\n              locale: locale,\n              router: this,\n            }).catch((err) => {\n              // we don't hard error during query updating\n              // as it's un-necessary and doesn't need to be fatal\n              // unless it is a fallback route and the props can't\n              // be loaded\n              if (isQueryUpdating) {\n                return null\n              }\n              throw err\n            })\n\n      // when rendering error routes we don't apply middleware\n      // effects\n      if (data && (pathname === '/_error' || pathname === '/404')) {\n        data.effect = undefined\n      }\n\n      if (isQueryUpdating) {\n        if (!data) {\n          data = { json: self.__NEXT_DATA__.props }\n        } else {\n          data.json = self.__NEXT_DATA__.props\n        }\n      }\n\n      handleCancelled()\n\n      if (\n        data?.effect?.type === 'redirect-internal' ||\n        data?.effect?.type === 'redirect-external'\n      ) {\n        return data.effect\n      }\n\n      if (data?.effect?.type === 'rewrite') {\n        const resolvedRoute = removeTrailingSlash(data.effect.resolvedHref)\n        const pages = await this.pageLoader.getPageList()\n\n        // during query updating the page must match although during\n        // client-transition a redirect that doesn't match a page\n        // can be returned and this should trigger a hard navigation\n        // which is valid for incremental migration\n        if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n          route = resolvedRoute\n          pathname = data.effect.resolvedHref\n          query = { ...query, ...data.effect.parsedAs.query }\n          resolvedAs = removeBasePath(\n            normalizeLocalePath(data.effect.parsedAs.pathname, this.locales)\n              .pathname\n          )\n\n          // Check again the cache with the new destination.\n          existingInfo = this.components[route]\n          if (\n            routeProps.shallow &&\n            existingInfo &&\n            this.route === route &&\n            !hasMiddleware\n          ) {\n            // If we have a match with the current route due to rewrite,\n            // we can copy the existing information to the rewritten one.\n            // Then, we return the information along with the matched route.\n            return { ...existingInfo, route }\n          }\n        }\n      }\n\n      if (isAPIRoute(route)) {\n        handleHardNavigation({ url: as, router: this })\n        return new Promise<never>(() => {})\n      }\n\n      const routeInfo =\n        cachedRouteInfo ||\n        (await this.fetchComponent(route).then<CompletePrivateRouteInfo>(\n          (res) => ({\n            Component: res.page,\n            styleSheets: res.styleSheets,\n            __N_SSG: res.mod.__N_SSG,\n            __N_SSP: res.mod.__N_SSP,\n          })\n        ))\n\n      if (process.env.NODE_ENV !== 'production') {\n        const { isValidElementType } =\n          require('next/dist/compiled/react-is') as typeof import('next/dist/compiled/react-is')\n        if (!isValidElementType(routeInfo.Component)) {\n          throw new Error(\n            `The default export is not a React Component in page: \"${pathname}\"`\n          )\n        }\n      }\n      const wasBailedPrefetch = data?.response?.headers.get('x-middleware-skip')\n\n      const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP\n\n      // For non-SSG prefetches that bailed before sending data\n      // we clear the cache to fetch full response\n      if (wasBailedPrefetch && data?.dataHref) {\n        delete this.sdc[data.dataHref]\n      }\n\n      const { props, cacheKey } = await this._getData(async () => {\n        if (shouldFetchData) {\n          if (data?.json && !wasBailedPrefetch) {\n            return { cacheKey: data.cacheKey, props: data.json }\n          }\n\n          const dataHref = data?.dataHref\n            ? data.dataHref\n            : this.pageLoader.getDataHref({\n                href: formatWithValidation({ pathname, query }),\n                asPath: resolvedAs,\n                locale,\n              })\n\n          const fetched = await fetchNextData({\n            dataHref,\n            isServerRender: this.isSsr,\n            parseJSON: true,\n            inflightCache: wasBailedPrefetch ? {} : this.sdc,\n            persistCache: !isPreview,\n            isPrefetch: false,\n            unstable_skipClientCache,\n          })\n\n          return {\n            cacheKey: fetched.cacheKey,\n            props: fetched.json || {},\n          }\n        }\n\n        return {\n          headers: {},\n          props: await this.getInitialProps(\n            routeInfo.Component,\n            // we provide AppTree later so this needs to be `any`\n            {\n              pathname,\n              query,\n              asPath: as,\n              locale,\n              locales: this.locales,\n              defaultLocale: this.defaultLocale,\n            } as any\n          ),\n        }\n      })\n\n      // Only bust the data cache for SSP routes although\n      // middleware can skip cache per request with\n      // x-middleware-cache: no-cache as well\n      if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n        delete this.sdc[cacheKey]\n      }\n\n      // we kick off a HEAD request in the background\n      // when a non-prefetch request is made to signal revalidation\n      if (\n        !this.isPreview &&\n        routeInfo.__N_SSG &&\n        process.env.NODE_ENV !== 'development' &&\n        !isQueryUpdating\n      ) {\n        fetchNextData(\n          Object.assign({}, fetchNextDataParams, {\n            isBackground: true,\n            persistCache: false,\n            inflightCache: this.sbc,\n          })\n        ).catch(() => {})\n      }\n\n      props.pageProps = Object.assign({}, props.pageProps)\n      routeInfo.props = props\n      routeInfo.route = route\n      routeInfo.query = query\n      routeInfo.resolvedAs = resolvedAs\n      this.components[route] = routeInfo\n\n      return routeInfo\n    } catch (err) {\n      return this.handleRouteInfoError(\n        getProperError(err),\n        pathname,\n        query,\n        as,\n        routeProps\n      )\n    }\n  }\n\n  private set(\n    state: typeof this.state,\n    data: PrivateRouteInfo,\n    resetScroll: { x: number; y: number } | null\n  ): Promise<void> {\n    this.state = state\n\n    return this.sub(\n      data,\n      this.components['/_app'].Component as AppComponent,\n      resetScroll\n    )\n  }\n\n  /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */\n  beforePopState(cb: BeforePopStateCallback) {\n    this._bps = cb\n  }\n\n  onlyAHashChange(as: string): boolean {\n    if (!this.asPath) return false\n    const [oldUrlNoHash, oldHash] = this.asPath.split('#', 2)\n    const [newUrlNoHash, newHash] = as.split('#', 2)\n\n    // Makes sure we scroll to the provided hash if the url/hash are the same\n    if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n      return true\n    }\n\n    // If the urls are change, there's more than a hash change\n    if (oldUrlNoHash !== newUrlNoHash) {\n      return false\n    }\n\n    // If the hash has changed, then it's a hash only change.\n    // This check is necessary to handle both the enter and\n    // leave hash === '' cases. The identity case falls through\n    // and is treated as a next reload.\n    return oldHash !== newHash\n  }\n\n  scrollToHash(as: string): void {\n    const [, hash = ''] = as.split('#', 2)\n\n    disableSmoothScrollDuringRouteTransition(\n      () => {\n        // Scroll to top if the hash is just `#` with no value or `#top`\n        // To mirror browsers\n        if (hash === '' || hash === 'top') {\n          window.scrollTo(0, 0)\n          return\n        }\n\n        // Decode hash to make non-latin anchor works.\n        const rawHash = decodeURIComponent(hash)\n        // First we check if the element by id is found\n        const idEl = document.getElementById(rawHash)\n        if (idEl) {\n          idEl.scrollIntoView()\n          return\n        }\n        // If there's no element with the id, we check the `name` property\n        // To mirror browsers\n        const nameEl = document.getElementsByName(rawHash)[0]\n        if (nameEl) {\n          nameEl.scrollIntoView()\n        }\n      },\n      {\n        onlyHashChange: this.onlyAHashChange(as),\n      }\n    )\n  }\n\n  urlIsNew(asPath: string): boolean {\n    return this.asPath !== asPath\n  }\n\n  /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */\n  async prefetch(\n    url: string,\n    asPath: string = url,\n    options: PrefetchOptions = {}\n  ): Promise<void> {\n    // Prefetch is not supported in development mode because it would trigger on-demand-entries\n    if (process.env.NODE_ENV !== 'production') {\n      return\n    }\n\n    if (typeof window !== 'undefined' && isBot(window.navigator.userAgent)) {\n      // No prefetches for bots that render the link since they are typically navigating\n      // links via the equivalent of a hard navigation and hence never utilize these\n      // prefetches.\n      return\n    }\n    let parsed = parseRelativeUrl(url)\n    const urlPathname = parsed.pathname\n\n    let { pathname, query } = parsed\n    const originalPathname = pathname\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      if (options.locale === false) {\n        pathname = normalizeLocalePath!(pathname, this.locales).pathname\n        parsed.pathname = pathname\n        url = formatWithValidation(parsed)\n\n        let parsedAs = parseRelativeUrl(asPath)\n        const localePathResult = normalizeLocalePath!(\n          parsedAs.pathname,\n          this.locales\n        )\n        parsedAs.pathname = localePathResult.pathname\n        options.locale = localePathResult.detectedLocale || this.defaultLocale\n        asPath = formatWithValidation(parsedAs)\n      }\n    }\n\n    const pages = await this.pageLoader.getPageList()\n    let resolvedAs = asPath\n\n    const locale =\n      typeof options.locale !== 'undefined'\n        ? options.locale || undefined\n        : this.locale\n\n    const isMiddlewareMatch = await matchesMiddleware({\n      asPath: asPath,\n      locale: locale,\n      router: this,\n    })\n\n    if (process.env.__NEXT_HAS_REWRITES && asPath.startsWith('/')) {\n      let rewrites: any\n      ;({ __rewrites: rewrites } = await getClientBuildManifest())\n\n      const rewritesResult = resolveRewrites(\n        addBasePath(addLocale(asPath, this.locale), true),\n        pages,\n        rewrites,\n        parsed.query,\n        (p: string) => resolveDynamicRoute(p, pages),\n        this.locales\n      )\n\n      if (rewritesResult.externalDest) {\n        return\n      }\n\n      if (!isMiddlewareMatch) {\n        resolvedAs = removeLocale(\n          removeBasePath(rewritesResult.asPath),\n          this.locale\n        )\n      }\n\n      if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n        // if this directly matches a page we need to update the href to\n        // allow the correct page chunk to be loaded\n        pathname = rewritesResult.resolvedHref\n        parsed.pathname = pathname\n\n        if (!isMiddlewareMatch) {\n          url = formatWithValidation(parsed)\n        }\n      }\n    }\n    parsed.pathname = resolveDynamicRoute(parsed.pathname, pages)\n\n    if (isDynamicRoute(parsed.pathname)) {\n      pathname = parsed.pathname\n      parsed.pathname = pathname\n      Object.assign(\n        query,\n        getRouteMatcher(getRouteRegex(parsed.pathname))(\n          parsePath(asPath).pathname\n        ) || {}\n      )\n\n      if (!isMiddlewareMatch) {\n        url = formatWithValidation(parsed)\n      }\n    }\n\n    const data =\n      process.env.__NEXT_MIDDLEWARE_PREFETCH === 'strict'\n        ? null\n        : await withMiddlewareEffects({\n            fetchData: () =>\n              fetchNextData({\n                dataHref: this.pageLoader.getDataHref({\n                  href: formatWithValidation({\n                    pathname: originalPathname,\n                    query,\n                  }),\n                  skipInterpolation: true,\n                  asPath: resolvedAs,\n                  locale,\n                }),\n                hasMiddleware: true,\n                isServerRender: false,\n                parseJSON: true,\n                inflightCache: this.sdc,\n                persistCache: !this.isPreview,\n                isPrefetch: true,\n              }),\n            asPath: asPath,\n            locale: locale,\n            router: this,\n          })\n\n    /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */\n    if (data?.effect.type === 'rewrite') {\n      parsed.pathname = data.effect.resolvedHref\n      pathname = data.effect.resolvedHref\n      query = { ...query, ...data.effect.parsedAs.query }\n      resolvedAs = data.effect.parsedAs.pathname\n      url = formatWithValidation(parsed)\n    }\n\n    /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */\n    if (data?.effect.type === 'redirect-external') {\n      return\n    }\n\n    const route = removeTrailingSlash(pathname)\n\n    if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n      this.components[urlPathname] = { __appRouter: true } as any\n    }\n\n    await Promise.all([\n      this.pageLoader._isSsg(route).then((isSsg) => {\n        return isSsg\n          ? fetchNextData({\n              dataHref: data?.json\n                ? data?.dataHref\n                : this.pageLoader.getDataHref({\n                    href: url,\n                    asPath: resolvedAs,\n                    locale: locale,\n                  }),\n              isServerRender: false,\n              parseJSON: true,\n              inflightCache: this.sdc,\n              persistCache: !this.isPreview,\n              isPrefetch: true,\n              unstable_skipClientCache:\n                options.unstable_skipClientCache ||\n                (options.priority &&\n                  !!process.env.__NEXT_OPTIMISTIC_CLIENT_CACHE),\n            })\n              .then(() => false)\n              .catch(() => false)\n          : false\n      }),\n      this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route),\n    ])\n  }\n\n  async fetchComponent(route: string) {\n    const handleCancelled = getCancelledHandler({ route, router: this })\n\n    try {\n      const componentResult = await this.pageLoader.loadPage(route)\n      handleCancelled()\n\n      return componentResult\n    } catch (err) {\n      handleCancelled()\n      throw err\n    }\n  }\n\n  _getData<T>(fn: () => Promise<T>): Promise<T> {\n    let cancelled = false\n    const cancel = () => {\n      cancelled = true\n    }\n    this.clc = cancel\n    return fn().then((data) => {\n      if (cancel === this.clc) {\n        this.clc = null\n      }\n\n      if (cancelled) {\n        const err: any = new Error('Loading initial props cancelled')\n        err.cancelled = true\n        throw err\n      }\n\n      return data\n    })\n  }\n\n  getInitialProps(\n    Component: ComponentType,\n    ctx: NextPageContext\n  ): Promise<Record<string, any>> {\n    const { Component: App } = this.components['/_app']\n    const AppTree = this._wrapApp(App as AppComponent)\n    ctx.AppTree = AppTree\n    return loadGetInitialProps<AppContextType<Router>>(App, {\n      AppTree,\n      Component,\n      router: this,\n      ctx,\n    })\n  }\n\n  get route(): string {\n    return this.state.route\n  }\n\n  get pathname(): string {\n    return this.state.pathname\n  }\n\n  get query(): ParsedUrlQuery {\n    return this.state.query\n  }\n\n  get asPath(): string {\n    return this.state.asPath\n  }\n\n  get locale(): string | undefined {\n    return this.state.locale\n  }\n\n  get isFallback(): boolean {\n    return this.state.isFallback\n  }\n\n  get isPreview(): boolean {\n    return this.state.isPreview\n  }\n}\n"], "names": ["create<PERSON><PERSON>", "Router", "matchesMiddleware", "resolveRewrites", "process", "env", "__NEXT_HAS_REWRITES", "require", "default", "buildCancellationError", "Object", "assign", "Error", "cancelled", "options", "matchers", "Promise", "resolve", "router", "page<PERSON><PERSON>der", "getMiddleware", "pathname", "asPathname", "parsePath", "<PERSON><PERSON><PERSON>", "cleanedAs", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "asWithBasePathAndLocale", "addBasePath", "addLocale", "locale", "some", "m", "RegExp", "regexp", "test", "strip<PERSON><PERSON>in", "url", "origin", "getLocationOrigin", "startsWith", "substring", "length", "prepareUrlAs", "as", "resolvedHref", "resolvedAs", "resolveHref", "hrefWasAbsolute", "asWasAbsolute", "preparedUrl", "preparedAs", "resolveDynamicRoute", "pages", "cleanPathname", "removeTrailingSlash", "denormalizePagePath", "includes", "page", "isDynamicRoute", "getRouteRegex", "re", "getMiddlewareData", "source", "response", "nextConfig", "basePath", "i18n", "locales", "trailingSlash", "Boolean", "__NEXT_TRAILING_SLASH", "rewriteHeader", "headers", "get", "rewriteTarget", "<PERSON><PERSON><PERSON>", "MATCHED_PATH_HEADER", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "parsedRewriteTarget", "parseRelativeUrl", "pathnameInfo", "getNextPathnameInfo", "parseData", "fsPathname", "all", "getPageList", "getClientBuildManifest", "then", "__rewrites", "rewrites", "normalizeLocalePath", "parsedSource", "undefined", "result", "query", "path", "matchedPage", "parsedAs", "resolvedPathname", "matches", "getRouteMatcher", "type", "src", "formatNextPathnameInfo", "defaultLocale", "buildId", "destination", "hash", "redirectTarget", "newAs", "newUrl", "withMiddlewareEffects", "fetchData", "data", "effect", "dataHref", "json", "text", "cache<PERSON>ey", "manualScrollRestoration", "__NEXT_SCROLL_RESTORATION", "window", "history", "v", "sessionStorage", "setItem", "removeItem", "n", "SSG_DATA_NOT_FOUND", "Symbol", "fetchRetry", "attempts", "fetch", "credentials", "method", "ok", "status", "tryToParseAsJSON", "JSON", "parse", "error", "fetchNextData", "inflightCache", "isPrefetch", "hasMiddleware", "isServerRender", "parseJSON", "persistCache", "isBackground", "unstable_skipClientCache", "href", "URL", "location", "getData", "params", "purpose", "NEXT_DEPLOYMENT_ID", "notFound", "<PERSON><PERSON><PERSON><PERSON>", "NODE_ENV", "catch", "err", "message", "Math", "random", "toString", "slice", "handleHardNavigation", "getCancelledHandler", "route", "cancel", "clc", "handleCancelled", "reload", "back", "forward", "push", "_key", "stringify", "x", "self", "pageXOffset", "y", "pageYOffset", "change", "replace", "_bfl", "skipNavigate", "__NEXT_CLIENT_ROUTER_FILTER_ENABLED", "_bfl_s", "_bfl_d", "<PERSON><PERSON><PERSON><PERSON>", "staticFilterData", "dynamicFilterData", "__routerFilterStatic", "__routerFilterDynamic", "console", "routerFilterSValue", "__NEXT_CLIENT_ROUTER_S_FILTER", "routerFilterDValue", "__NEXT_CLIENT_ROUTER_D_FILTER", "numHashes", "numItems", "errorRate", "import", "matchesBflStatic", "matchesBflDynamic", "pathsToCheck", "curAs", "allowMatchCurrent", "asNoSlash", "asNoSlashLocale", "contains", "normalizedAS", "curAs<PERSON><PERSON>s", "split", "i", "currentPart", "join", "forcedScroll", "isLocalURL", "isQueryUpdating", "_h", "shallow", "shouldResolveHref", "_shouldResolveHref", "nextState", "state", "readyStateChange", "isReady", "isSsr", "prevLocale", "__NEXT_I18N_SUPPORT", "localePathResult", "detectedLocale", "formatWithValidation", "didNavigate", "detectedDomain", "detectDomainLocale", "domainLocales", "isLocaleDomain", "hostname", "domain", "asNoBasePath", "http", "ST", "performance", "mark", "scroll", "routeProps", "_inFlightRoute", "events", "emit", "removeLocale", "localeChange", "onlyAHashChange", "changeState", "scrollToHash", "set", "components", "isError", "parsed", "urlIsNew", "parsedAsPathname", "__appRouter", "isMiddlewareRewrite", "isMiddlewareMatch", "rewritesResult", "p", "externalDest", "routeMatch", "routeRegex", "shouldInterpolate", "interpolatedAs", "interpolateAs", "missingParams", "keys", "groups", "filter", "param", "optional", "warn", "omit", "isErrorRoute", "routeInfo", "getRouteInfo", "isPreview", "<PERSON><PERSON><PERSON><PERSON>", "cleanedParsedPathname", "for<PERSON>ach", "key", "prefixedAs", "rewriteAs", "localeResult", "cur<PERSON><PERSON>eMatch", "component", "Component", "unstable_scriptLoader", "scripts", "concat", "script", "handleClientScriptLoad", "props", "__N_SSG", "__N_SSP", "pageProps", "__N_REDIRECT", "__N_REDIRECT_BASE_PATH", "parsedHref", "__N_PREVIEW", "notFoundRoute", "fetchComponent", "_", "isNotFound", "__NEXT_DATA__", "statusCode", "isValidShallowRoute", "shouldScroll", "resetScroll", "upcomingScrollState", "upcomingRouterState", "canSkipUpdating", "compareRouterStates", "e", "document", "documentElement", "lang", "hashRegex", "getURL", "_shallow", "__N", "handleRouteInfoError", "loadErrorFail", "isAssetError", "styleSheets", "getInitialProps", "gipErr", "routeInfoErr", "requestedRoute", "existingInfo", "cachedRouteInfo", "fetchNextDataParams", "getDataHref", "skipInterpolation", "sbc", "sdc", "resolvedRoute", "isAPIRoute", "res", "mod", "isValidElementType", "wasBailedPrefetch", "shouldFetchData", "_getData", "fetched", "getProperError", "sub", "beforePopState", "cb", "_bps", "oldUrlNoHash", "oldHash", "newUrlNoHash", "newHash", "disableSmoothScrollDuringRouteTransition", "scrollTo", "rawHash", "decodeURIComponent", "idEl", "getElementById", "scrollIntoView", "nameEl", "getElementsByName", "onlyHashChange", "prefetch", "isBot", "navigator", "userAgent", "urlPathname", "originalPathname", "__NEXT_MIDDLEWARE_PREFETCH", "_isSsg", "isSsg", "priority", "__NEXT_OPTIMISTIC_CLIENT_CACHE", "componentResult", "loadPage", "fn", "ctx", "App", "AppTree", "_wrapApp", "loadGetInitialProps", "constructor", "initialProps", "wrapApp", "subscription", "isFirstPopStateEvent", "onPopState", "__NA", "getItem", "initial", "autoExportDynamic", "autoExport", "__NEXT_ROUTER_BASEPATH", "gssp", "gip", "isExperimentalCompile", "appGip", "gsp", "search", "_initialMatchesMiddlewarePromise", "addEventListener", "scrollRestoration", "mitt"], "mappings": "AAAA,4BAA4B;AAu+BpBI,QAAQC,GAAG,CAACqH,yBAAyB,EAAE;;;;;;;;;;;;;;;;;IAzX/B1H,SAAS,EAAA;eAATA;;;eAiDKC;;IA9jBCC,iBAAiB,EAAA;eAAjBA;;;;;qCAvFc;6BAK7B;wBACgC;mEACC;qCACJ;qCACA;+DACnB;uBACkD;2BACpC;kCACE;8BACD;4BACF;2BACO;oCACF;2BACT;2BACA;8BACG;gCACE;6BACH;6BACA;6BACA;4BACD;qCACS;wCACG;+BACH;4BACT;uBACL;sBACD;+BACS;qCAC2B;2BAErB;AAEpC,IAAIC;AACJ,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,EAAE;;AAoCrC,SAASG;IACP,OAAOC,OAAOC,MAAM,CAAC,OAAA,cAA4B,CAA5B,IAAIC,MAAM,oBAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA2B,IAAG;QACjDC,WAAW;IACb;AACF;AASO,eAAeX,kBACpBY,OAAkC;IAElC,MAAMC,WAAW,MAAMC,QAAQC,OAAO,CACpCH,QAAQI,MAAM,CAACC,UAAU,CAACC,aAAa;IAEzC,IAAI,CAACL,UAAU,OAAO;IAEtB,MAAM,EAAEM,UAAUC,UAAU,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACT,QAAQU,MAAM;IACzD,6FAA6F;IAC7F,MAAMC,YAAYC,CAAAA,GAAAA,aAAAA,WAAW,EAACJ,cAC1BK,CAAAA,GAAAA,gBAAAA,cAAc,EAACL,cACfA;IACJ,MAAMM,0BAA0BC,CAAAA,GAAAA,aAAAA,WAAW,EACzCC,CAAAA,GAAAA,WAAAA,SAAS,EAACL,WAAWX,QAAQiB,MAAM;IAGrC,2EAA2E;IAC3E,uEAAuE;IACvE,OAAOhB,SAASiB,IAAI,CAAC,CAACC,IACpB,IAAIC,OAAOD,EAAEE,MAAM,EAAEC,IAAI,CAACR;AAE9B;AAEA,SAASS,YAAYC,GAAW;IAC9B,MAAMC,SAASC,CAAAA,GAAAA,OAAAA,iBAAiB;IAEhC,OAAOF,IAAIG,UAAU,CAACF,UAAUD,IAAII,SAAS,CAACH,OAAOI,MAAM,IAAIL;AACjE;AAEA,SAASM,aAAa1B,MAAkB,EAAEoB,GAAQ,EAAEO,EAAQ;IAC1D,sDAAsD;IACtD,kDAAkD;IAClD,IAAI,CAACC,cAAcC,WAAW,GAAGC,CAAAA,GAAAA,aAAAA,WAAW,EAAC9B,QAAQoB,KAAK;IAC1D,MAAMC,SAASC,CAAAA,GAAAA,OAAAA,iBAAiB;IAChC,MAAMS,kBAAkBH,aAAaL,UAAU,CAACF;IAChD,MAAMW,gBAAgBH,cAAcA,WAAWN,UAAU,CAACF;IAE1DO,eAAeT,YAAYS;IAC3BC,aAAaA,aAAaV,YAAYU,cAAcA;IAEpD,MAAMI,cAAcF,kBAAkBH,eAAejB,CAAAA,GAAAA,aAAAA,WAAW,EAACiB;IACjE,MAAMM,aAAaP,KACfR,YAAYW,CAAAA,GAAAA,aAAAA,WAAW,EAAC9B,QAAQ2B,OAChCE,cAAcD;IAElB,OAAO;QACLR,KAAKa;QACLN,IAAIK,gBAAgBE,aAAavB,CAAAA,GAAAA,aAAAA,WAAW,EAACuB;IAC/C;AACF;AAEA,SAASC,oBAAoBhC,QAAgB,EAAEiC,KAAe;IAC5D,MAAMC,gBAAgBC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACpC;IAC9D,IAAIkC,kBAAkB,UAAUA,kBAAkB,WAAW;QAC3D,OAAOlC;IACT;IAEA,2CAA2C;IAC3C,IAAI,CAACiC,MAAMI,QAAQ,CAACH,gBAAgB;QAClC,iDAAiD;QACjDD,MAAMtB,IAAI,CAAC,CAAC2B;YACV,IAAIC,CAAAA,GAAAA,WAAAA,cAAc,EAACD,SAASE,CAAAA,GAAAA,YAAAA,aAAa,EAACF,MAAMG,EAAE,CAAC1B,IAAI,CAACmB,gBAAgB;gBACtElC,WAAWsC;gBACX,OAAO;YACT;QACF;IACF;IACA,OAAOH,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACnC;AAC7B;AAEA,SAAS0C,kBACPC,MAAc,EACdC,QAAkB,EAClBnD,OAAkC;IAElC,MAAMoD,aAAa;QACjBC,UAAUrD,QAAQI,MAAM,CAACiD,QAAQ;QACjCC,MAAM;YAAEC,SAASvD,QAAQI,MAAM,CAACmD,OAAO;QAAC;QACxCC,eAAeC,QAAQnE,QAAQC,GAAG,CAACmE,qBAAqB;IAC1D;IACA,MAAMC,gBAAgBR,SAASS,OAAO,CAACC,GAAG,CAAC;IAE3C,IAAIC,gBACFH,iBAAiBR,SAASS,OAAO,CAACC,GAAG,CAAC;IAExC,MAAME,cAAcZ,SAASS,OAAO,CAACC,GAAG,CAACG,WAAAA,mBAAmB;IAE5D,IACED,eACA,CAACD,iBACD,CAACC,YAAYnB,QAAQ,CAAC,2BACtB,CAACmB,YAAYnB,QAAQ,CAAC,cACtB,CAACmB,YAAYnB,QAAQ,CAAC,SACtB;QACA,4DAA4D;QAC5DkB,gBAAgBC;IAClB;IAEA,IAAID,eAAe;QACjB,IACEA,cAAcnC,UAAU,CAAC,QACzBrC,QAAQC,GAAG,CAAC0E,6BACZ,aADsD;YAEtD,MAAMC,sBAAsBC,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACL;YAC7C,MAAMM,eAAeC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACH,oBAAoB3D,QAAQ,EAAE;gBACrE6C;gBACAkB,WAAW;YACb;YAEA,IAAIC,aAAa7B,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC0B,aAAa7D,QAAQ;YAC1D,OAAOL,QAAQsE,GAAG,CAAC;gBACjBxE,QAAQI,MAAM,CAACC,UAAU,CAACoE,WAAW;gBACrCC,CAAAA,GAAAA,aAAAA,sBAAsB;aACvB,EAAEC,IAAI,CAAC,CAAA;oBAAC,CAACnC,OAAO,EAAEoC,YAAYC,QAAQ,EAAE,CAAM,GAAA;gBAC7C,IAAI9C,KAAKf,CAAAA,GAAAA,WAAAA,SAAS,EAACoD,aAAa7D,QAAQ,EAAE6D,aAAanD,MAAM;gBAE7D,IACE6B,CAAAA,GAAAA,WAAAA,cAAc,EAACf,OACd,CAAC4B,iBACAnB,MAAMI,QAAQ,CACZkC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACjE,CAAAA,GAAAA,gBAAAA,cAAc,EAACkB,KAAK/B,QAAQI,MAAM,CAACmD,OAAO,EAC3DhD,QAAQ,GAEf;oBACA,MAAMwE,eAAeV,CAAAA,GAAAA,qBAAAA,mBAAmB,EACtCF,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACjB,QAAQ3C,QAAQ,EACjC;wBACE6C,YAAY9D,QAAQC,GAAG,CAACC,mBAAmB,OACvCwF,0BACA5B;wBACJkB,WAAW;oBACb;oBAGFvC,KAAKhB,CAAAA,GAAAA,aAAAA,WAAW,EAACgE,aAAaxE,QAAQ;oBACtC2D,oBAAoB3D,QAAQ,GAAGwB;gBACjC;gBAEA,IAAIzC,QAAQC,GAAG,CAACC,mBAAmB,EAAE;;qBAe9B,IAAI,CAACgD,MAAMI,QAAQ,CAAC2B,aAAa;oBACtC,MAAMe,mBAAmB/C,oBAAoBgC,YAAY/B;oBAEzD,IAAI8C,qBAAqBf,YAAY;wBACnCA,aAAae;oBACf;gBACF;gBAEA,MAAMtD,eAAe,CAACQ,MAAMI,QAAQ,CAAC2B,cACjChC,oBACEuC,CAAAA,GAAAA,qBAAAA,mBAAmB,EACjBjE,CAAAA,GAAAA,gBAAAA,cAAc,EAACqD,oBAAoB3D,QAAQ,GAC3CP,QAAQI,MAAM,CAACmD,OAAO,EACtBhD,QAAQ,EACViC,SAEF+B;gBAEJ,IAAIzB,CAAAA,GAAAA,WAAAA,cAAc,EAACd,eAAe;oBAChC,MAAMuD,UAAUC,CAAAA,GAAAA,cAAAA,eAAe,EAACzC,CAAAA,GAAAA,YAAAA,aAAa,EAACf,eAAeD;oBAC7DnC,OAAOC,MAAM,CAACqE,oBAAoBgB,KAAK,EAAEK,WAAW,CAAC;gBACvD;gBAEA,OAAO;oBACLE,MAAM;oBACNJ,UAAUnB;oBACVlC;gBACF;YACF;QACF;QACA,MAAM0D,MAAMjF,CAAAA,GAAAA,WAAAA,SAAS,EAACyC;QACtB,MAAM3C,WAAWoF,CAAAA,GAAAA,wBAAAA,sBAAsB,EAAC;YACtC,GAAGtB,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACqB,IAAInF,QAAQ,EAAE;gBAAE6C;gBAAYkB,WAAW;YAAK,EAAE;YACrEsB,eAAe5F,QAAQI,MAAM,CAACwF,aAAa;YAC3CC,SAAS;QACX;QAEA,OAAO3F,QAAQC,OAAO,CAAC;YACrBsF,MAAM;YACNK,aAAc,KAAEvF,WAAWmF,IAAIR,KAAK,GAAGQ,IAAIK,IAAI;QACjD;IACF;IAEA,MAAMC,iBAAiB7C,SAASS,OAAO,CAACC,GAAG,CAAC;IAE5C,IAAImC,gBAAgB;QAClB,IAAIA,eAAerE,UAAU,CAAC,MAAM;YAClC,MAAM+D,MAAMjF,CAAAA,GAAAA,WAAAA,SAAS,EAACuF;YACtB,MAAMzF,WAAWoF,CAAAA,GAAAA,wBAAAA,sBAAsB,EAAC;gBACtC,GAAGtB,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACqB,IAAInF,QAAQ,EAAE;oBAAE6C;oBAAYkB,WAAW;gBAAK,EAAE;gBACrEsB,eAAe5F,QAAQI,MAAM,CAACwF,aAAa;gBAC3CC,SAAS;YACX;YAEA,OAAO3F,QAAQC,OAAO,CAAC;gBACrBsF,MAAM;gBACNQ,OAAQ,KAAE1F,WAAWmF,IAAIR,KAAK,GAAGQ,IAAIK,IAAI;gBACzCG,QAAS,KAAE3F,WAAWmF,IAAIR,KAAK,GAAGQ,IAAIK,IAAI;YAC5C;QACF;QAEA,OAAO7F,QAAQC,OAAO,CAAC;YACrBsF,MAAM;YACNK,aAAaE;QACf;IACF;IAEA,OAAO9F,QAAQC,OAAO,CAAC;QAAEsF,MAAM;IAAgB;AACjD;AAMA,eAAeU,sBACbnG,OAAkC;IAElC,MAAMuF,UAAU,MAAMnG,kBAAkBY;IACxC,IAAI,CAACuF,WAAW,CAACvF,QAAQoG,SAAS,EAAE;QAClC,OAAO;IACT;IAEA,MAAMC,OAAO,MAAMrG,QAAQoG,SAAS;IAEpC,MAAME,SAAS,MAAMrD,kBAAkBoD,KAAKE,QAAQ,EAAEF,KAAKlD,QAAQ,EAAEnD;IAErE,OAAO;QACLuG,UAAUF,KAAKE,QAAQ;QACvBC,MAAMH,KAAKG,IAAI;QACfrD,UAAUkD,KAAKlD,QAAQ;QACvBsD,MAAMJ,KAAKI,IAAI;QACfC,UAAUL,KAAKK,QAAQ;QACvBJ;IACF;AACF;AAyEA,MAAMK,0BACJrH,QAAQC,GAAG,CAACqH,yBAAyB,MACrC,OAAOC,WAAW,eAClB,uBAAuBA,OAAOC,OAAO,IACrC,CAAC,CAAE;IACD,IAAI;QACF,IAAIC,IAAI;QACR,wCAAwC;QACxC,OAAOC,eAAeC,OAAO,CAACF,GAAGA,IAAIC,eAAeE,UAAU,CAACH,IAAI;IACrE,EAAE,OAAOI,GAAG,CAAC;AACf;AAEF,MAAMC,qBAAqBC,OAAO;AAElC,SAASC,WACP9F,GAAW,EACX+F,QAAgB,EAChBvH,OAAgD;IAEhD,OAAOwH,MAAMhG,KAAK;QAChB,sEAAsE;QACtE,yDAAyD;QACzD,EAAE;QACF,oEAAoE;QACpE,YAAY;QACZ,mEAAmE;QACnE,EAAE;QACF,iEAAiE;QACjE,sEAAsE;QACtE,8CAA8C;QAC9C,0CAA0C;QAC1CiG,aAAa;QACbC,QAAQ1H,QAAQ0H,MAAM,IAAI;QAC1B9D,SAAShE,OAAOC,MAAM,CAAC,CAAC,GAAGG,QAAQ4D,OAAO,EAAE;YAC1C,iBAAiB;QACnB;IACF,GAAGe,IAAI,CAAC,CAACxB;QACP,OAAO,CAACA,SAASwE,EAAE,IAAIJ,WAAW,KAAKpE,SAASyE,MAAM,IAAI,MACtDN,WAAW9F,KAAK+F,WAAW,GAAGvH,WAC9BmD;IACN;AACF;AAsBA,SAAS0E,iBAAiBpB,IAAY;IACpC,IAAI;QACF,OAAOqB,KAAKC,KAAK,CAACtB;IACpB,EAAE,OAAOuB,OAAO;QACd,OAAO;IACT;AACF;AAEA,SAASC,cAAc,KAUD;IAVC,IAAA,EACrB1B,QAAQ,EACR2B,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,SAAS,EACTC,YAAY,EACZC,YAAY,EACZC,wBAAwB,EACJ,GAVC;IAWrB,MAAM,EAAEC,MAAMhC,QAAQ,EAAE,GAAG,IAAIiC,IAAIpC,UAAUM,OAAO+B,QAAQ,CAACF,IAAI;IACjE,MAAMG,UAAU,CAACC;YAULA;eATVxB,WAAWf,UAAU8B,iBAAiB,IAAI,GAAG;YAC3CzE,SAAShE,OAAOC,MAAM,CACpB,CAAC,GACDsI,aAAa;gBAAEY,SAAS;YAAW,IAAI,CAAC,GACxCZ,cAAcC,gBAAgB;gBAAE,yBAAyB;YAAI,IAAI,CAAC,GAClE9I,QAAQC,GAAG,CAACyJ,kBAAkB,QAC1B,0BACA,CAAC;YAEPtB,QAAQoB,CAAAA,iBAAAA,UAAAA,OAAAA,KAAAA,IAAAA,OAAQpB,MAAM,KAAA,OAAdoB,iBAAkB;QAC5B,GACGnE,IAAI,CAAC,CAACxB;YACL,IAAIA,SAASwE,EAAE,IAAImB,CAAAA,UAAAA,OAAAA,KAAAA,IAAAA,OAAQpB,MAAM,MAAK,QAAQ;gBAC5C,OAAO;oBAAEnB;oBAAUpD;oBAAUsD,MAAM;oBAAID,MAAM,CAAC;oBAAGE;gBAAS;YAC5D;YAEA,OAAOvD,SAASsD,IAAI,GAAG9B,IAAI,CAAC,CAAC8B;gBAC3B,IAAI,CAACtD,SAASwE,EAAE,EAAE;oBAChB;;;;;aAKC,GACD,IACES,iBACA;wBAAC;wBAAK;wBAAK;wBAAK;qBAAI,CAACxF,QAAQ,CAACO,SAASyE,MAAM,GAC7C;wBACA,OAAO;4BAAErB;4BAAUpD;4BAAUsD;4BAAMD,MAAM,CAAC;4BAAGE;wBAAS;oBACxD;oBAEA,IAAIvD,SAASyE,MAAM,KAAK,KAAK;4BACvBC;wBAAJ,IAAA,CAAIA,oBAAAA,iBAAiBpB,KAAAA,KAAAA,OAAAA,KAAAA,IAAjBoB,kBAAwBoB,QAAQ,EAAE;4BACpC,OAAO;gCACL1C;gCACAC,MAAM;oCAAEyC,UAAU7B;gCAAmB;gCACrCjE;gCACAsD;gCACAC;4BACF;wBACF;oBACF;oBAEA,MAAMsB,QAAQ,OAAA,cAAwC,CAAxC,IAAIlI,MAAO,gCAAX,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuC;oBAErD;;;;aAIC,GACD,IAAI,CAACuI,gBAAgB;wBACnBa,CAAAA,GAAAA,aAAAA,cAAc,EAAClB;oBACjB;oBAEA,MAAMA;gBACR;gBAEA,OAAO;oBACLzB;oBACAC,MAAM8B,YAAYT,iBAAiBpB,QAAQ;oBAC3CtD;oBACAsD;oBACAC;gBACF;YACF;QACF,GACC/B,IAAI,CAAC,CAAC0B;YACL,IACE,CAACkC,gBACDjJ,QAAQC,GAAG,CAAC4J,QAAQ,gCAAK,gBACzB9C,KAAKlD,QAAQ,CAACS,OAAO,CAACC,GAAG,CAAC,0BAA0B,YACpD;gBACA,OAAOqE,aAAa,CAACxB,SAAS;YAChC;YACA,OAAOL;QACT,GACC+C,KAAK,CAAC,CAACC;YACN,IAAI,CAACZ,0BAA0B;gBAC7B,OAAOP,aAAa,CAACxB,SAAS;YAChC;YACA,IACE,AACA2C,IAAIC,KADK,EACE,KAAK,qBAChB,UAAU;YACVD,IAAIC,OAAO,KAAK,qDAChB,SAAS;YACTD,IAAIC,OAAO,KAAK,eAChB;gBACAJ,CAAAA,GAAAA,aAAAA,cAAc,EAACG;YACjB;YACA,MAAMA;QACR;;IAEJ,+CAA+C;IAC/C,gDAAgD;IAChD,0DAA0D;IAC1D,2DAA2D;IAC3D,IAAIZ,4BAA4BF,cAAc;QAC5C,OAAOM,QAAQ,CAAC,GAAGlE,IAAI,CAAC,CAAC0B;YACvB,IAAIA,KAAKlD,QAAQ,CAACS,OAAO,CAACC,GAAG,CAAC,0BAA0B,YAAY;gBAClE,8CAA8C;gBAC9CqE,aAAa,CAACxB,SAAS,GAAGxG,QAAQC,OAAO,CAACkG;YAC5C;YAEA,OAAOA;QACT;IACF;IAEA,IAAI6B,aAAa,CAACxB,SAAS,KAAK1B,WAAW;QACzC,OAAOkD,aAAa,CAACxB,SAAS;IAChC;IACA,OAAQwB,aAAa,CAACxB,SAAS,GAAGmC,QAChCL,eAAe;QAAEd,QAAQ;IAAO,IAAI,CAAC;AAEzC;AAMO,SAASxI;IACd,OAAOqK,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C;AAEA,SAASC,qBAAqB,KAM7B;IAN6B,IAAA,EAC5BnI,GAAG,EACHpB,MAAM,EAIP,GAN6B;IAO5B,wDAAwD;IACxD,kDAAkD;IAClD,IAAIoB,QAAQT,CAAAA,GAAAA,aAAAA,WAAW,EAACC,CAAAA,GAAAA,WAAAA,SAAS,EAACZ,OAAOM,MAAM,EAAEN,OAAOa,MAAM,IAAI;QAChE,MAAM,OAAA,cAEL,CAFK,IAAInB,MACP,2DAAwD0B,MAAI,MAAGoH,SAASF,IAAI,GADzE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA7B,OAAO+B,QAAQ,CAACF,IAAI,GAAGlH;AACzB;AAEA,MAAMoI,sBAAsB,CAAA;QAAC,EAC3BC,KAAK,EACLzJ,MAAM,EAIP,GAAA;IACC,IAAIL,YAAY;IAChB,MAAM+J,SAAU1J,OAAO2J,GAAG,GAAG;QAC3BhK,YAAY;IACd;IAEA,MAAMiK,kBAAkB;QACtB,IAAIjK,WAAW;YACb,MAAMiI,QAAa,OAAA,cAElB,CAFkB,IAAIlI,MACpB,0CAAuC+J,QAAM,MAD7B,qBAAA;uBAAA;4BAAA;8BAAA;YAEnB;YACA7B,MAAMjI,SAAS,GAAG;YAClB,MAAMiI;QACR;QAEA,IAAI8B,WAAW1J,OAAO2J,GAAG,EAAE;YACzB3J,OAAO2J,GAAG,GAAG;QACf;IACF;IACA,OAAOC;AACT;AAEe,MAAM7K;IA+SnB8K,SAAe;QACbpD,OAAO+B,QAAQ,CAACqB,MAAM;IACxB;IAEA;;GAEC,GACDC,OAAO;QACLrD,OAAOC,OAAO,CAACoD,IAAI;IACrB;IAEA;;GAEC,GACDC,UAAU;QACRtD,OAAOC,OAAO,CAACqD,OAAO;IACxB;IAEA;;;;;GAKC,GACDC,KAAK5I,GAAQ,EAAEO,EAAQ,EAAE/B,OAA+B,EAAE;QAAjCA,IAAAA,YAAAA,KAAAA,GAAAA,UAA6B,CAAC;QACrD;;;QAaE,CAAA,EAAEwB,GAAG,EAAEO,EAAE,EAAE,GAAGD,aAAa,IAAI,EAAEN,KAAKO,GAAE;QAC1C,OAAO,IAAI,CAAC6I,MAAM,CAAC,aAAapJ,KAAKO,IAAI/B;IAC3C;IAEA;;;;;GAKC,GACD6K,QAAQrJ,GAAQ,EAAEO,EAAQ,EAAE/B,OAA+B,EAAE;QAAjCA,IAAAA,YAAAA,KAAAA,GAAAA,UAA6B,CAAC;;QACtD,CAAA,EAAEwB,GAAG,EAAEO,EAAE,EAAE,GAAGD,aAAa,IAAI,EAAEN,KAAKO,GAAE;QAC1C,OAAO,IAAI,CAAC6I,MAAM,CAAC,gBAAgBpJ,KAAKO,IAAI/B;IAC9C;IAEA,MAAM8K,KACJ/I,EAAU,EACVE,UAAmB,EACnBhB,MAAuB,EACvB8J,YAAsB,EACtB;QACA,IAAIzL,QAAQC,GAAG,CAACyL,wBAAqC,WAAF;YACjD,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;gBAChC,MAAM,EAAEC,WAAW,EAAE,GACnB1L,QAAQ;gBAKV,IAAI2L;gBACJ,IAAIC;gBAEJ,IAAI;;oBACA,CAAA,EACAC,sBAAsBF,gBAAgB,EACtCG,uBAAuBF,iBAAiB,EACzC,GAAI,MAAM3G,CAAAA,GAAAA,aAAAA,sBAAsB,GAGjC;gBACF,EAAE,OAAO2E,KAAK;oBACZ,8CAA8C;oBAC9C,aAAa;oBACbmC,QAAQxD,KAAK,CAACqB;oBACd,IAAI0B,cAAc;wBAChB,OAAO;oBACT;oBACApB,qBAAqB;wBACnBnI,KAAKT,CAAAA,GAAAA,aAAAA,WAAW,EACdC,CAAAA,GAAAA,WAAAA,SAAS,EAACe,IAAId,UAAU,IAAI,CAACA,MAAM,EAAE,IAAI,CAAC2E,aAAa;wBAEzDxF,QAAQ,IAAI;oBACd;oBACA,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;gBAEA,MAAMuL,qBAAqCnM,QAAQC,GAAG,CACnDmM,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEhC,IAAI,CAACN,oBAAoBK,oBAAoB;oBAC3CL,mBAAmBK,qBAAqBA,qBAAqBzG;gBAC/D;gBAEA,MAAM2G,qBAAqCrM,QAAQC,GAAG,CACnDqM,6BAA6B;;;;;;;gBAEhC,IAAI,CAACP,qBAAqBM,oBAAoB;oBAC5CN,oBAAoBM,uCAChBA,qBACA3G;gBACN;gBAEA,IAAIoG,oBAAAA,OAAAA,KAAAA,IAAAA,iBAAkBS,SAAS,EAAE;oBAC/B,IAAI,CAACZ,MAAM,GAAG,IAAIE,YAChBC,iBAAiBU,QAAQ,EACzBV,iBAAiBW,SAAS;oBAE5B,IAAI,CAACd,MAAM,CAACe,MAAM,CAACZ;gBACrB;gBAEA,IAAIC,qBAAAA,OAAAA,KAAAA,IAAAA,kBAAmBQ,SAAS,EAAE;oBAChC,IAAI,CAACX,MAAM,GAAG,IAAIC,YAChBE,kBAAkBS,QAAQ,EAC1BT,kBAAkBU,SAAS;oBAE7B,IAAI,CAACb,MAAM,CAACc,MAAM,CAACX;gBACrB;YACF;YAEA,IAAIY,mBAAmB;YACvB,IAAIC,oBAAoB;YACxB,MAAMC,eACJ;gBAAC;oBAAEpK;gBAAG;gBAAG;oBAAEA,IAAIE;gBAAW;aAAE;YAE9B,KAAK,MAAM,EAAEF,IAAIqK,KAAK,EAAEC,iBAAiB,EAAE,IAAIF,aAAc;gBAC3D,IAAIC,OAAO;oBACT,MAAME,YAAY5J,CAAAA,GAAAA,qBAAAA,mBAAmB,EACnC,IAAIiG,IAAIyD,OAAO,YAAY7L,QAAQ;oBAErC,MAAMgM,kBAAkBxL,CAAAA,GAAAA,aAAAA,WAAW,EACjCC,CAAAA,GAAAA,WAAAA,SAAS,EAACsL,WAAWrL,UAAU,IAAI,CAACA,MAAM;oBAG5C,IACEoL,qBACAC,cACE5J,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAIiG,IAAI,IAAI,CAACjI,MAAM,EAAE,YAAYH,QAAQ,GAC/D;4BAGI,cACA;wBAHJ0L,mBACEA,oBACA,CAAC,CAAA,CAAA,CAAC,eAAA,IAAI,CAAChB,MAAM,KAAA,OAAA,KAAA,IAAX,aAAauB,QAAQ,CAACF,UAAAA,KACxB,CAAC,CAAA,CAAA,CAAC,gBAAA,IAAI,CAACrB,MAAM,KAAA,OAAA,KAAA,IAAX,cAAauB,QAAQ,CAACD,gBAAAA;wBAE1B,KAAK,MAAME,gBAAgB;4BAACH;4BAAWC;yBAAgB,CAAE;4BACvD,sDAAsD;4BACtD,8BAA8B;4BAC9B,MAAMG,aAAaD,aAAaE,KAAK,CAAC;4BACtC,IACE,IAAIC,IAAI,GACR,CAACV,qBAAqBU,IAAIF,WAAW7K,MAAM,GAAG,GAC9C+K,IACA;oCAEmB;gCADnB,MAAMC,cAAcH,WAAWhD,KAAK,CAAC,GAAGkD,GAAGE,IAAI,CAAC;gCAChD,IAAID,eAAAA,CAAAA,CAAe,eAAA,IAAI,CAAC3B,MAAM,KAAA,OAAA,KAAA,IAAX,aAAasB,QAAQ,CAACK,YAAAA,GAAc;oCACrDX,oBAAoB;oCACpB;gCACF;4BACF;wBACF;wBAEA,yDAAyD;wBACzD,oBAAoB;wBACpB,IAAID,oBAAoBC,mBAAmB;4BACzC,IAAInB,cAAc;gCAChB,OAAO;4BACT;4BACApB,qBAAqB;gCACnBnI,KAAKT,CAAAA,GAAAA,aAAAA,WAAW,EACdC,CAAAA,GAAAA,WAAAA,SAAS,EAACe,IAAId,UAAU,IAAI,CAACA,MAAM,EAAE,IAAI,CAAC2E,aAAa;gCAEzDxF,QAAQ,IAAI;4BACd;4BACA,OAAO,IAAIF,QAAQ,KAAO;wBAC5B;oBACF;gBACF;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAc0K,OACZlD,MAAqB,EACrBlG,GAAW,EACXO,EAAU,EACV/B,OAA0B,EAC1B+M,YAAuC,EACrB;YA8Ob;QA7OL,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAACxL,MAAM;YACpBmI,qBAAqB;gBAAEnI;gBAAKpB,QAAQ,IAAI;YAAC;YACzC,OAAO;QACT;QACA,sEAAsE;QACtE,yEAAyE;QACzE,2BAA2B;QAC3B,MAAM6M,kBAAmBjN,QAAgBkN,EAAE,KAAK;QAEhD,IAAI,CAACD,mBAAmB,CAACjN,QAAQmN,OAAO,EAAE;YACxC,MAAM,IAAI,CAACrC,IAAI,CAAC/I,IAAIiD,WAAWhF,QAAQiB,MAAM;QAC/C;QAEA,IAAImM,oBACFH,mBACCjN,QAAgBqN,kBAAkB,IACnC5M,CAAAA,GAAAA,WAAAA,SAAS,EAACe,KAAKjB,QAAQ,KAAKE,CAAAA,GAAAA,WAAAA,SAAS,EAACsB,IAAIxB,QAAQ;QAEpD,MAAM+M,YAAY;YAChB,GAAG,IAAI,CAACC,KAAK;QACf;QAEA,yDAAyD;QACzD,4DAA4D;QAC5D,+BAA+B;QAC/B,MAAMC,mBAAmB,IAAI,CAACC,OAAO,KAAK;QAC1C,IAAI,CAACA,OAAO,GAAG;QACf,MAAMC,QAAQ,IAAI,CAACA,KAAK;QAExB,IAAI,CAACT,iBAAiB;YACpB,IAAI,CAACS,KAAK,GAAG;QACf;QAEA,sDAAsD;QACtD,wDAAwD;QACxD,IAAIT,mBAAmB,IAAI,CAAClD,GAAG,EAAE;YAC/B,OAAO;QACT;QAEA,MAAM4D,aAAaL,UAAUrM,MAAM;QAEnC,IAAI3B,QAAQC,GAAG,CAACqO,mBAAmB,EAAE;;gBAmC5B;;QAqDT,oDAAoD;QACpD,IAAIa,OAAAA,EAAE,EAAE;YACNC,YAAYC,IAAI,CAAC;QACnB;QAEA,MAAM,EAAExB,UAAU,KAAK,EAAEyB,SAAS,IAAI,EAAE,GAAG5O;QAC3C,MAAM6O,aAAa;YAAE1B;QAAQ;QAE7B,IAAI,IAAI,CAAC2B,cAAc,IAAI,IAAI,CAAC/E,GAAG,EAAE;YACnC,IAAI,CAAC2D,OAAO;gBACVvO,OAAO4P,MAAM,CAACC,IAAI,CAChB,oBACArP,0BACA,IAAI,CAACmP,cAAc,EACnBD;YAEJ;YACA,IAAI,CAAC9E,GAAG;YACR,IAAI,CAACA,GAAG,GAAG;QACb;QAEAhI,KAAKhB,CAAAA,GAAAA,aAAAA,WAAW,EACdC,CAAAA,GAAAA,WAAAA,SAAS,EACPJ,CAAAA,GAAAA,aAAAA,WAAW,EAACmB,MAAMlB,CAAAA,GAAAA,gBAAAA,cAAc,EAACkB,MAAMA,IACvC/B,QAAQiB,MAAM,EACd,IAAI,CAAC2E,aAAa;QAGtB,MAAMjF,YAAYsO,CAAAA,GAAAA,cAAAA,YAAY,EAC5BrO,CAAAA,GAAAA,aAAAA,WAAW,EAACmB,MAAMlB,CAAAA,GAAAA,gBAAAA,cAAc,EAACkB,MAAMA,IACvCuL,UAAUrM,MAAM;QAElB,IAAI,CAAC6N,cAAc,GAAG/M;QAEtB,MAAMmN,eAAevB,eAAeL,UAAUrM,MAAM;QAEpD,qDAAqD;QACrD,0DAA0D;QAE1D,IAAI,CAACgM,mBAAmB,IAAI,CAACkC,eAAe,CAACxO,cAAc,CAACuO,cAAc;YACxE5B,UAAU5M,MAAM,GAAGC;YACnBxB,OAAO4P,MAAM,CAACC,IAAI,CAAC,mBAAmBjN,IAAI8M;YAC1C,8DAA8D;YAC9D,IAAI,CAACO,WAAW,CAAC1H,QAAQlG,KAAKO,IAAI;gBAChC,GAAG/B,OAAO;gBACV4O,QAAQ;YACV;YACA,IAAIA,QAAQ;gBACV,IAAI,CAACS,YAAY,CAAC1O;YACpB;YACA,IAAI;gBACF,MAAM,IAAI,CAAC2O,GAAG,CAAChC,WAAW,IAAI,CAACiC,UAAU,CAACjC,UAAUzD,KAAK,CAAC,EAAE;YAC9D,EAAE,OAAOR,KAAK;gBACZ,IAAImG,CAAAA,GAAAA,SAAAA,OAAO,EAACnG,QAAQA,IAAItJ,SAAS,EAAE;oBACjCZ,OAAO4P,MAAM,CAACC,IAAI,CAAC,oBAAoB3F,KAAK1I,WAAWkO;gBACzD;gBACA,MAAMxF;YACR;YAEAlK,OAAO4P,MAAM,CAACC,IAAI,CAAC,sBAAsBjN,IAAI8M;YAC7C,OAAO;QACT;QAEA,IAAIY,SAAStL,CAAAA,GAAAA,kBAAAA,gBAAgB,EAAC3C;QAC9B,IAAI,EAAEjB,QAAQ,EAAE2E,KAAK,EAAE,GAAGuK;QAE1B,yEAAyE;QACzE,2EAA2E;QAC3E,oBAAoB;QACpB,IAAIjN,OAAiBqC;QACrB,IAAI;;YACD,CAACrC,OAAO,EAAEoC,YAAYC,QAAQ,EAAE,CAAC,GAAG,MAAM3E,QAAQsE,GAAG,CAAC;gBACrD,IAAI,CAACnE,UAAU,CAACoE,WAAW;gBAC3BC,CAAAA,GAAAA,aAAAA,sBAAsB;gBACtB,IAAI,CAACrE,UAAU,CAACC,aAAa;aAC9B;QACH,EAAE,OAAO+I,KAAK;YACZ,wEAAwE;YACxE,+BAA+B;YAC/BM,qBAAqB;gBAAEnI,KAAKO;gBAAI3B,QAAQ,IAAI;YAAC;YAC7C,OAAO;QACT;QAEA,uEAAuE;QACvE,8EAA8E;QAC9E,uDAAuD;QACvD,oEAAoE;QACpE,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAACsP,QAAQ,CAAC/O,cAAc,CAACuO,cAAc;YAC9CxH,SAAS;QACX;QAEA,iEAAiE;QACjE,iDAAiD;QACjD,IAAIzF,aAAaF;QAEjB,6DAA6D;QAC7D,gEAAgE;QAChE,2DAA2D;QAC3DxB,WAAWA,WACPmC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC7B,CAAAA,GAAAA,gBAAAA,cAAc,EAACN,aACnCA;QAEJ,IAAIsJ,QAAQnH,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACnC;QAChC,MAAMoP,mBAAmB5N,GAAGJ,UAAU,CAAC,QAAQwC,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACpC,IAAIxB,QAAQ;QAE5E,0DAA0D;QAC1D,0BAA0B;QAC1B,IAAA,CAAK,4BAAA,IAAI,CAACgP,UAAU,CAAChP,SAAS,KAAA,OAAA,KAAA,IAAzB,0BAAmCqP,WAAW,EAAE;YACnDjG,qBAAqB;gBAAEnI,KAAKO;gBAAI3B,QAAQ,IAAI;YAAC;YAC7C,OAAO,IAAIF,QAAQ,KAAO;QAC5B;QAEA,MAAM2P,sBAAsB,CAAC,CAC3BF,CAAAA,oBACA9F,UAAU8F,oBACT,CAAA,CAAC7M,CAAAA,GAAAA,WAAAA,cAAc,EAAC+G,UACf,CAACrE,CAAAA,GAAAA,cAAAA,eAAe,EAACzC,CAAAA,GAAAA,YAAAA,aAAa,EAAC8G,QAAQ8F,iBAAgB,CAAC;QAG5D,0DAA0D;QAC1D,qDAAqD;QACrD,MAAMG,oBACJ,CAAC9P,QAAQmN,OAAO,IACf,MAAM/N,kBAAkB;YACvBsB,QAAQqB;YACRd,QAAQqM,UAAUrM,MAAM;YACxBb,QAAQ,IAAI;QACd;QAEF,IAAI6M,mBAAmB6C,mBAAmB;YACxC1C,oBAAoB;QACtB;QAEA,IAAIA,qBAAqB7M,aAAa,WAAW;;YAC7CP,QAAgBqN,kBAAkB,GAAG;YAEvC,IAAI/N,QAAQC,GAAG,CAACC,mBAAmB,YAAIuC,GAAGJ,UAAU,CAAC,MAAM;;iBA4BpD;gBACL8N,OAAOlP,QAAQ,GAAGgC,oBAAoBhC,UAAUiC;gBAEhD,IAAIiN,OAAOlP,QAAQ,KAAKA,UAAU;oBAChCA,WAAWkP,OAAOlP,QAAQ;oBAC1BkP,OAAOlP,QAAQ,GAAGQ,CAAAA,GAAAA,aAAAA,WAAW,EAACR;oBAE9B,IAAI,CAACuP,mBAAmB;wBACtBtO,MAAMuM,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC0B;oBAC7B;gBACF;YACF;QACF;QAEA,IAAI,CAACzC,CAAAA,GAAAA,YAAAA,UAAU,EAACjL,KAAK;YACnB,IAAIzC,QAAQC,GAAG,CAAC4J,QAAQ,KAAK,WAAc;gBACzC,MAAM,OAAA,cAGL,CAHK,IAAIrJ,MACP,oBAAiB0B,MAAI,gBAAaO,KAAG,8CACnC,uFAFC,qBAAA;2BAAA;gCAAA;kCAAA;gBAGN;YACF;YACA4H,qBAAqB;gBAAEnI,KAAKO;gBAAI3B,QAAQ,IAAI;YAAC;YAC7C,OAAO;QACT;QAEA6B,aAAagN,CAAAA,GAAAA,cAAAA,YAAY,EAACpO,CAAAA,GAAAA,gBAAAA,cAAc,EAACoB,aAAaqL,UAAUrM,MAAM;QAEtE4I,QAAQnH,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACnC;QAC5B,IAAI2P,aAA6B;QAEjC,IAAIpN,CAAAA,GAAAA,WAAAA,cAAc,EAAC+G,QAAQ;YACzB,MAAMxE,WAAWlB,CAAAA,GAAAA,kBAAAA,gBAAgB,EAAClC;YAClC,MAAMzB,aAAa6E,SAAS9E,QAAQ;YAEpC,MAAM4P,aAAapN,CAAAA,GAAAA,YAAAA,aAAa,EAAC8G;YACjCqG,aAAa1K,CAAAA,GAAAA,cAAAA,eAAe,EAAC2K,YAAY3P;YACzC,MAAM4P,oBAAoBvG,UAAUrJ;YACpC,MAAM6P,iBAAiBD,oBACnBE,CAAAA,GAAAA,eAAAA,aAAa,EAACzG,OAAOrJ,YAAY0E,SAChC,CAAC;YAEN,IAAI,CAACgL,cAAeE,qBAAqB,CAACC,eAAepL,MAAM,EAAG;gBAChE,MAAMsL,gBAAgB3Q,OAAO4Q,IAAI,CAACL,WAAWM,MAAM,EAAEC,MAAM,CACzD,CAACC,QAAU,CAACzL,KAAK,CAACyL,MAAM,IAAI,CAACR,WAAWM,MAAM,CAACE,MAAM,CAACC,QAAQ;gBAGhE,IAAIL,cAAc1O,MAAM,GAAG,KAAK,CAACiO,mBAAmB;oBAClD,IAAIxQ,QAAQC,GAAG,CAAC4J,QAAQ,KAAK,WAAc;wBACzCqC,QAAQqF,IAAI,CACT,KACCT,CAAAA,oBACK,uBACA,6BAA+B,IACrC,iCACC,CAAC,iBAAcG,cAAczD,IAAI,CAC/B,QACA,0BAA4B;oBAEpC;oBAEA,MAAM,OAAA,cAWL,CAXK,IAAIhN,MACPsQ,CAAAA,oBACI,0BAAyB5O,MAAI,sCAAmC+O,cAAczD,IAAI,CACjF,QACA,oCACD,8BAA6BtM,aAAW,8CAA6CqJ,QAAM,KAAG,IACjG,CAAC,iDACCuG,CAAAA,oBACI,8BACA,sBAAqB,CAC1B,IAVC,qBAAA;+BAAA;oCAAA;sCAAA;oBAWN;gBACF;YACF,OAAO,IAAIA,mBAAmB;gBAC5BrO,KAAKgM,CAAAA,GAAAA,WAAAA,oBAAoB,EACvBnO,OAAOC,MAAM,CAAC,CAAC,GAAGwF,UAAU;oBAC1B9E,UAAU8P,eAAepL,MAAM;oBAC/BC,OAAO4L,CAAAA,GAAAA,MAAAA,IAAI,EAAC5L,OAAOmL,eAAevH,MAAM;gBAC1C;YAEJ,OAAO;gBACL,iEAAiE;gBACjElJ,OAAOC,MAAM,CAACqF,OAAOgL;YACvB;QACF;QAEA,IAAI,CAACjD,iBAAiB;YACpB9N,OAAO4P,MAAM,CAACC,IAAI,CAAC,oBAAoBjN,IAAI8M;QAC7C;QAEA,MAAMkC,eAAe,IAAI,CAACxQ,QAAQ,KAAK,UAAU,IAAI,CAACA,QAAQ,KAAK;QAEnE,IAAI;gBAsKAiK,qCAAAA,2BACAwG;YAtKF,IAAIA,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;gBACtCpH;gBACAtJ;gBACA2E;gBACAnD;gBACAE;gBACA4M;gBACA5N,QAAQqM,UAAUrM,MAAM;gBACxBiQ,WAAW5D,UAAU4D,SAAS;gBAC9B9I,eAAe0H;gBACfrH,0BAA0BzI,QAAQyI,wBAAwB;gBAC1DwE,iBAAiBA,mBAAmB,CAAC,IAAI,CAACkE,UAAU;gBACpDtB;YACF;YAEA,IAAI,CAAC5C,mBAAmB,CAACjN,QAAQmN,OAAO,EAAE;gBACxC,MAAM,IAAI,CAACrC,IAAI,CACb/I,IACA,gBAAgBiP,YAAYA,UAAU/O,UAAU,GAAG+C,WACnDsI,UAAUrM,MAAM;YAEpB;YAEA,IAAI,WAAW+P,aAAalB,mBAAmB;gBAC7CvP,WAAWyQ,UAAUnH,KAAK,IAAIA;gBAC9BA,QAAQtJ;gBAER,IAAI,CAACsO,WAAW1B,OAAO,EAAE;oBACvBjI,QAAQtF,OAAOC,MAAM,CAAC,CAAC,GAAGmR,UAAU9L,KAAK,IAAI,CAAC,GAAGA;gBACnD;gBAEA,MAAMkM,wBAAwBxQ,CAAAA,GAAAA,aAAAA,WAAW,EAAC6O,OAAOlP,QAAQ,IACrDM,CAAAA,GAAAA,gBAAAA,cAAc,EAAC4O,OAAOlP,QAAQ,IAC9BkP,OAAOlP,QAAQ;gBAEnB,IAAI2P,cAAc3P,aAAa6Q,uBAAuB;oBACpDxR,OAAO4Q,IAAI,CAACN,YAAYmB,OAAO,CAAC,CAACC;wBAC/B,IAAIpB,cAAchL,KAAK,CAACoM,IAAI,KAAKpB,UAAU,CAACoB,IAAI,EAAE;4BAChD,OAAOpM,KAAK,CAACoM,IAAI;wBACnB;oBACF;gBACF;gBAEA,IAAIxO,CAAAA,GAAAA,WAAAA,cAAc,EAACvC,WAAW;oBAC5B,MAAMgR,aACJ,CAAC1C,WAAW1B,OAAO,IAAI6D,UAAU/O,UAAU,GACvC+O,UAAU/O,UAAU,GACpBlB,CAAAA,GAAAA,aAAAA,WAAW,EACTC,CAAAA,GAAAA,WAAAA,SAAS,EACP,IAAI2H,IAAI5G,IAAI6G,SAASF,IAAI,EAAEnI,QAAQ,EACnC+M,UAAUrM,MAAM,GAElB;oBAGR,IAAIuQ,YAAYD;oBAEhB,IAAI3Q,CAAAA,GAAAA,aAAAA,WAAW,EAAC4Q,YAAY;wBAC1BA,YAAY3Q,CAAAA,GAAAA,gBAAAA,cAAc,EAAC2Q;oBAC7B;oBAEA,IAAIlS,QAAQC,GAAG,CAACqO,mBAAmB,EAAE;;oBAKrC,MAAMuC,aAAapN,CAAAA,GAAAA,YAAAA,aAAa,EAACxC;oBACjC,MAAMmR,gBAAgBlM,CAAAA,GAAAA,cAAAA,eAAe,EAAC2K,YACpC,IAAIxH,IAAI6I,WAAW5I,SAASF,IAAI,EAAEnI,QAAQ;oBAG5C,IAAImR,eAAe;wBACjB9R,OAAOC,MAAM,CAACqF,OAAOwM;oBACvB;gBACF;YACF;YAEA,yDAAyD;YACzD,IAAI,UAAUV,WAAW;gBACvB,IAAIA,UAAUvL,IAAI,KAAK,qBAAqB;oBAC1C,OAAO,IAAI,CAACmF,MAAM,CAAClD,QAAQsJ,UAAU9K,MAAM,EAAE8K,UAAU/K,KAAK,EAAEjG;gBAChE,OAAO;oBACL2J,qBAAqB;wBAAEnI,KAAKwP,UAAUlL,WAAW;wBAAE1F,QAAQ,IAAI;oBAAC;oBAChE,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;YACF;YAEA,MAAMyR,YAAiBX,UAAUY,SAAS;YAC1C,IAAID,aAAaA,UAAUE,qBAAqB,EAAE;gBAChD,MAAMC,UAAU,EAAE,CAACC,MAAM,CAACJ,UAAUE,qBAAqB;gBAEzDC,QAAQT,OAAO,CAAC,CAACW;oBACfC,CAAAA,GAAAA,QAAAA,sBAAsB,EAACD,OAAOE,KAAK;gBACrC;YACF;YAEA,uCAAuC;YACvC,IAAKlB,CAAAA,UAAUmB,OAAO,IAAInB,UAAUoB,OAAM,KAAMpB,UAAUkB,KAAK,EAAE;gBAC/D,IACElB,UAAUkB,KAAK,CAACG,SAAS,IACzBrB,UAAUkB,KAAK,CAACG,SAAS,CAACC,YAAY,EACtC;oBACA,0DAA0D;oBAC1DtS,QAAQiB,MAAM,GAAG;oBAEjB,MAAM6E,cAAckL,UAAUkB,KAAK,CAACG,SAAS,CAACC,YAAY;oBAE1D,oEAAoE;oBACpE,gEAAgE;oBAChE,WAAW;oBACX,IACExM,YAAYnE,UAAU,CAAC,QACvBqP,UAAUkB,KAAK,CAACG,SAAS,CAACE,sBAAsB,KAAK,OACrD;wBACA,MAAMC,aAAarO,CAAAA,GAAAA,kBAAAA,gBAAgB,EAAC2B;wBACpC0M,WAAWjS,QAAQ,GAAGgC,oBACpBiQ,WAAWjS,QAAQ,EACnBiC;wBAGF,MAAM,EAAEhB,KAAK0E,MAAM,EAAEnE,IAAIkE,KAAK,EAAE,GAAGnE,aACjC,IAAI,EACJgE,aACAA;wBAEF,OAAO,IAAI,CAAC8E,MAAM,CAAClD,QAAQxB,QAAQD,OAAOjG;oBAC5C;oBACA2J,qBAAqB;wBAAEnI,KAAKsE;wBAAa1F,QAAQ,IAAI;oBAAC;oBACtD,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;gBAEAoN,UAAU4D,SAAS,GAAG,CAAC,CAACF,UAAUkB,KAAK,CAACO,WAAW;gBAEnD,sBAAsB;gBACtB,IAAIzB,UAAUkB,KAAK,CAACjJ,QAAQ,KAAK7B,oBAAoB;oBACnD,IAAIsL;oBAEJ,IAAI;wBACF,MAAM,IAAI,CAACC,cAAc,CAAC;wBAC1BD,gBAAgB;oBAClB,EAAE,OAAOE,GAAG;wBACVF,gBAAgB;oBAClB;oBAEA1B,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;wBAClCpH,OAAO6I;wBACPnS,UAAUmS;wBACVxN;wBACAnD;wBACAE;wBACA4M,YAAY;4BAAE1B,SAAS;wBAAM;wBAC7BlM,QAAQqM,UAAUrM,MAAM;wBACxBiQ,WAAW5D,UAAU4D,SAAS;wBAC9B2B,YAAY;oBACd;oBAEA,IAAI,UAAU7B,WAAW;wBACvB,MAAM,OAAA,cAAiD,CAAjD,IAAIlR,MAAO,yCAAX,qBAAA;mCAAA;wCAAA;0CAAA;wBAAgD;oBACxD;gBACF;YACF;YAEA,IACEmN,mBACA,IAAI,CAAC1M,QAAQ,KAAK,aAClBiK,CAAAA,CAAAA,4BAAAA,KAAKsI,aAAa,CAACZ,KAAK,KAAA,OAAA,KAAA,IAAA,CAAxB1H,sCAAAA,0BAA0B6H,SAAS,KAAA,OAAA,KAAA,IAAnC7H,oCAAqCuI,UAAU,MAAK,OAAA,CAAA,CACpD/B,mBAAAA,UAAUkB,KAAK,KAAA,OAAA,KAAA,IAAflB,iBAAiBqB,SAAS,GAC1B;gBACA,yDAAyD;gBACzD,kCAAkC;gBAClCrB,UAAUkB,KAAK,CAACG,SAAS,CAACU,UAAU,GAAG;YACzC;gBAI0C/B;YAF1C,6DAA6D;YAC7D,MAAMgC,sBACJhT,QAAQmN,OAAO,IAAIG,UAAUzD,KAAK,KAAMmH,CAAAA,CAAAA,mBAAAA,UAAUnH,KAAK,KAAA,OAAfmH,mBAAmBnH,KAAI;gBAG/D7J;YADF,MAAMiT,eACJjT,CAAAA,kBAAAA,QAAQ4O,MAAM,KAAA,OAAd5O,kBAAmB,CAACiN,mBAAmB,CAAC+F;YAC1C,MAAME,cAAcD,eAAe;gBAAE1I,GAAG;gBAAGG,GAAG;YAAE,IAAI;YACpD,MAAMyI,sBAAsBpG,gBAAAA,OAAAA,eAAgBmG;YAE5C,0CAA0C;YAC1C,MAAME,sBAAsB;gBAC1B,GAAG9F,SAAS;gBACZzD;gBACAtJ;gBACA2E;gBACAxE,QAAQC;gBACRwQ,YAAY;YACd;YAEA,0EAA0E;YAC1E,sEAAsE;YACtE,uEAAuE;YACvE,yEAAyE;YACzE,YAAY;YACZ,IAAIlE,mBAAmB8D,cAAc;oBAmBjCvG,sCAAAA,4BACAwG;gBAnBFA,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;oBAClCpH,OAAO,IAAI,CAACtJ,QAAQ;oBACpBA,UAAU,IAAI,CAACA,QAAQ;oBACvB2E;oBACAnD;oBACAE;oBACA4M,YAAY;wBAAE1B,SAAS;oBAAM;oBAC7BlM,QAAQqM,UAAUrM,MAAM;oBACxBiQ,WAAW5D,UAAU4D,SAAS;oBAC9BjE,iBAAiBA,mBAAmB,CAAC,IAAI,CAACkE,UAAU;gBACtD;gBAEA,IAAI,UAAUH,WAAW;oBACvB,MAAM,OAAA,cAA6D,CAA7D,IAAIlR,MAAO,qCAAkC,IAAI,CAACS,QAAQ,GAA1D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4D;gBACpE;gBAEA,IACE,IAAI,CAACA,QAAQ,KAAK,aAClBiK,CAAAA,CAAAA,6BAAAA,KAAKsI,aAAa,CAACZ,KAAK,KAAA,OAAA,KAAA,IAAA,CAAxB1H,uCAAAA,2BAA0B6H,SAAS,KAAA,OAAA,KAAA,IAAnC7H,qCAAqCuI,UAAU,MAAK,OAAA,CAAA,CACpD/B,oBAAAA,UAAUkB,KAAK,KAAA,OAAA,KAAA,IAAflB,kBAAiBqB,SAAS,GAC1B;oBACA,yDAAyD;oBACzD,kCAAkC;oBAClCrB,UAAUkB,KAAK,CAACG,SAAS,CAACU,UAAU,GAAG;gBACzC;gBAEA,IAAI;oBACF,MAAM,IAAI,CAACzD,GAAG,CAAC8D,qBAAqBpC,WAAWmC;gBACjD,EAAE,OAAO9J,KAAK;oBACZ,IAAImG,CAAAA,GAAAA,SAAAA,OAAO,EAACnG,QAAQA,IAAItJ,SAAS,EAAE;wBACjCZ,OAAO4P,MAAM,CAACC,IAAI,CAAC,oBAAoB3F,KAAK1I,WAAWkO;oBACzD;oBACA,MAAMxF;gBACR;gBAEA,OAAO;YACT;YAEAlK,OAAO4P,MAAM,CAACC,IAAI,CAAC,uBAAuBjN,IAAI8M;YAC9C,IAAI,CAACO,WAAW,CAAC1H,QAAQlG,KAAKO,IAAI/B;YAElC,0EAA0E;YAC1E,iBAAiB;YACjB,iDAAiD;YACjD,MAAMqT,kBACJpG,mBACA,CAACkG,uBACD,CAAC3F,oBACD,CAAC0B,gBACDoE,CAAAA,GAAAA,eAAAA,mBAAmB,EAACF,qBAAqB,IAAI,CAAC7F,KAAK;YAErD,IAAI,CAAC8F,iBAAiB;gBACpB,IAAI;oBACF,MAAM,IAAI,CAAC/D,GAAG,CAAC8D,qBAAqBpC,WAAWmC;gBACjD,EAAE,OAAOI,GAAQ;oBACf,IAAIA,EAAExT,SAAS,EAAEiR,UAAUhJ,KAAK,GAAGgJ,UAAUhJ,KAAK,IAAIuL;yBACjD,MAAMA;gBACb;gBAEA,IAAIvC,UAAUhJ,KAAK,EAAE;oBACnB,IAAI,CAACiF,iBAAiB;wBACpB9N,OAAO4P,MAAM,CAACC,IAAI,CAChB,oBACAgC,UAAUhJ,KAAK,EACfrH,WACAkO;oBAEJ;oBAEA,MAAMmC,UAAUhJ,KAAK;gBACvB;gBAEA,IAAI1I,QAAQC,GAAG,CAACqO,mBAAmB,EAAE;;gBAMrC,IAAI,CAACX,iBAAiB;oBACpB9N,OAAO4P,MAAM,CAACC,IAAI,CAAC,uBAAuBjN,IAAI8M;gBAChD;gBAEA,mDAAmD;gBACnD,MAAM8E,YAAY;gBAClB,IAAIV,gBAAgBU,UAAUrS,IAAI,CAACS,KAAK;oBACtC,IAAI,CAACsN,YAAY,CAACtN;gBACpB;YACF;YAEA,OAAO;QACT,EAAE,OAAOsH,KAAK;YACZ,IAAImG,CAAAA,GAAAA,SAAAA,OAAO,EAACnG,QAAQA,IAAItJ,SAAS,EAAE;gBACjC,OAAO;YACT;YACA,MAAMsJ;QACR;IACF;IAEA+F,YACE1H,MAAqB,EACrBlG,GAAW,EACXO,EAAU,EACV/B,OAA+B,EACzB;QADNA,IAAAA,YAAAA,KAAAA,GAAAA,UAA6B,CAAC;QAE9B,IAAIV,QAAQC,GAAG,CAAC4J,QAAQ,KAAK,WAAc;YACzC,IAAI,OAAOtC,OAAOC,OAAO,KAAK,aAAa;gBACzC0E,QAAQxD,KAAK,CAAE;gBACf;YACF;YAEA,IAAI,OAAOnB,OAAOC,OAAO,CAACY,OAAO,KAAK,aAAa;gBACjD8D,QAAQxD,KAAK,CAAE,6BAA0BN,SAAO;gBAChD;YACF;QACF;QAEA,IAAIA,WAAW,eAAekM,CAAAA,GAAAA,OAAAA,MAAM,QAAO7R,IAAI;YAC7C,IAAI,CAAC8R,QAAQ,GAAG7T,QAAQmN,OAAO;YAC/BtG,OAAOC,OAAO,CAACY,OAAO,CACpB;gBACElG;gBACAO;gBACA/B;gBACA8T,KAAK;gBACLxC,KAAM,IAAI,CAACjH,IAAI,GAAG3C,WAAW,cAAc,IAAI,CAAC2C,IAAI,GAAGnL;YACzD,GACA,AACA,qFAAqF,KADK;YAE1F,kEAAkE;YAClE,IACA6C;QAEJ;IACF;IAEA,MAAMgS,qBACJ1K,GAAgD,EAChD9I,QAAgB,EAChB2E,KAAqB,EACrBnD,EAAU,EACV8M,UAA2B,EAC3BmF,aAAuB,EACY;QACnC,IAAI3K,IAAItJ,SAAS,EAAE;YACjB,gCAAgC;YAChC,MAAMsJ;QACR;QAEA,IAAI4K,CAAAA,GAAAA,aAAAA,YAAY,EAAC5K,QAAQ2K,eAAe;YACtC7U,OAAO4P,MAAM,CAACC,IAAI,CAAC,oBAAoB3F,KAAKtH,IAAI8M;YAEhD,iEAAiE;YACjE,0BAA0B;YAC1B,0CAA0C;YAC1C,4CAA4C;YAE5C,+DAA+D;YAC/DlF,qBAAqB;gBACnBnI,KAAKO;gBACL3B,QAAQ,IAAI;YACd;YAEA,kEAAkE;YAClE,8DAA8D;YAC9D,MAAMT;QACR;QAEA6L,QAAQxD,KAAK,CAACqB;QAEd,IAAI;YACF,IAAI6I;YACJ,MAAM,EAAErP,MAAM+O,SAAS,EAAEsC,WAAW,EAAE,GACpC,MAAM,IAAI,CAACvB,cAAc,CAAC;YAE5B,MAAM3B,YAAsC;gBAC1CkB;gBACAN;gBACAsC;gBACA7K;gBACArB,OAAOqB;YACT;YAEA,IAAI,CAAC2H,UAAUkB,KAAK,EAAE;gBACpB,IAAI;oBACFlB,UAAUkB,KAAK,GAAG,MAAM,IAAI,CAACiC,eAAe,CAACvC,WAAW;wBACtDvI;wBACA9I;wBACA2E;oBACF;gBACF,EAAE,OAAOkP,QAAQ;oBACf5I,QAAQxD,KAAK,CAAC,2CAA2CoM;oBACzDpD,UAAUkB,KAAK,GAAG,CAAC;gBACrB;YACF;YAEA,OAAOlB;QACT,EAAE,OAAOqD,cAAc;YACrB,OAAO,IAAI,CAACN,oBAAoB,CAC9BvE,CAAAA,GAAAA,SAAAA,OAAO,EAAC6E,gBAAgBA,eAAe,OAAA,cAA4B,CAA5B,IAAIvU,MAAMuU,eAAe,KAAzB,qBAAA;uBAAA;4BAAA;8BAAA;YAA2B,IAClE9T,UACA2E,OACAnD,IACA8M,YACA;QAEJ;IACF;IAEA,MAAMoC,aAAa,KA4BlB,EAAE;QA5BgB,IAAA,EACjBpH,OAAOyK,cAAc,EACrB/T,QAAQ,EACR2E,KAAK,EACLnD,EAAE,EACFE,UAAU,EACV4M,UAAU,EACV5N,MAAM,EACNmH,aAAa,EACb8I,SAAS,EACTzI,wBAAwB,EACxBwE,eAAe,EACf4C,mBAAmB,EACnBgD,UAAU,EAeX,GA5BkB;QA6BjB;;;;;KAKC,GACD,IAAIhJ,QAAQyK;QAEZ,IAAI;gBA6EAjO,cACAA,eAKEA,eA0DsBA;YA5I1B,IAAIkO,eAA6C,IAAI,CAAChF,UAAU,CAAC1F,MAAM;YACvE,IAAIgF,WAAW1B,OAAO,IAAIoH,gBAAgB,IAAI,CAAC1K,KAAK,KAAKA,OAAO;gBAC9D,OAAO0K;YACT;YAEA,MAAMvK,kBAAkBJ,oBAAoB;gBAAEC;gBAAOzJ,QAAQ,IAAI;YAAC;YAElE,IAAIgI,eAAe;gBACjBmM,eAAevP;YACjB;YAEA,IAAIwP,kBACFD,gBACA,CAAE,CAAA,aAAaA,YAAW,KAC1BjV,QAAQC,GAAG,CAAC4J,QAAQ,gCAAK,gBACrBoL,0BACAvP;YAEN,MAAMwD,eAAeyE;YACrB,MAAMwH,sBAA2C;gBAC/ClO,UAAU,IAAI,CAAClG,UAAU,CAACqU,WAAW,CAAC;oBACpChM,MAAMqF,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC;wBAAExN;wBAAU2E;oBAAM;oBAC7CyP,mBAAmB;oBACnBjU,QAAQmS,aAAa,SAAS5Q;oBAC9BhB;gBACF;gBACAmH,eAAe;gBACfC,gBAAgB,IAAI,CAACqF,KAAK;gBAC1BpF,WAAW;gBACXJ,eAAeM,eAAe,IAAI,CAACoM,GAAG,GAAG,IAAI,CAACC,GAAG;gBACjDtM,cAAc,CAAC2I;gBACf/I,YAAY;gBACZM;gBACAD;YACF;YAEA,IAAInC,OAKF4G,mBAAmB,CAAC4C,sBAChB,OACA,MAAM1J,sBAAsB;gBAC1BC,WAAW,IAAM6B,cAAcwM;gBAC/B/T,QAAQmS,aAAa,SAAS5Q;gBAC9BhB,QAAQA;gBACRb,QAAQ,IAAI;YACd,GAAGgJ,KAAK,CAAC,CAACC;gBACR,4CAA4C;gBAC5C,oDAAoD;gBACpD,oDAAoD;gBACpD,YAAY;gBACZ,IAAI4D,iBAAiB;oBACnB,OAAO;gBACT;gBACA,MAAM5D;YACR;YAEN,wDAAwD;YACxD,UAAU;YACV,IAAIhD,QAAS9F,CAAAA,aAAa,aAAaA,aAAa,MAAK,GAAI;gBAC3D8F,KAAKC,MAAM,GAAGtB;YAChB;YAEA,IAAIiI,iBAAiB;gBACnB,IAAI,CAAC5G,MAAM;oBACTA,OAAO;wBAAEG,MAAMgE,KAAKsI,aAAa,CAACZ,KAAK;oBAAC;gBAC1C,OAAO;oBACL7L,KAAKG,IAAI,GAAGgE,KAAKsI,aAAa,CAACZ,KAAK;gBACtC;YACF;YAEAlI;YAEA,IACE3D,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,eAAAA,KAAMC,MAAM,KAAA,OAAA,KAAA,IAAZD,aAAcZ,IAAI,MAAK,uBACvBY,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gBAAAA,KAAMC,MAAM,KAAA,OAAA,KAAA,IAAZD,cAAcZ,IAAI,MAAK,qBACvB;gBACA,OAAOY,KAAKC,MAAM;YACpB;YAEA,IAAID,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gBAAAA,KAAMC,MAAM,KAAA,OAAA,KAAA,IAAZD,cAAcZ,IAAI,MAAK,WAAW;gBACpC,MAAMqP,gBAAgBpS,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC2D,KAAKC,MAAM,CAACtE,YAAY;gBAClE,MAAMQ,QAAQ,MAAM,IAAI,CAACnC,UAAU,CAACoE,WAAW;gBAE/C,4DAA4D;gBAC5D,yDAAyD;gBACzD,4DAA4D;gBAC5D,2CAA2C;gBAC3C,IAAI,CAACwI,mBAAmBzK,MAAMI,QAAQ,CAACkS,gBAAgB;oBACrDjL,QAAQiL;oBACRvU,WAAW8F,KAAKC,MAAM,CAACtE,YAAY;oBACnCkD,QAAQ;wBAAE,GAAGA,KAAK;wBAAE,GAAGmB,KAAKC,MAAM,CAACjB,QAAQ,CAACH,KAAK;oBAAC;oBAClDjD,aAAapB,CAAAA,GAAAA,gBAAAA,cAAc,EACzBiE,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACuB,KAAKC,MAAM,CAACjB,QAAQ,CAAC9E,QAAQ,EAAE,IAAI,CAACgD,OAAO,EAC5DhD,QAAQ;oBAGb,kDAAkD;oBAClDgU,eAAe,IAAI,CAAChF,UAAU,CAAC1F,MAAM;oBACrC,IACEgF,WAAW1B,OAAO,IAClBoH,gBACA,IAAI,CAAC1K,KAAK,KAAKA,SACf,CAACzB,eACD;wBACA,4DAA4D;wBAC5D,6DAA6D;wBAC7D,gEAAgE;wBAChE,OAAO;4BAAE,GAAGmM,YAAY;4BAAE1K;wBAAM;oBAClC;gBACF;YACF;YAEA,IAAIkL,CAAAA,GAAAA,YAAAA,UAAU,EAAClL,QAAQ;gBACrBF,qBAAqB;oBAAEnI,KAAKO;oBAAI3B,QAAQ,IAAI;gBAAC;gBAC7C,OAAO,IAAIF,QAAe,KAAO;YACnC;YAEA,MAAM8Q,YACJwD,mBACC,MAAM,IAAI,CAAC7B,cAAc,CAAC9I,OAAOlF,IAAI,CACpC,CAACqQ,MAAS,CAAA;oBACRpD,WAAWoD,IAAInS,IAAI;oBACnBqR,aAAac,IAAId,WAAW;oBAC5B/B,SAAS6C,IAAIC,GAAG,CAAC9C,OAAO;oBACxBC,SAAS4C,IAAIC,GAAG,CAAC7C,OAAO;gBAC1B,CAAA;YAGJ,IAAI9S,QAAQC,GAAG,CAAC4J,QAAQ,KAAK,WAAc;gBACzC,MAAM,EAAE+L,kBAAkB,EAAE,GAC1BzV,QAAQ;gBACV,IAAI,CAACyV,mBAAmBlE,UAAUY,SAAS,GAAG;oBAC5C,MAAM,OAAA,cAEL,CAFK,IAAI9R,MACP,2DAAwDS,WAAS,MAD9D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YACA,MAAM4U,oBAAoB9O,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,iBAAAA,KAAMlD,QAAQ,KAAA,OAAA,KAAA,IAAdkD,eAAgBzC,OAAO,CAACC,GAAG,CAAC;YAEtD,MAAMuR,kBAAkBpE,UAAUmB,OAAO,IAAInB,UAAUoB,OAAO;YAE9D,yDAAyD;YACzD,4CAA4C;YAC5C,IAAI+C,qBAAAA,CAAqB9O,QAAAA,OAAAA,KAAAA,IAAAA,KAAME,QAAQ,GAAE;gBACvC,OAAO,IAAI,CAACsO,GAAG,CAACxO,KAAKE,QAAQ,CAAC;YAChC;YAEA,MAAM,EAAE2L,KAAK,EAAExL,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC2O,QAAQ,CAAC;gBAC9C,IAAID,iBAAiB;oBACnB,IAAI/O,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,KAAMG,IAAI,KAAI,CAAC2O,mBAAmB;wBACpC,OAAO;4BAAEzO,UAAUL,KAAKK,QAAQ;4BAAEwL,OAAO7L,KAAKG,IAAI;wBAAC;oBACrD;oBAEA,MAAMD,WAAWF,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,KAAME,QAAQ,IAC3BF,KAAKE,QAAQ,GACb,IAAI,CAAClG,UAAU,CAACqU,WAAW,CAAC;wBAC1BhM,MAAMqF,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC;4BAAExN;4BAAU2E;wBAAM;wBAC7CxE,QAAQuB;wBACRhB;oBACF;oBAEJ,MAAMqU,UAAU,MAAMrN,cAAc;wBAClC1B;wBACA8B,gBAAgB,IAAI,CAACqF,KAAK;wBAC1BpF,WAAW;wBACXJ,eAAeiN,oBAAoB,CAAC,IAAI,IAAI,CAACN,GAAG;wBAChDtM,cAAc,CAAC2I;wBACf/I,YAAY;wBACZM;oBACF;oBAEA,OAAO;wBACL/B,UAAU4O,QAAQ5O,QAAQ;wBAC1BwL,OAAOoD,QAAQ9O,IAAI,IAAI,CAAC;oBAC1B;gBACF;gBAEA,OAAO;oBACL5C,SAAS,CAAC;oBACVsO,OAAO,MAAM,IAAI,CAACiC,eAAe,CAC/BnD,UAAUY,SAAS,EACnB,AACA,qDADqD;wBAEnDrR;wBACA2E;wBACAxE,QAAQqB;wBACRd;wBACAsC,SAAS,IAAI,CAACA,OAAO;wBACrBqC,eAAe,IAAI,CAACA,aAAa;oBACnC;gBAEJ;YACF;YAEA,mDAAmD;YACnD,6CAA6C;YAC7C,uCAAuC;YACvC,IAAIoL,UAAUoB,OAAO,IAAIqC,oBAAoBlO,QAAQ,IAAIG,UAAU;gBACjE,OAAO,IAAI,CAACmO,GAAG,CAACnO,SAAS;YAC3B;YAEA,+CAA+C;YAC/C,6DAA6D;YAC7D,IACE,CAAC,IAAI,CAACwK,SAAS,IACfF,UAAUmB,OAAO,IACjB7S,QAAQC,GAAG,CAAC4J,QAAQ,gCAAK,iBACzB,CAAC8D,iBACD;;YAUFiF,MAAMG,SAAS,GAAGzS,OAAOC,MAAM,CAAC,CAAC,GAAGqS,MAAMG,SAAS;YACnDrB,UAAUkB,KAAK,GAAGA;YAClBlB,UAAUnH,KAAK,GAAGA;YAClBmH,UAAU9L,KAAK,GAAGA;YAClB8L,UAAU/O,UAAU,GAAGA;YACvB,IAAI,CAACsN,UAAU,CAAC1F,MAAM,GAAGmH;YAEzB,OAAOA;QACT,EAAE,OAAO3H,KAAK;YACZ,OAAO,IAAI,CAAC0K,oBAAoB,CAC9BwB,CAAAA,GAAAA,SAAAA,cAAc,EAAClM,MACf9I,UACA2E,OACAnD,IACA8M;QAEJ;IACF;IAEQS,IACN/B,KAAwB,EACxBlH,IAAsB,EACtB6M,WAA4C,EAC7B;QACf,IAAI,CAAC3F,KAAK,GAAGA;QAEb,OAAO,IAAI,CAACiI,GAAG,CACbnP,MACA,IAAI,CAACkJ,UAAU,CAAC,QAAQ,CAACqC,SAAS,EAClCsB;IAEJ;IAEA;;;GAGC,GACDuC,eAAeC,EAA0B,EAAE;QACzC,IAAI,CAACC,IAAI,GAAGD;IACd;IAEAvG,gBAAgBpN,EAAU,EAAW;QACnC,IAAI,CAAC,IAAI,CAACrB,MAAM,EAAE,OAAO;QACzB,MAAM,CAACkV,cAAcC,QAAQ,GAAG,IAAI,CAACnV,MAAM,CAACiM,KAAK,CAAC,KAAK;QACvD,MAAM,CAACmJ,cAAcC,QAAQ,GAAGhU,GAAG4K,KAAK,CAAC,KAAK;QAE9C,yEAAyE;QACzE,IAAIoJ,WAAWH,iBAAiBE,gBAAgBD,YAAYE,SAAS;YACnE,OAAO;QACT;QAEA,0DAA0D;QAC1D,IAAIH,iBAAiBE,cAAc;YACjC,OAAO;QACT;QAEA,yDAAyD;QACzD,uDAAuD;QACvD,2DAA2D;QAC3D,mCAAmC;QACnC,OAAOD,YAAYE;IACrB;IAEA1G,aAAatN,EAAU,EAAQ;QAC7B,MAAM,GAAGgE,OAAO,EAAE,CAAC,GAAGhE,GAAG4K,KAAK,CAAC,KAAK;QAEpCqJ,CAAAA,GAAAA,qBAAAA,wCAAwC,EACtC;YACE,gEAAgE;YAChE,qBAAqB;YACrB,IAAIjQ,SAAS,MAAMA,SAAS,OAAO;gBACjCc,OAAOoP,QAAQ,CAAC,GAAG;gBACnB;YACF;YAEA,8CAA8C;YAC9C,MAAMC,UAAUC,mBAAmBpQ;YACnC,+CAA+C;YAC/C,MAAMqQ,OAAO5C,SAAS6C,cAAc,CAACH;YACrC,IAAIE,MAAM;gBACRA,KAAKE,cAAc;gBACnB;YACF;YACA,kEAAkE;YAClE,qBAAqB;YACrB,MAAMC,SAAS/C,SAASgD,iBAAiB,CAACN,QAAQ,CAAC,EAAE;YACrD,IAAIK,QAAQ;gBACVA,OAAOD,cAAc;YACvB;QACF,GACA;YACEG,gBAAgB,IAAI,CAACtH,eAAe,CAACpN;QACvC;IAEJ;IAEA2N,SAAShP,MAAc,EAAW;QAChC,OAAO,IAAI,CAACA,MAAM,KAAKA;IACzB;IAEA;;;;;GAKC,GACD,MAAMgW,SACJlV,GAAW,EACXd,MAAoB,EACpBV,OAA6B,EACd;QAFfU,IAAAA,WAAAA,KAAAA,GAAAA,SAAiBc;QACjBxB,IAAAA,YAAAA,KAAAA,GAAAA,UAA2B,CAAC;QAE5B,2FAA2F;QAC3F,IAAIV,QAAQC,GAAG,CAAC4J,QAAQ,KAAK,WAAc;YACzC;QACF;;;QAQA,IAAIsG,SAAStL,IAAAA,kCAAgB,EAAC3C;QAC9B,MAAMsV,cAAcrH,OAAOlP,QAAQ;QAEnC,IAAI,AAAEA,QAAQ,EAAE2E,KAAK,EAAE,GAAGuK;QAC1B,MAAMsH,mBAAmBxW;QAmBzB,MAAMiC,QAAQ,MAAM,IAAI,CAACnC,UAAU,CAACoE,WAAW;QAC/C,IAAIxC,aAAavB;QAEjB,MAAMO,SACJ,OAAOjB,QAAQiB,MAAM,KAAK,cACtBjB,QAAQiB,MAAM,IAAI+D,YAClB,IAAI,CAAC/D,MAAM;QAEjB,MAAM6O,oBAAoB,MAAM1Q,kBAAkB;QA0DlD,MAAMiH,OACJ/G,QAAQC,GAAG,CAACyX,0BAA0B,KAAK,WACvC,OACA,MAAM7Q,sBAAsB;QA4ClC,MAAM0D,QAAQnH,IAAAA,wCAAmB,EAACnC;IAiCpC;IAEA,MAAMoS,eAAe9I,KAAa,EAAE;QAClC,MAAMG,kBAAkBJ,oBAAoB;YAAEC;YAAOzJ,QAAQ,IAAI;QAAC;QAElE,IAAI;YACF,MAAMiX,kBAAkB,MAAM,IAAI,CAAChX,UAAU,CAACiX,QAAQ,CAACzN;YACvDG;YAEA,OAAOqN;QACT,EAAE,OAAOhO,KAAK;YACZW;YACA,MAAMX;QACR;IACF;IAEAgM,SAAYkC,EAAoB,EAAc;QAC5C,IAAIxX,YAAY;QAChB,MAAM+J,SAAS;YACb/J,YAAY;QACd;QACA,IAAI,CAACgK,GAAG,GAAGD;QACX,OAAOyN,KAAK5S,IAAI,CAAC,CAAC0B;YAChB,IAAIyD,WAAW,IAAI,CAACC,GAAG,EAAE;gBACvB,IAAI,CAACA,GAAG,GAAG;YACb;YAEA,IAAIhK,WAAW;gBACb,MAAMsJ,MAAW,OAAA,cAA4C,CAA5C,IAAIvJ,MAAM,oCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA2C;gBAC5DuJ,IAAItJ,SAAS,GAAG;gBAChB,MAAMsJ;YACR;YAEA,OAAOhD;QACT;IACF;IAEA8N,gBACEvC,SAAwB,EACxB4F,GAAoB,EACU;QAC9B,MAAM,EAAE5F,WAAW6F,GAAG,EAAE,GAAG,IAAI,CAAClI,UAAU,CAAC,QAAQ;QACnD,MAAMmI,UAAU,IAAI,CAACC,QAAQ,CAACF;QAC9BD,IAAIE,OAAO,GAAGA;QACd,OAAOE,CAAAA,GAAAA,OAAAA,mBAAmB,EAAyBH,KAAK;YACtDC;YACA9F;YACAxR,QAAQ,IAAI;YACZoX;QACF;IACF;IAEA,IAAI3N,QAAgB;QAClB,OAAO,IAAI,CAAC0D,KAAK,CAAC1D,KAAK;IACzB;IAEA,IAAItJ,WAAmB;QACrB,OAAO,IAAI,CAACgN,KAAK,CAAChN,QAAQ;IAC5B;IAEA,IAAI2E,QAAwB;QAC1B,OAAO,IAAI,CAACqI,KAAK,CAACrI,KAAK;IACzB;IAEA,IAAIxE,SAAiB;QACnB,OAAO,IAAI,CAAC6M,KAAK,CAAC7M,MAAM;IAC1B;IAEA,IAAIO,SAA6B;QAC/B,OAAO,IAAI,CAACsM,KAAK,CAACtM,MAAM;IAC1B;IAEA,IAAIkQ,aAAsB;QACxB,OAAO,IAAI,CAAC5D,KAAK,CAAC4D,UAAU;IAC9B;IAEA,IAAID,YAAqB;QACvB,OAAO,IAAI,CAAC3D,KAAK,CAAC2D,SAAS;IAC7B;IAj1DA2G,YACEtX,QAAgB,EAChB2E,KAAqB,EACrBnD,EAAU,EACV,EACE+V,YAAY,EACZzX,UAAU,EACVoX,GAAG,EACHM,OAAO,EACPnG,SAAS,EACTvI,GAAG,EACH2O,YAAY,EACZ7G,UAAU,EACVlQ,MAAM,EACNsC,OAAO,EACPqC,aAAa,EACbuI,aAAa,EACb+C,SAAS,EAeV,CACD;QAzEF,yCAAyC;aACzC2D,GAAAA,GAAqB,CAAC;QACtB,0CAA0C;aAC1CD,GAAAA,GAAqB,CAAC;aAgBtBqD,oBAAAA,GAAuB;aAiBf5N,IAAAA,GAAenL;aA+JvBgZ,UAAAA,GAAa,CAAC3E;YACZ,MAAM,EAAE0E,oBAAoB,EAAE,GAAG,IAAI;YACrC,IAAI,CAACA,oBAAoB,GAAG;YAE5B,MAAM1K,QAAQgG,EAAEhG,KAAK;YAErB,IAAI,CAACA,OAAO;gBACV,6CAA6C;gBAC7C,sDAAsD;gBACtD,kCAAkC;gBAClC,EAAE;gBACF,oEAAoE;gBACpE,4BAA4B;gBAC5B,4DAA4D;gBAC5D,kFAAkF;gBAClF,gDAAgD;gBAChD,MAAM,EAAEhN,QAAQ,EAAE2E,KAAK,EAAE,GAAG,IAAI;gBAChC,IAAI,CAACkK,WAAW,CACd,gBACArB,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC;oBAAExN,UAAUQ,CAAAA,GAAAA,aAAAA,WAAW,EAACR;oBAAW2E;gBAAM,IAC9D0O,CAAAA,GAAAA,OAAAA,MAAM;gBAER;YACF;YAEA,kFAAkF;YAClF,IAAIrG,MAAM4K,IAAI,EAAE;gBACdtR,OAAO+B,QAAQ,CAACqB,MAAM;gBACtB;YACF;YAEA,IAAI,CAACsD,MAAMuG,GAAG,EAAE;gBACd;YACF;YAEA,yDAAyD;YACzD,IACEmE,wBACA,IAAI,CAAChX,MAAM,KAAKsM,MAAMvN,OAAO,CAACiB,MAAM,IACpCsM,MAAMxL,EAAE,KAAK,IAAI,CAACrB,MAAM,EACxB;gBACA;YACF;YAEA,IAAIqM;YACJ,MAAM,EAAEvL,GAAG,EAAEO,EAAE,EAAE/B,OAAO,EAAEsR,GAAG,EAAE,GAAG/D;YAClC,IAAIjO,QAAQC,GAAG,CAACqH,yBAAyB,EAAE;;YAqB3C,IAAI,CAACyD,IAAI,GAAGiH;YAEZ,MAAM,EAAE/Q,QAAQ,EAAE,GAAG4D,CAAAA,GAAAA,kBAAAA,gBAAgB,EAAC3C;YAEtC,gDAAgD;YAChD,yDAAyD;YACzD,IACE,IAAI,CAACkM,KAAK,IACV3L,OAAOhB,CAAAA,GAAAA,aAAAA,WAAW,EAAC,IAAI,CAACL,MAAM,KAC9BH,aAAaQ,CAAAA,GAAAA,aAAAA,WAAW,EAAC,IAAI,CAACR,QAAQ,GACtC;gBACA;YACF;YAEA,uDAAuD;YACvD,wDAAwD;YACxD,IAAI,IAAI,CAACoV,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACpI,QAAQ;gBAClC;YACF;YAEA,IAAI,CAAC3C,MAAM,CACT,gBACApJ,KACAO,IACAnC,OAAOC,MAAM,CAA2C,CAAC,GAAGG,SAAS;gBACnEmN,SAASnN,QAAQmN,OAAO,IAAI,IAAI,CAAC0G,QAAQ;gBACzC5S,QAAQjB,QAAQiB,MAAM,IAAI,IAAI,CAAC2E,aAAa;gBAC5C,iDAAiD;gBACjDsH,IAAI;YACN,IACAH;QAEJ;QA5NE,uCAAuC;QACvC,MAAMlD,QAAQnH,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACnC;QAElC,6CAA6C;QAC7C,IAAI,CAACgP,UAAU,GAAG,CAAC;QACnB,oDAAoD;QACpD,wDAAwD;QACxD,kCAAkC;QAClC,IAAIhP,aAAa,WAAW;YAC1B,IAAI,CAACgP,UAAU,CAAC1F,MAAM,GAAG;gBACvB+H;gBACAyG,SAAS;gBACTnG,OAAO4F;gBACPzO;gBACA8I,SAAS2F,gBAAgBA,aAAa3F,OAAO;gBAC7CC,SAAS0F,gBAAgBA,aAAa1F,OAAO;YAC/C;QACF;QAEA,IAAI,CAAC7C,UAAU,CAAC,QAAQ,GAAG;YACzBqC,WAAW6F;YACXvD,aAAa,EAEZ;QACH;QAEA,4CAA4C;QAC5C,gFAAgF;QAChF,IAAI,CAACnF,MAAM,GAAG5P,OAAO4P,MAAM;QAE3B,IAAI,CAAC1O,UAAU,GAAGA;QAClB,8DAA8D;QAC9D,kDAAkD;QAClD,MAAMiY,oBACJxV,CAAAA,GAAAA,WAAAA,cAAc,EAACvC,aAAaiK,KAAKsI,aAAa,CAACyF,UAAU;QAE3D,IAAI,CAAClV,QAAQ,GAAG/D,QAAQC,GAAG,CAACiZ,sBAAsB,MAAI;QACtD,IAAI,CAAChD,GAAG,GAAGwC;QACX,IAAI,CAACjO,GAAG,GAAG;QACX,IAAI,CAAC4N,QAAQ,GAAGI;QAChB,6DAA6D;QAC7D,0BAA0B;QAC1B,IAAI,CAACrK,KAAK,GAAG;QACb,IAAI,CAACU,cAAc,GAAG;QACtB,IAAI,CAACX,OAAO,GAAG,CAAC,CACdjD,CAAAA,KAAKsI,aAAa,CAAC2F,IAAI,IACvBjO,KAAKsI,aAAa,CAAC4F,GAAG,IACtBlO,KAAKsI,aAAa,CAAC6F,qBAAqB,IACvCnO,KAAKsI,aAAa,CAAC8F,MAAM,IAAI,CAACpO,KAAKsI,aAAa,CAAC+F,GAAG,IACpD,CAACP,qBACA,CAAC9N,KAAK5B,QAAQ,CAACkQ,MAAM,IACrB,CAACxZ,QAAQC,GAAG,CAACC,2BAAmB;QAGpC,IAAIF,QAAQC,GAAG,CAACqO,mBAAmB,EAAE;;QAUrC,IAAI,CAACL,KAAK,GAAG;YACX1D;YACAtJ;YACA2E;YACAxE,QAAQ4X,oBAAoB/X,WAAWwB;YACvCmP,WAAW,CAAC,CAACA;YACbjQ,QAAQ3B,QAAQC,GAAG,CAACqO,mBAAmB,OAAG3M,0BAAS+D;YACnDmM;QACF;QAEA,IAAI,CAAC4H,gCAAgC,GAAG7Y,QAAQC,OAAO,CAAC;QAExD,IAAI,OAAO0G,WAAW,aAAa;YACjC,kEAAkE;YAClE,4CAA4C;YAC5C,IAAI,CAAC9E,GAAGJ,UAAU,CAAC,OAAO;gBACxB,2DAA2D;gBAC3D,4DAA4D;gBAC5D,MAAM3B,UAA6B;oBAAEiB;gBAAO;gBAC5C,MAAMP,SAASkT,CAAAA,GAAAA,OAAAA,MAAM;gBAErB,IAAI,CAACmF,gCAAgC,GAAG3Z,kBAAkB;oBACxDgB,QAAQ,IAAI;oBACZa;oBACAP;gBACF,GAAGiE,IAAI,CAAC,CAACY;oBACP,kEAAkE;oBAClE,sDAAsD;;oBACpDvF,QAAgBqN,kBAAkB,GAAGtL,OAAOxB;oBAE9C,IAAI,CAAC6O,WAAW,CACd,gBACA7J,UACI7E,SACAqN,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC;wBACnBxN,UAAUQ,CAAAA,GAAAA,aAAAA,WAAW,EAACR;wBACtB2E;oBACF,IACJxE,QACAV;oBAEF,OAAOuF;gBACT;YACF;YAEAsB,OAAOmS,gBAAgB,CAAC,YAAY,IAAI,CAACd,UAAU;YAEnD,2DAA2D;YAC3D,mDAAmD;YACnD,IAAI5Y,QAAQC,GAAG,CAACqH,yBAAyB,EAAE;;QAK7C;IACF;AAyrDF;AAj4DqBzH,OA6CZ4P,MAAAA,GAAmCmK,CAAAA,GAAAA,MAAAA,OAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4728, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/image-config.ts"], "sourcesContent": ["export const VALID_LOADERS = [\n  'default',\n  'imgix',\n  'cloudinary',\n  'akamai',\n  'custom',\n] as const\n\nexport type LoaderValue = (typeof VALID_LOADERS)[number]\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\nexport type ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nexport type LocalPattern = {\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\nexport type RemotePattern = {\n  /**\n   * Must be `http` or `https`.\n   */\n  protocol?: 'http' | 'https'\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single subdomain.\n   * Double `**` matches any number of subdomains.\n   */\n  hostname: string\n\n  /**\n   * Can be literal port such as `8080` or empty string\n   * meaning no port.\n   */\n  port?: string\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\ntype ImageFormat = 'image/avif' | 'image/webp'\n\n/**\n * Image configurations\n *\n * @see [Image configuration options](https://nextjs.org/docs/api-reference/next/image#configuration-options)\n */\nexport type ImageConfigComplete = {\n  /** @see [Device sizes documentation](https://nextjs.org/docs/api-reference/next/image#device-sizes) */\n  deviceSizes: number[]\n\n  /** @see [Image sizing documentation](https://nextjs.org/docs/app/building-your-application/optimizing/images#image-sizing) */\n  imageSizes: number[]\n\n  /** @see [Image loaders configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader) */\n  loader: LoaderValue\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/app/api-reference/components/image#path) */\n  path: string\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/image#loader-configuration) */\n  loaderFile: string\n\n  /**\n   * @deprecated Use `remotePatterns` instead.\n   */\n  domains: string[]\n\n  /** @see [Disable static image import configuration](https://nextjs.org/docs/api-reference/next/image#disable-static-imports) */\n  disableStaticImages: boolean\n\n  /** @see [Cache behavior](https://nextjs.org/docs/api-reference/next/image#caching-behavior) */\n  minimumCacheTTL: number\n\n  /** @see [Acceptable formats](https://nextjs.org/docs/api-reference/next/image#acceptable-formats) */\n  formats: ImageFormat[]\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  dangerouslyAllowSVG: boolean\n\n  /** @see [Content Security Policy](https://nextjs.org/docs/api-reference/next/image#contentsecuritypolicy) */\n  contentSecurityPolicy: string\n\n  /** @see [Content Disposition Type](https://nextjs.org/docs/api-reference/next/image#contentdispositiontype) */\n  contentDispositionType: 'inline' | 'attachment'\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#remotepatterns) */\n  remotePatterns: Array<URL | RemotePattern>\n\n  /** @see [Local Patterns](https://nextjs.org/docs/api-reference/next/image#localPatterns) */\n  localPatterns: LocalPattern[] | undefined\n\n  /** @see [Qualities](https://nextjs.org/docs/api-reference/next/image#qualities) */\n  qualities: number[] | undefined\n\n  /** @see [Unoptimized](https://nextjs.org/docs/api-reference/next/image#unoptimized) */\n  unoptimized: boolean\n}\n\nexport type ImageConfig = Partial<ImageConfigComplete>\n\nexport const imageConfigDefault: ImageConfigComplete = {\n  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],\n  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],\n  path: '/_next/image',\n  loader: 'default',\n  loaderFile: '',\n  domains: [],\n  disableStaticImages: false,\n  minimumCacheTTL: 60,\n  formats: ['image/webp'],\n  dangerouslyAllowSVG: false,\n  contentSecurityPolicy: `script-src 'none'; frame-src 'none'; sandbox;`,\n  contentDispositionType: 'attachment',\n  localPatterns: undefined, // default: allow all local images\n  remotePatterns: [], // default: allow no remote images\n  qualities: undefined, // default: allow all qualities\n  unoptimized: false,\n}\n"], "names": ["VALID_LOADERS", "imageConfigDefault", "deviceSizes", "imageSizes", "path", "loader", "loaderFile", "domains", "disableStaticImages", "minimumCacheTTL", "formats", "dangerouslyAllowSVG", "contentSecurityPolicy", "contentDispositionType", "localPatterns", "undefined", "remotePatterns", "qualities", "unoptimized"], "mappings": ";;;;;;;;;;;;;;IAAaA,aAAa,EAAA;eAAbA;;IAiIAC,kBAAkB,EAAA;eAAlBA;;;AAjIN,MAAMD,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;CACD;AA2HM,MAAMC,qBAA0C;IACrDC,aAAa;QAAC;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;KAAK;IAC1DC,YAAY;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;KAAI;IAC/CC,MAAM;IACNC,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;IACXC,qBAAqB;IACrBC,iBAAiB;IACjBC,SAAS;QAAC;KAAa;IACvBC,qBAAqB;IACrBC,uBAAwB;IACxBC,wBAAwB;IACxBC,eAAeC;IACfC,gBAAgB,EAAE;IAClBC,WAAWF;IACXG,aAAa;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4798, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/image-config-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\nimport type { ImageConfigComplete } from './image-config'\nimport { imageConfigDefault } from './image-config'\n\nexport const ImageConfigContext =\n  React.createContext<ImageConfigComplete>(imageConfigDefault)\n\nif (process.env.NODE_ENV !== 'production') {\n  ImageConfigContext.displayName = 'ImageConfigContext'\n}\n"], "names": ["ImageConfigContext", "React", "createContext", "imageConfigDefault", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAOII,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAHhBN,sBAAAA;;;eAAAA;;;;gEAJK;6BAEiB;AAE5B,MAAMA,qBACXC,OAAAA,OAAK,CAACC,aAAa,CAAsBC,aAAAA,kBAAkB;AAE7D,wCAA2C;IACzCH,mBAAmBO,WAAW,GAAG;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/app-router-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport type { FetchServerResponseResult } from '../../client/components/router-reducer/fetch-server-response'\nimport type {\n  FocusAndScrollRef,\n  PrefetchKind,\n} from '../../client/components/router-reducer/router-reducer-types'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport React from 'react'\n\nexport type ChildSegmentMap = Map<string, CacheNode>\n\n/**\n * Cache node used in app-router / layout-router.\n */\nexport type CacheNode = ReadyCacheNode | LazyCacheNode\n\nexport type LoadingModuleData =\n  | [React.JSX.Element, React.ReactNode, React.ReactNode]\n  | null\n\n/** viewport metadata node */\nexport type HeadData = React.ReactNode\n\nexport type LazyCacheNode = {\n  /**\n   * When rsc is null, this is a lazily-initialized cache node.\n   *\n   * If the app attempts to render it, it triggers a lazy data fetch,\n   * postpones the render, and schedules an update to a new tree.\n   *\n   * TODO: This mechanism should not be used when PPR is enabled, though it\n   * currently is in some cases until we've implemented partial\n   * segment fetching.\n   */\n  rsc: null\n\n  /**\n   * A prefetched version of the segment data. See explanation in corresponding\n   * field of ReadyCacheNode (below).\n   *\n   * Since LazyCacheNode mostly only exists in the non-PPR implementation, this\n   * will usually be null, but it could have been cloned from a previous\n   * CacheNode that was created by the PPR implementation. Eventually we want\n   * to migrate everything away from LazyCacheNode entirely.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * A pending response for the lazy data fetch. If this is not present\n   * during render, it is lazily created.\n   */\n  lazyData: Promise<FetchServerResponseResult> | null\n\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  /**\n   * Child parallel routes.\n   */\n  parallelRoutes: Map<string, ChildSegmentMap>\n\n  /**\n   * The timestamp of the navigation that last updated the CacheNode's data. If\n   * a CacheNode is reused from a previous navigation, this value is not\n   * updated. Used to track the staleness of the data.\n   */\n  navigatedAt: number\n}\n\nexport type ReadyCacheNode = {\n  /**\n   * When rsc is not null, it represents the RSC data for the\n   * corresponding segment.\n   *\n   * `null` is a valid React Node but because segment data is always a\n   * <LayoutRouter> component, we can use `null` to represent empty.\n   *\n   * TODO: For additional type safety, update this type to\n   * Exclude<React.ReactNode, null>. Need to update createEmptyCacheNode to\n   * accept rsc as an argument, or just inline the callers.\n   */\n  rsc: React.ReactNode\n\n  /**\n   * Represents a static version of the segment that can be shown immediately,\n   * and may or may not contain dynamic holes. It's prefetched before a\n   * navigation occurs.\n   *\n   * During rendering, we will choose whether to render `rsc` or `prefetchRsc`\n   * with `useDeferredValue`. As with the `rsc` field, a value of `null` means\n   * no value was provided. In this case, the LayoutRouter will go straight to\n   * rendering the `rsc` value; if that one is also missing, it will suspend and\n   * trigger a lazy fetch.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * There should never be a lazy data request in this case.\n   */\n  lazyData: null\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  parallelRoutes: Map<string, ChildSegmentMap>\n\n  navigatedAt: number\n}\n\nexport interface NavigateOptions {\n  scroll?: boolean\n}\n\nexport interface PrefetchOptions {\n  kind: PrefetchKind\n  onInvalidate?: () => void\n}\n\nexport interface AppRouterInstance {\n  /**\n   * Navigate to the previous history entry.\n   */\n  back(): void\n  /**\n   * Navigate to the next history entry.\n   */\n  forward(): void\n  /**\n   * Refresh the current page.\n   */\n  refresh(): void\n  /**\n   * Refresh the current page. Use in development only.\n   * @internal\n   */\n  hmrRefresh(): void\n  /**\n   * Navigate to the provided href.\n   * Pushes a new history entry.\n   */\n  push(href: string, options?: NavigateOptions): void\n  /**\n   * Navigate to the provided href.\n   * Replaces the current history entry.\n   */\n  replace(href: string, options?: NavigateOptions): void\n  /**\n   * Prefetch the provided href.\n   */\n  prefetch(href: string, options?: PrefetchOptions): void\n}\n\nexport const AppRouterContext = React.createContext<AppRouterInstance | null>(\n  null\n)\nexport const LayoutRouterContext = React.createContext<{\n  parentTree: FlightRouterState\n  parentCacheNode: CacheNode\n  parentSegmentPath: FlightSegmentPath | null\n  url: string\n} | null>(null)\n\nexport const GlobalLayoutRouterContext = React.createContext<{\n  tree: FlightRouterState\n  focusAndScrollRef: FocusAndScrollRef\n  nextUrl: string | null\n}>(null as any)\n\nexport const TemplateContext = React.createContext<React.ReactNode>(null as any)\n\nif (process.env.NODE_ENV !== 'production') {\n  AppRouterContext.displayName = 'AppRouterContext'\n  LayoutRouterContext.displayName = 'LayoutRouterContext'\n  GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext'\n  TemplateContext.displayName = 'TemplateContext'\n}\n\nexport const MissingSlotContext = React.createContext<Set<string>>(new Set())\n"], "names": ["AppRouterContext", "GlobalLayoutRouterContext", "LayoutRouterContext", "MissingSlotContext", "TemplateContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName", "Set"], "mappings": "AAmLIO,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAnL7B;;;;;;;;;;;;;;;;;;;IAiKaT,gBAAgB,EAAA;eAAhBA;;IAUAC,yBAAyB,EAAA;eAAzBA;;IAPAC,mBAAmB,EAAA;eAAnBA;;IAsBAC,kBAAkB,EAAA;eAAlBA;;IATAC,eAAe,EAAA;eAAfA;;;;gEAtKK;AAsJX,MAAMJ,mBAAmBK,OAAAA,OAAK,CAACC,aAAa,CACjD;AAEK,MAAMJ,sBAAsBG,OAAAA,OAAK,CAACC,aAAa,CAK5C;AAEH,MAAML,4BAA4BI,OAAAA,OAAK,CAACC,aAAa,CAIzD;AAEI,MAAMF,kBAAkBC,OAAAA,OAAK,CAACC,aAAa,CAAkB;AAEpE,wCAA2C;IACzCN,iBAAiBU,WAAW,GAAG;IAC/BR,oBAAoBQ,WAAW,GAAG;IAClCT,0BAA0BS,WAAW,GAAG;IACxCN,gBAAgBM,WAAW,GAAG;AAChC;AAEO,MAAMP,qBAAqBE,OAAAA,OAAK,CAACC,aAAa,CAAc,IAAIK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/hooks-client-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport { createContext } from 'react'\nimport type { Params } from '../../server/request/params'\n\nexport const SearchParamsContext = createContext<URLSearchParams | null>(null)\nexport const PathnameContext = createContext<string | null>(null)\nexport const PathParamsContext = createContext<Params | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  SearchParamsContext.displayName = 'SearchParamsContext'\n  PathnameContext.displayName = 'PathnameContext'\n  PathParamsContext.displayName = 'PathParamsContext'\n}\n"], "names": ["PathParamsContext", "PathnameContext", "SearchParamsContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AASII,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAT7B;;;;;;;;;;;;;;;;;IAOaN,iBAAiB,EAAA;eAAjBA;;IADAC,eAAe,EAAA;eAAfA;;IADAC,mBAAmB,EAAA;eAAnBA;;;uBAHiB;AAGvB,MAAMA,sBAAsBC,CAAAA,GAAAA,OAAAA,aAAa,EAAyB;AAClE,MAAMF,kBAAkBE,CAAAA,GAAAA,OAAAA,aAAa,EAAgB;AACrD,MAAMH,oBAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAAgB;AAE9D,wCAA2C;IACzCD,oBAAoBK,WAAW,GAAG;IAClCN,gBAAgBM,WAAW,GAAG;IAC9BP,kBAAkBO,WAAW,GAAG;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4914, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/utils/as-path-to-search-params.ts"], "sourcesContent": ["// Convert router.asPath to a URLSearchParams object\n// example: /dynamic/[slug]?foo=bar -> { foo: 'bar' }\nexport function asPathToSearchParams(asPath: string): URLSearchParams {\n  return new URL(asPath, 'http://n').searchParams\n}\n"], "names": ["asPathToSearchParams", "<PERSON><PERSON><PERSON>", "URL", "searchParams"], "mappings": "AAAA,oDAAoD;AACpD,qDAAqD;;;;+BACrCA,wBAAAA;;;eAAAA;;;AAAT,SAASA,qBAAqBC,MAAc;IACjD,OAAO,IAAIC,IAAID,QAAQ,YAAYE,YAAY;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4932, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/router/adapters.tsx"], "sourcesContent": ["import type { AppRouterInstance } from '../app-router-context.shared-runtime'\nimport type { Params } from '../../../server/request/params'\nimport type { NextRouter } from './router'\n\nimport React, { useMemo, useRef } from 'react'\nimport { PathnameContext } from '../hooks-client-context.shared-runtime'\nimport { isDynamicRoute } from './utils'\nimport { asPathToSearchParams } from './utils/as-path-to-search-params'\nimport { getRouteRegex } from './utils/route-regex'\n\n/** It adapts a Pages Router (`NextRouter`) to the App Router Instance. */\nexport function adaptForAppRouterInstance(\n  pagesRouter: NextRouter\n): AppRouterInstance {\n  return {\n    back() {\n      pagesRouter.back()\n    },\n    forward() {\n      pagesRouter.forward()\n    },\n    refresh() {\n      pagesRouter.reload()\n    },\n    hmrRefresh() {},\n    push(href, { scroll } = {}) {\n      void pagesRouter.push(href, undefined, { scroll })\n    },\n    replace(href, { scroll } = {}) {\n      void pagesRouter.replace(href, undefined, { scroll })\n    },\n    prefetch(href) {\n      void pagesRouter.prefetch(href)\n    },\n  }\n}\n\n/**\n * adaptForSearchParams transforms the ParsedURLQuery into URLSearchParams.\n *\n * @param router the router that contains the query.\n * @returns the search params in the URLSearchParams format\n */\nexport function adaptForSearchParams(\n  router: Pick<NextRouter, 'isReady' | 'query' | 'asPath'>\n): URLSearchParams {\n  if (!router.isReady || !router.query) {\n    return new URLSearchParams()\n  }\n\n  return asPathToSearchParams(router.asPath)\n}\n\nexport function adaptForPathParams(\n  router: Pick<NextRouter, 'isReady' | 'pathname' | 'query' | 'asPath'>\n): Params | null {\n  if (!router.isReady || !router.query) {\n    return null\n  }\n  const pathParams: Params = {}\n  const routeRegex = getRouteRegex(router.pathname)\n  const keys = Object.keys(routeRegex.groups)\n  for (const key of keys) {\n    pathParams[key] = router.query[key]!\n  }\n  return pathParams\n}\n\nexport function PathnameContextProviderAdapter({\n  children,\n  router,\n  ...props\n}: React.PropsWithChildren<{\n  router: Pick<NextRouter, 'pathname' | 'asPath' | 'isReady' | 'isFallback'>\n  isAutoExport: boolean\n}>) {\n  const ref = useRef(props.isAutoExport)\n  const value = useMemo(() => {\n    // isAutoExport is only ever `true` on the first render from the server,\n    // so reset it to `false` after we read it for the first time as `true`. If\n    // we don't use the value, then we don't need it.\n    const isAutoExport = ref.current\n    if (isAutoExport) {\n      ref.current = false\n    }\n\n    // When the route is a dynamic route, we need to do more processing to\n    // determine if we need to stop showing the pathname.\n    if (isDynamicRoute(router.pathname)) {\n      // When the router is rendering the fallback page, it can't possibly know\n      // the path, so return `null` here. Read more about fallback pages over\n      // at:\n      // https://nextjs.org/docs/api-reference/data-fetching/get-static-paths#fallback-pages\n      if (router.isFallback) {\n        return null\n      }\n\n      // When `isAutoExport` is true, meaning this is a page page has been\n      // automatically statically optimized, and the router is not ready, then\n      // we can't know the pathname yet. Read more about automatic static\n      // optimization at:\n      // https://nextjs.org/docs/advanced-features/automatic-static-optimization\n      if (isAutoExport && !router.isReady) {\n        return null\n      }\n    }\n\n    // The `router.asPath` contains the pathname seen by the browser (including\n    // any query strings), so it should have that stripped. Read more about the\n    // `asPath` option over at:\n    // https://nextjs.org/docs/api-reference/next/router#router-object\n    let url: URL\n    try {\n      url = new URL(router.asPath, 'http://f')\n    } catch (_) {\n      // fallback to / for invalid asPath values e.g. //\n      return '/'\n    }\n\n    return url.pathname\n  }, [router.asPath, router.isFallback, router.isReady, router.pathname])\n\n  return (\n    <PathnameContext.Provider value={value}>\n      {children}\n    </PathnameContext.Provider>\n  )\n}\n"], "names": ["PathnameContextProviderAdapter", "adaptForAppRouterInstance", "adaptForPathParams", "adaptForSearchParams", "pagesRouter", "back", "forward", "refresh", "reload", "hmrRefresh", "push", "href", "scroll", "undefined", "replace", "prefetch", "router", "isReady", "query", "URLSearchParams", "asPathToSearchParams", "<PERSON><PERSON><PERSON>", "pathParams", "routeRegex", "getRouteRegex", "pathname", "keys", "Object", "groups", "key", "children", "props", "ref", "useRef", "isAutoExport", "value", "useMemo", "current", "isDynamicRoute", "<PERSON><PERSON><PERSON><PERSON>", "url", "URL", "_", "PathnameContext", "Provider"], "mappings": ";;;;;;;;;;;;;;;;IAoEgBA,8BAA8B,EAAA;eAA9BA;;IAzDAC,yBAAyB,EAAA;eAAzBA;;IA0CAC,kBAAkB,EAAA;eAAlBA;;IAVAC,oBAAoB,EAAA;eAApBA;;;;;iEAvCuB;iDACP;uBACD;sCACM;4BACP;AAGvB,SAASF,0BACdG,WAAuB;IAEvB,OAAO;QACLC;YACED,YAAYC,IAAI;QAClB;QACAC;YACEF,YAAYE,OAAO;QACrB;QACAC;YACEH,YAAYI,MAAM;QACpB;QACAC,eAAc;QACdC,MAAKC,IAAI,EAAE,KAAA;YAAA,IAAA,EAAEC,MAAM,EAAE,GAAV,UAAA,KAAA,IAAa,CAAC,IAAd;YACT,KAAKR,YAAYM,IAAI,CAACC,MAAME,WAAW;gBAAED;YAAO;QAClD;QACAE,SAAQH,IAAI,EAAE,KAAA;YAAA,IAAA,EAAEC,MAAM,EAAE,GAAV,UAAA,KAAA,IAAa,CAAC,IAAd;YACZ,KAAKR,YAAYU,OAAO,CAACH,MAAME,WAAW;gBAAED;YAAO;QACrD;QACAG,UAASJ,IAAI;YACX,KAAKP,YAAYW,QAAQ,CAACJ;QAC5B;IACF;AACF;AAQO,SAASR,qBACda,MAAwD;IAExD,IAAI,CAACA,OAAOC,OAAO,IAAI,CAACD,OAAOE,KAAK,EAAE;QACpC,OAAO,IAAIC;IACb;IAEA,OAAOC,CAAAA,GAAAA,sBAAAA,oBAAoB,EAACJ,OAAOK,MAAM;AAC3C;AAEO,SAASnB,mBACdc,MAAqE;IAErE,IAAI,CAACA,OAAOC,OAAO,IAAI,CAACD,OAAOE,KAAK,EAAE;QACpC,OAAO;IACT;IACA,MAAMI,aAAqB,CAAC;IAC5B,MAAMC,aAAaC,CAAAA,GAAAA,YAAAA,aAAa,EAACR,OAAOS,QAAQ;IAChD,MAAMC,OAAOC,OAAOD,IAAI,CAACH,WAAWK,MAAM;IAC1C,KAAK,MAAMC,OAAOH,KAAM;QACtBJ,UAAU,CAACO,IAAI,GAAGb,OAAOE,KAAK,CAACW,IAAI;IACrC;IACA,OAAOP;AACT;AAEO,SAAStB,+BAA+B,KAO7C;IAP6C,IAAA,EAC7C8B,QAAQ,EACRd,MAAM,EACN,GAAGe,OAIH,GAP6C;IAQ7C,MAAMC,MAAMC,CAAAA,GAAAA,OAAAA,MAAM,EAACF,MAAMG,YAAY;IACrC,MAAMC,QAAQC,CAAAA,GAAAA,OAAAA,OAAO,EAAC;QACpB,wEAAwE;QACxE,2EAA2E;QAC3E,iDAAiD;QACjD,MAAMF,eAAeF,IAAIK,OAAO;QAChC,IAAIH,cAAc;YAChBF,IAAIK,OAAO,GAAG;QAChB;QAEA,sEAAsE;QACtE,qDAAqD;QACrD,IAAIC,CAAAA,GAAAA,OAAAA,cAAc,EAACtB,OAAOS,QAAQ,GAAG;YACnC,yEAAyE;YACzE,uEAAuE;YACvE,MAAM;YACN,sFAAsF;YACtF,IAAIT,OAAOuB,UAAU,EAAE;gBACrB,OAAO;YACT;YAEA,oEAAoE;YACpE,wEAAwE;YACxE,mEAAmE;YACnE,mBAAmB;YACnB,0EAA0E;YAC1E,IAAIL,gBAAgB,CAAClB,OAAOC,OAAO,EAAE;gBACnC,OAAO;YACT;QACF;QAEA,2EAA2E;QAC3E,2EAA2E;QAC3E,2BAA2B;QAC3B,kEAAkE;QAClE,IAAIuB;QACJ,IAAI;YACFA,MAAM,IAAIC,IAAIzB,OAAOK,MAAM,EAAE;QAC/B,EAAE,OAAOqB,GAAG;YACV,kDAAkD;YAClD,OAAO;QACT;QAEA,OAAOF,IAAIf,QAAQ;IACrB,GAAG;QAACT,OAAOK,MAAM;QAAEL,OAAOuB,UAAU;QAAEvB,OAAOC,OAAO;QAAED,OAAOS,QAAQ;KAAC;IAEtE,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACkB,iCAAAA,eAAe,CAACC,QAAQ,EAAA;QAACT,OAAOA;kBAC9BL;;AAGP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5072, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/error-source.ts"], "sourcesContent": ["const symbolError = Symbol.for('NextjsError')\n\nexport function getErrorSource(error: Error): 'server' | 'edge-server' | null {\n  return (error as any)[symbolError] || null\n}\n\nexport type ErrorSourceType = 'edge-server' | 'server'\n\nexport function decorateServerError(error: Error, type: ErrorSourceType) {\n  Object.defineProperty(error, symbolError, {\n    writable: false,\n    enumerable: false,\n    configurable: false,\n    value: type,\n  })\n}\n"], "names": ["decorateServerError", "getErrorSource", "symbolError", "Symbol", "for", "error", "type", "Object", "defineProperty", "writable", "enumerable", "configurable", "value"], "mappings": ";;;;;;;;;;;;;;IAQgBA,mBAAmB,EAAA;eAAnBA;;IANAC,cAAc,EAAA;eAAdA;;;AAFhB,MAAMC,cAAcC,OAAOC,GAAG,CAAC;AAExB,SAASH,eAAeI,KAAY;IACzC,OAAQA,KAAa,CAACH,YAAY,IAAI;AACxC;AAIO,SAASF,oBAAoBK,KAAY,EAAEC,IAAqB;IACrEC,OAAOC,cAAc,CAACH,OAAOH,aAAa;QACxCO,UAAU;QACVC,YAAY;QACZC,cAAc;QACdC,OAAON;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/normalized-asset-prefix.ts"], "sourcesContent": ["export function normalizedAssetPrefix(assetPrefix: string | undefined): string {\n  // remove all leading slashes and trailing slashes\n  const escapedAssetPrefix = assetPrefix?.replace(/^\\/+|\\/+$/g, '') || false\n\n  // if an assetPrefix was '/', we return empty string\n  // because it could be an unnecessary trailing slash\n  if (!escapedAssetPrefix) {\n    return ''\n  }\n\n  if (URL.canParse(escapedAssetPrefix)) {\n    const url = new URL(escapedAssetPrefix).toString()\n    return url.endsWith('/') ? url.slice(0, -1) : url\n  }\n\n  // assuming assetPrefix here is a pathname-style,\n  // restore the leading slash\n  return `/${escapedAssetPrefix}`\n}\n"], "names": ["normalizedAssetPrefix", "assetPrefix", "escapedAssetPrefix", "replace", "URL", "canParse", "url", "toString", "endsWith", "slice"], "mappings": ";;;+BAAgBA,yBAAAA;;;eAAAA;;;AAAT,SAASA,sBAAsBC,WAA+B;IACnE,kDAAkD;IAClD,MAAMC,qBAAqBD,CAAAA,eAAAA,OAAAA,KAAAA,IAAAA,YAAaE,OAAO,CAAC,cAAc,GAAA,KAAO;IAErE,oDAAoD;IACpD,oDAAoD;IACpD,IAAI,CAACD,oBAAoB;QACvB,OAAO;IACT;IAEA,IAAIE,IAAIC,QAAQ,CAACH,qBAAqB;QACpC,MAAMI,MAAM,IAAIF,IAAIF,oBAAoBK,QAAQ;QAChD,OAAOD,IAAIE,QAAQ,CAAC,OAAOF,IAAIG,KAAK,CAAC,GAAG,CAAC,KAAKH;IAChD;IAEA,iDAAiD;IACjD,4BAA4B;IAC5B,OAAQ,MAAGJ;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/format-webpack-messages.ts"], "sourcesContent": ["/**\nMIT License\n\nCopyright (c) 2015-present, Facebook, Inc.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\n// This file is based on https://github.com/facebook/create-react-app/blob/7b1a32be6ec9f99a6c9a3c66813f3ac09c4736b9/packages/react-dev-utils/formatWebpackMessages.js\n// It's been edited to remove chalk and CRA-specific logic\n\nconst friendlySyntaxErrorLabel = 'Syntax error:'\n\nconst WEBPACK_BREAKING_CHANGE_POLYFILLS =\n  '\\n\\nBREAKING CHANGE: webpack < 5 used to include polyfills for node.js core modules by default.'\n\nfunction isLikelyASyntaxError(message: string) {\n  return stripAnsi(message).includes(friendlySyntaxErrorLabel)\n}\n\nlet hadMissingSassError = false\n\n// Cleans up webpack error messages.\nfunction formatMessage(\n  message: any,\n  verbose?: boolean,\n  importTraceNote?: boolean\n) {\n  // TODO: Replace this once webpack 5 is stable\n  if (typeof message === 'object' && message.message) {\n    const filteredModuleTrace =\n      message.moduleTrace &&\n      message.moduleTrace.filter(\n        (trace: any) =>\n          !/next-(middleware|client-pages|route|edge-function)-loader\\.js/.test(\n            trace.originName\n          )\n      )\n\n    let body = message.message\n    const breakingChangeIndex = body.indexOf(WEBPACK_BREAKING_CHANGE_POLYFILLS)\n    if (breakingChangeIndex >= 0) {\n      body = body.slice(0, breakingChangeIndex)\n    }\n\n    message =\n      (message.moduleName ? stripAnsi(message.moduleName) + '\\n' : '') +\n      (message.file ? stripAnsi(message.file) + '\\n' : '') +\n      body +\n      (message.details && verbose ? '\\n' + message.details : '') +\n      (filteredModuleTrace && filteredModuleTrace.length\n        ? (importTraceNote || '\\n\\nImport trace for requested module:') +\n          filteredModuleTrace\n            .map((trace: any) => `\\n${trace.moduleName}`)\n            .join('')\n        : '') +\n      (message.stack && verbose ? '\\n' + message.stack : '')\n  }\n  let lines = message.split('\\n')\n\n  // Strip Webpack-added headers off errors/warnings\n  // https://github.com/webpack/webpack/blob/master/lib/ModuleError.js\n  lines = lines.filter((line: string) => !/Module [A-z ]+\\(from/.test(line))\n\n  // Transform parsing error into syntax error\n  // TODO: move this to our ESLint formatter?\n  lines = lines.map((line: string) => {\n    const parsingError = /Line (\\d+):(?:(\\d+):)?\\s*Parsing error: (.+)$/.exec(\n      line\n    )\n    if (!parsingError) {\n      return line\n    }\n    const [, errorLine, errorColumn, errorMessage] = parsingError\n    return `${friendlySyntaxErrorLabel} ${errorMessage} (${errorLine}:${errorColumn})`\n  })\n\n  message = lines.join('\\n')\n  // Smoosh syntax errors (commonly found in CSS)\n  message = message.replace(\n    /SyntaxError\\s+\\((\\d+):(\\d+)\\)\\s*(.+?)\\n/g,\n    `${friendlySyntaxErrorLabel} $3 ($1:$2)\\n`\n  )\n  // Clean up export errors\n  message = message.replace(\n    /^.*export '(.+?)' was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$2'.`\n  )\n  message = message.replace(\n    /^.*export 'default' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$2' does not contain a default export (imported as '$1').`\n  )\n  message = message.replace(\n    /^.*export '(.+?)' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$3' (imported as '$2').`\n  )\n  lines = message.split('\\n')\n\n  // Remove leading newline\n  if (lines.length > 2 && lines[1].trim() === '') {\n    lines.splice(1, 1)\n  }\n\n  // Cleans up verbose \"module not found\" messages for files and packages.\n  if (lines[1] && lines[1].startsWith('Module not found: ')) {\n    lines = [\n      lines[0],\n      lines[1]\n        .replace('Error: ', '')\n        .replace('Module not found: Cannot find file:', 'Cannot find file:'),\n      ...lines.slice(2),\n    ]\n  }\n\n  // Add helpful message for users trying to use Sass for the first time\n  if (lines[1] && lines[1].match(/Cannot find module.+sass/)) {\n    // ./file.module.scss (<<loader info>>) => ./file.module.scss\n    const firstLine = lines[0].split('!')\n    lines[0] = firstLine[firstLine.length - 1]\n\n    lines[1] =\n      \"To use Next.js' built-in Sass support, you first need to install `sass`.\\n\"\n    lines[1] += 'Run `npm i sass` or `yarn add sass` inside your workspace.\\n'\n    lines[1] += '\\nLearn more: https://nextjs.org/docs/messages/install-sass'\n\n    // dispose of unhelpful stack trace\n    lines = lines.slice(0, 2)\n    hadMissingSassError = true\n  } else if (\n    hadMissingSassError &&\n    message.match(/(sass-loader|resolve-url-loader: CSS error)/)\n  ) {\n    // dispose of unhelpful stack trace following missing sass module\n    lines = []\n  }\n\n  if (!verbose) {\n    message = lines.join('\\n')\n    // Internal stacks are generally useless so we strip them... with the\n    // exception of stacks containing `webpack:` because they're normally\n    // from user code generated by Webpack. For more information see\n    // https://github.com/facebook/create-react-app/pull/1050\n    message = message.replace(\n      /^\\s*at\\s((?!webpack:).)*:\\d+:\\d+[\\s)]*(\\n|$)/gm,\n      ''\n    ) // at ... ...:x:y\n    message = message.replace(/^\\s*at\\s<anonymous>(\\n|$)/gm, '') // at <anonymous>\n\n    message = message.replace(\n      /File was processed with these loaders:\\n(.+[\\\\/](next[\\\\/]dist[\\\\/].+|@next[\\\\/]react-refresh-utils[\\\\/]loader)\\.js\\n)*You may need an additional loader to handle the result of these loaders.\\n/g,\n      ''\n    )\n\n    lines = message.split('\\n')\n  }\n\n  // Remove duplicated newlines\n  lines = (lines as string[]).filter(\n    (line, index, arr) =>\n      index === 0 || line.trim() !== '' || line.trim() !== arr[index - 1].trim()\n  )\n\n  // Reassemble the message\n  message = lines.join('\\n')\n  return message.trim()\n}\n\nexport default function formatWebpackMessages(json: any, verbose?: boolean) {\n  const formattedErrors = json.errors.map((message: any) => {\n    const isUnknownNextFontError = message.message.includes(\n      'An error occurred in `next/font`.'\n    )\n    return formatMessage(message, isUnknownNextFontError || verbose)\n  })\n  const formattedWarnings = json.warnings.map((message: any) => {\n    return formatMessage(message, verbose)\n  })\n\n  // Reorder errors to put the most relevant ones first.\n  let reactServerComponentsError = -1\n\n  for (let i = 0; i < formattedErrors.length; i++) {\n    const error = formattedErrors[i]\n    if (error.includes('ReactServerComponentsError')) {\n      reactServerComponentsError = i\n      break\n    }\n  }\n\n  // Move the reactServerComponentsError to the top if it exists\n  if (reactServerComponentsError !== -1) {\n    const error = formattedErrors.splice(reactServerComponentsError, 1)\n    formattedErrors.unshift(error[0])\n  }\n\n  const result = {\n    ...json,\n    errors: formattedErrors,\n    warnings: formattedWarnings,\n  }\n  if (!verbose && result.errors.some(isLikelyASyntaxError)) {\n    // If there are any syntax errors, show just them.\n    result.errors = result.errors.filter(isLikelyASyntaxError)\n    result.warnings = []\n  }\n  return result\n}\n"], "names": ["formatWebpackMessages", "friendlySyntaxErrorLabel", "WEBPACK_BREAKING_CHANGE_POLYFILLS", "isLikelyASyntaxError", "message", "stripAnsi", "includes", "hadMissingSassError", "formatMessage", "verbose", "importTraceNote", "filteredModuleTrace", "moduleTrace", "filter", "trace", "test", "originName", "body", "breakingChangeIndex", "indexOf", "slice", "moduleName", "file", "details", "length", "map", "join", "stack", "lines", "split", "line", "parsingError", "exec", "errorLine", "errorColumn", "errorMessage", "replace", "trim", "splice", "startsWith", "match", "firstLine", "index", "arr", "json", "formattedErrors", "errors", "isUnknownNextFontError", "formattedWarnings", "warnings", "reactServerComponentsError", "i", "error", "unshift", "result", "some"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;+BAiKA,WAAA;;;eAAwBA;;;;oEAhKF;AACtB,qKAAqK;AACrK,0DAA0D;AAE1D,MAAMC,2BAA2B;AAEjC,MAAMC,oCACJ;AAEF,SAASC,qBAAqBC,OAAe;IAC3C,OAAOC,CAAAA,GAAAA,WAAAA,OAAS,EAACD,SAASE,QAAQ,CAACL;AACrC;AAEA,IAAIM,sBAAsB;AAE1B,oCAAoC;AACpC,SAASC,cACPJ,OAAY,EACZK,OAAiB,EACjBC,eAAyB;IAEzB,8CAA8C;IAC9C,IAAI,OAAON,YAAY,YAAYA,QAAQA,OAAO,EAAE;QAClD,MAAMO,sBACJP,QAAQQ,WAAW,IACnBR,QAAQQ,WAAW,CAACC,MAAM,CACxB,CAACC,QACC,CAAC,gEAAgEC,IAAI,CACnED,MAAME,UAAU;QAIxB,IAAIC,OAAOb,QAAQA,OAAO;QAC1B,MAAMc,sBAAsBD,KAAKE,OAAO,CAACjB;QACzC,IAAIgB,uBAAuB,GAAG;YAC5BD,OAAOA,KAAKG,KAAK,CAAC,GAAGF;QACvB;QAEAd,UACGA,CAAAA,QAAQiB,UAAU,GAAGhB,CAAAA,GAAAA,WAAAA,OAAS,EAACD,QAAQiB,UAAU,IAAI,OAAO,EAAC,IAC7DjB,CAAAA,QAAQkB,IAAI,GAAGjB,CAAAA,GAAAA,WAAAA,OAAS,EAACD,QAAQkB,IAAI,IAAI,OAAO,EAAC,IAClDL,OACCb,CAAAA,QAAQmB,OAAO,IAAId,UAAU,OAAOL,QAAQmB,OAAO,GAAG,EAAC,IACvDZ,CAAAA,uBAAuBA,oBAAoBa,MAAM,GAC7Cd,CAAAA,mBAAmB,wCAAuC,IAC3DC,oBACGc,GAAG,CAAC,CAACX,QAAgB,OAAIA,MAAMO,UAAU,EACzCK,IAAI,CAAC,MACR,EAAC,IACJtB,CAAAA,QAAQuB,KAAK,IAAIlB,UAAU,OAAOL,QAAQuB,KAAK,GAAG,EAAC;IACxD;IACA,IAAIC,QAAQxB,QAAQyB,KAAK,CAAC;IAE1B,kDAAkD;IAClD,oEAAoE;IACpED,QAAQA,MAAMf,MAAM,CAAC,CAACiB,OAAiB,CAAC,uBAAuBf,IAAI,CAACe;IAEpE,4CAA4C;IAC5C,2CAA2C;IAC3CF,QAAQA,MAAMH,GAAG,CAAC,CAACK;QACjB,MAAMC,eAAe,gDAAgDC,IAAI,CACvEF;QAEF,IAAI,CAACC,cAAc;YACjB,OAAOD;QACT;QACA,MAAM,GAAGG,WAAWC,aAAaC,aAAa,GAAGJ;QACjD,OAAU9B,2BAAyB,MAAGkC,eAAa,OAAIF,YAAU,MAAGC,cAAY;IAClF;IAEA9B,UAAUwB,MAAMF,IAAI,CAAC;IACrB,+CAA+C;IAC/CtB,UAAUA,QAAQgC,OAAO,CACvB,4CACC,KAAEnC,2BAAyB;IAE9B,yBAAyB;IACzBG,UAAUA,QAAQgC,OAAO,CACvB,mDACC;IAEHhC,UAAUA,QAAQgC,OAAO,CACvB,6EACC;IAEHhC,UAAUA,QAAQgC,OAAO,CACvB,2EACC;IAEHR,QAAQxB,QAAQyB,KAAK,CAAC;IAEtB,yBAAyB;IACzB,IAAID,MAAMJ,MAAM,GAAG,KAAKI,KAAK,CAAC,EAAE,CAACS,IAAI,OAAO,IAAI;QAC9CT,MAAMU,MAAM,CAAC,GAAG;IAClB;IAEA,wEAAwE;IACxE,IAAIV,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,CAACW,UAAU,CAAC,uBAAuB;QACzDX,QAAQ;YACNA,KAAK,CAAC,EAAE;YACRA,KAAK,CAAC,EAAE,CACLQ,OAAO,CAAC,WAAW,IACnBA,OAAO,CAAC,uCAAuC;eAC/CR,MAAMR,KAAK,CAAC;SAChB;IACH;IAEA,sEAAsE;IACtE,IAAIQ,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,CAACY,KAAK,CAAC,6BAA6B;QAC1D,6DAA6D;QAC7D,MAAMC,YAAYb,KAAK,CAAC,EAAE,CAACC,KAAK,CAAC;QACjCD,KAAK,CAAC,EAAE,GAAGa,SAAS,CAACA,UAAUjB,MAAM,GAAG,EAAE;QAE1CI,KAAK,CAAC,EAAE,GACN;QACFA,KAAK,CAAC,EAAE,IAAI;QACZA,KAAK,CAAC,EAAE,IAAI;QAEZ,mCAAmC;QACnCA,QAAQA,MAAMR,KAAK,CAAC,GAAG;QACvBb,sBAAsB;IACxB,OAAO,IACLA,uBACAH,QAAQoC,KAAK,CAAC,gDACd;QACA,iEAAiE;QACjEZ,QAAQ,EAAE;IACZ;IAEA,IAAI,CAACnB,SAAS;QACZL,UAAUwB,MAAMF,IAAI,CAAC;QACrB,qEAAqE;QACrE,qEAAqE;QACrE,gEAAgE;QAChE,yDAAyD;QACzDtB,UAAUA,QAAQgC,OAAO,CACvB,kDACA,IACA,iBAAiB;;QACnBhC,UAAUA,QAAQgC,OAAO,CAAC,+BAA+B,IAAI,iBAAiB;;QAE9EhC,UAAUA,QAAQgC,OAAO,CACvB,sMACA;QAGFR,QAAQxB,QAAQyB,KAAK,CAAC;IACxB;IAEA,6BAA6B;IAC7BD,QAASA,MAAmBf,MAAM,CAChC,CAACiB,MAAMY,OAAOC,MACZD,UAAU,KAAKZ,KAAKO,IAAI,OAAO,MAAMP,KAAKO,IAAI,OAAOM,GAAG,CAACD,QAAQ,EAAE,CAACL,IAAI;IAG5E,yBAAyB;IACzBjC,UAAUwB,MAAMF,IAAI,CAAC;IACrB,OAAOtB,QAAQiC,IAAI;AACrB;AAEe,SAASrC,sBAAsB4C,IAAS,EAAEnC,OAAiB;IACxE,MAAMoC,kBAAkBD,KAAKE,MAAM,CAACrB,GAAG,CAAC,CAACrB;QACvC,MAAM2C,yBAAyB3C,QAAQA,OAAO,CAACE,QAAQ,CACrD;QAEF,OAAOE,cAAcJ,SAAS2C,0BAA0BtC;IAC1D;IACA,MAAMuC,oBAAoBJ,KAAKK,QAAQ,CAACxB,GAAG,CAAC,CAACrB;QAC3C,OAAOI,cAAcJ,SAASK;IAChC;IAEA,sDAAsD;IACtD,IAAIyC,6BAA6B,CAAC;IAElC,IAAK,IAAIC,IAAI,GAAGA,IAAIN,gBAAgBrB,MAAM,EAAE2B,IAAK;QAC/C,MAAMC,QAAQP,eAAe,CAACM,EAAE;QAChC,IAAIC,MAAM9C,QAAQ,CAAC,+BAA+B;YAChD4C,6BAA6BC;YAC7B;QACF;IACF;IAEA,8DAA8D;IAC9D,IAAID,+BAA+B,CAAC,GAAG;QACrC,MAAME,QAAQP,gBAAgBP,MAAM,CAACY,4BAA4B;QACjEL,gBAAgBQ,OAAO,CAACD,KAAK,CAAC,EAAE;IAClC;IAEA,MAAME,SAAS;QACb,GAAGV,IAAI;QACPE,QAAQD;QACRI,UAAUD;IACZ;IACA,IAAI,CAACvC,WAAW6C,OAAOR,MAAM,CAACS,IAAI,CAACpD,uBAAuB;QACxD,kDAAkD;QAClDmD,OAAOR,MAAM,GAAGQ,OAAOR,MAAM,CAACjC,MAAM,CAACV;QACrCmD,OAAOL,QAAQ,GAAG,EAAE;IACtB;IACA,OAAOK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/side-effect.tsx"], "sourcesContent": ["import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n"], "names": ["SideEffect", "isServer", "window", "useClientOnlyLayoutEffect", "useLayoutEffect", "useClientOnlyEffect", "useEffect", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "Children", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate"], "mappings": ";;;+BAoBA,WAAA;;;eAAwBA;;;uBAnBuC;AAe/D,MAAMC,WAAW,OAAOC,WAAW;AACnC,MAAMC,4BAA4BF,WAAW,KAAO,IAAIG,OAAAA,eAAe;AACvE,MAAMC,sBAAsBJ,WAAW,KAAO,IAAIK,OAAAA,SAAS;AAE5C,SAASN,WAAWO,KAAsB;IACvD,MAAM,EAAEC,WAAW,EAAEC,uBAAuB,EAAE,GAAGF;IAEjD,SAASG;QACP,IAAIF,eAAeA,YAAYG,gBAAgB,EAAE;YAC/C,MAAMC,eAAeC,OAAAA,QAAQ,CAACC,OAAO,CACnCC,MAAMC,IAAI,CAACR,YAAYG,gBAAgB,EAA0BM,MAAM,CACrEC;YAGJV,YAAYW,UAAU,CAACV,wBAAwBG,cAAcL;QAC/D;IACF;IAEA,IAAIN,UAAU;YACZO;QAAAA,eAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,YAAaG,gBAAgB,KAAA,OAAA,KAAA,IAA7BH,8BAA+BY,GAAG,CAACb,MAAMc,QAAQ;QACjDX;IACF;IAEAP;gDAA0B;gBACxBK;YAAAA,eAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,YAAaG,gBAAgB,KAAA,OAAA,KAAA,IAA7BH,8BAA+BY,GAAG,CAACb,MAAMc,QAAQ;YACjD;wDAAO;wBACLb;oBAAAA,eAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,YAAaG,gBAAgB,KAAA,OAAA,KAAA,IAA7BH,8BAA+Bc,MAAM,CAACf,MAAMc,QAAQ;gBACtD;;QACF;;IAEA,kFAAkF;IAClF,oFAAoF;IACpF,gEAAgE;IAChE,qFAAqF;IACrF,mFAAmF;IACnFlB;gDAA0B;YACxB,IAAIK,aAAa;gBACfA,YAAYe,cAAc,GAAGb;YAC/B;YACA;wDAAO;oBACL,IAAIF,aAAa;wBACfA,YAAYe,cAAc,GAAGb;oBAC/B;gBACF;;QACF;;IAEAL;0CAAoB;YAClB,IAAIG,eAAeA,YAAYe,cAAc,EAAE;gBAC7Cf,YAAYe,cAAc;gBAC1Bf,YAAYe,cAAc,GAAG;YAC/B;YACA;kDAAO;oBACL,IAAIf,eAAeA,YAAYe,cAAc,EAAE;wBAC7Cf,YAAYe,cAAc;wBAC1Bf,YAAYe,cAAc,GAAG;oBAC/B;gBACF;;QACF;;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/amp-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\n\nexport const AmpStateContext: React.Context<any> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  AmpStateContext.displayName = 'AmpStateContext'\n}\n"], "names": ["AmpStateContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAIIG,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAFhBL,mBAAAA;;;eAAAA;;;;gEAFK;AAEX,MAAMA,kBAAsCC,OAAAA,OAAK,CAACC,aAAa,CAAC,CAAC;AAExE,wCAA2C;IACzCF,gBAAgBM,WAAW,GAAG;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/amp-mode.ts"], "sourcesContent": ["export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n"], "names": ["isInAmpMode", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;+BAAgBA,eAAAA;;;eAAAA;;;AAAT,SAASA,YAAY,KAAA;IAAA,IAAA,EAC1BC,WAAW,KAAK,EAChBC,SAAS,KAAK,EACdC,WAAW,KAAK,EACjB,GAJ2B,UAAA,KAAA,IAIxB,CAAC,IAJuB;IAK1B,OAAOF,YAAaC,UAAUC;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/shared/lib/head.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n"], "names": ["defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "child", "type", "React", "Fragment", "concat", "Children", "toArray", "props", "children", "reduce", "fragmentList", "fragmentChild", "METATYPES", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "h", "isUnique", "<PERSON><PERSON><PERSON>", "key", "indexOf", "slice", "has", "add", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "filter", "map", "c", "process", "env", "NODE_ENV", "srcMessage", "warnOnce", "cloneElement", "Head", "ampState", "useContext", "AmpStateContext", "headManager", "HeadManagerContext", "Effect", "reduceComponentsToState", "isInAmpMode"], "mappings": "AA2IUkD,QAAQC,GAAG,CAACC,QAAQ,KAAK;AA3InC;;;;;;;;;;;;;;;;IAgLA,OAAmB,EAAA;eAAnB;;IAnKgBpD,WAAW,EAAA;eAAXA;;;;;;iEAX4B;qEACzB;yCACa;iDACG;yBACP;0BACH;AAMlB,SAASA,YAAYC,SAAiB;IAAjBA,IAAAA,cAAAA,KAAAA,GAAAA,YAAY;IACtC,MAAMC,OAAO;sBAAC,CAAA,GAAA,YAAA,GAAA,EAACC,QAAAA;YAAKC,SAAQ;WAAY;KAAa;IACrD,IAAI,CAACH,WAAW;QACdC,KAAKG,IAAI,CAAA,WAAA,GACP,CAAA,GAAA,YAAA,GAAA,EAACF,QAAAA;YAAKG,MAAK;YAAWC,SAAQ;WAAyB;IAE3D;IACA,OAAOL;AACT;AAEA,SAASM,iBACPC,IAAoC,EACpCC,KAA2C;IAE3C,8FAA8F;IAC9F,IAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,UAAU;QAC1D,OAAOD;IACT;IACA,kCAAkC;IAClC,IAAIC,MAAMC,IAAI,KAAKC,OAAAA,OAAK,CAACC,QAAQ,EAAE;QACjC,OAAOJ,KAAKK,MAAM,CAChB,AACAF,OAAAA,OAAK,CAACG,QAAQ,CAACC,OAAO,CAACN,MAAMO,KAAK,CAACC,QAAQ,EAAEC,MAAM,CACjD,AACA,CACEC,cACAC,uBAL+F,6DAEE;YAKjG,IACE,OAAOA,kBAAkB,YACzB,OAAOA,kBAAkB,UACzB;gBACA,OAAOD;YACT;YACA,OAAOA,aAAaN,MAAM,CAACO;QAC7B,GACA,EAAE;IAGR;IACA,OAAOZ,KAAKK,MAAM,CAACJ;AACrB;AAEA,MAAMY,YAAY;IAAC;IAAQ;IAAa;IAAW;CAAW;AAE9D;;;;AAIA,GACA,SAASC;IACP,MAAMC,OAAO,IAAIC;IACjB,MAAMC,OAAO,IAAID;IACjB,MAAME,YAAY,IAAIF;IACtB,MAAMG,iBAAsD,CAAC;IAE7D,OAAO,CAACC;QACN,IAAIC,WAAW;QACf,IAAIC,SAAS;QAEb,IAAIF,EAAEG,GAAG,IAAI,OAAOH,EAAEG,GAAG,KAAK,YAAYH,EAAEG,GAAG,CAACC,OAAO,CAAC,OAAO,GAAG;YAChEF,SAAS;YACT,MAAMC,MAAMH,EAAEG,GAAG,CAACE,KAAK,CAACL,EAAEG,GAAG,CAACC,OAAO,CAAC,OAAO;YAC7C,IAAIT,KAAKW,GAAG,CAACH,MAAM;gBACjBF,WAAW;YACb,OAAO;gBACLN,KAAKY,GAAG,CAACJ;YACX;QACF;QAEA,wCAAwC;QACxC,OAAQH,EAAElB,IAAI;YACZ,KAAK;YACL,KAAK;gBACH,IAAIe,KAAKS,GAAG,CAACN,EAAElB,IAAI,GAAG;oBACpBmB,WAAW;gBACb,OAAO;oBACLJ,KAAKU,GAAG,CAACP,EAAElB,IAAI;gBACjB;gBACA;YACF,KAAK;gBACH,IAAK,IAAI0B,IAAI,GAAGC,MAAMhB,UAAUiB,MAAM,EAAEF,IAAIC,KAAKD,IAAK;oBACpD,MAAMG,WAAWlB,SAAS,CAACe,EAAE;oBAC7B,IAAI,CAACR,EAAEZ,KAAK,CAACwB,cAAc,CAACD,WAAW;oBAEvC,IAAIA,aAAa,WAAW;wBAC1B,IAAIb,UAAUQ,GAAG,CAACK,WAAW;4BAC3BV,WAAW;wBACb,OAAO;4BACLH,UAAUS,GAAG,CAACI;wBAChB;oBACF,OAAO;wBACL,MAAME,WAAWb,EAAEZ,KAAK,CAACuB,SAAS;wBAClC,MAAMG,aAAaf,cAAc,CAACY,SAAS,IAAI,IAAIf;wBACnD,IAAKe,CAAAA,aAAa,UAAU,CAACT,MAAK,KAAMY,WAAWR,GAAG,CAACO,WAAW;4BAChEZ,WAAW;wBACb,OAAO;4BACLa,WAAWP,GAAG,CAACM;4BACfd,cAAc,CAACY,SAAS,GAAGG;wBAC7B;oBACF;gBACF;gBACA;QACJ;QAEA,OAAOb;IACT;AACF;AAEA;;;CAGC,GACD,SAASc,iBACPC,oBAAoD,EACpD5B,KAAQ;IAER,MAAM,EAAEhB,SAAS,EAAE,GAAGgB;IACtB,OAAO4B,qBACJ1B,MAAM,CAACX,kBAAkB,EAAE,EAC3BsC,OAAO,GACPhC,MAAM,CAACd,YAAYC,WAAW6C,OAAO,IACrCC,MAAM,CAACxB,UACPuB,OAAO,GACPE,GAAG,CAAC,CAACC,GAA4BZ;QAChC,MAAML,MAAMiB,EAAEjB,GAAG,IAAIK;QACrB,wCAA4C;YAC1C,yDAAyD;YACzD,IAAIY,EAAEtC,IAAI,KAAK,YAAYsC,EAAEhC,KAAK,CAAC,OAAO,KAAK,uBAAuB;gBACpE,MAAMoC,aAAaJ,EAAEhC,KAAK,CAAC,MAAM,GAC5B,4BAAyBgC,EAAEhC,KAAK,CAAC,MAAM,GAAC,MACxC;gBACLqC,CAAAA,GAAAA,UAAAA,QAAQ,EACL,mDAAgDD,aAAW;YAEhE,OAAO,IAAIJ,EAAEtC,IAAI,KAAK,UAAUsC,EAAEhC,KAAK,CAAC,MAAM,KAAK,cAAc;gBAC/DqC,CAAAA,GAAAA,UAAAA,QAAQ,EACL,wFAAqFL,EAAEhC,KAAK,CAAC,OAAO,GAAC;YAE1G;QACF;QACA,OAAA,WAAA,GAAOL,OAAAA,OAAK,CAAC2C,YAAY,CAACN,GAAG;YAAEjB;QAAI;IACrC;AACJ;AAEA;;;CAGC,GACD,SAASwB,KAAK,KAA2C;IAA3C,IAAA,EAAEtC,QAAQ,EAAiC,GAA3C;IACZ,MAAMuC,WAAWC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,yBAAAA,eAAe;IAC3C,MAAMC,cAAcF,CAAAA,GAAAA,OAAAA,UAAU,EAACG,iCAAAA,kBAAkB;IACjD,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACC,YAAAA,OAAM,EAAA;QACLC,yBAAyBnB;QACzBgB,aAAaA;QACb3D,WAAW+D,CAAAA,GAAAA,SAAAA,WAAW,EAACP;kBAEtBvC;;AAGP;MAEA,WAAesC", "ignoreList": [0], "debugId": null}}]}