'use client';

import { useState, useEffect } from 'react';
import { Users, Plus, X } from 'lucide-react';

interface Character {
  id: string;
  novelId: string;
  name: string;
  role: string;
  description: string;
  personality?: string;
  appearance?: string;
  relationships?: string;
}

interface CharacterManagerProps {
  novelId?: string;
  characters: Character[];
  onCharactersChange: (characters: Character[]) => void;
  disabled?: boolean;
}

export default function CharacterManager({ novelId, characters, onCharactersChange, disabled }: CharacterManagerProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newCharacter, setNewCharacter] = useState({
    name: '',
    role: '其他',
    description: ''
  });

  // 当小说ID变化时，加载对应的人物设定
  useEffect(() => {
    if (novelId) {
      loadCharacters();
    } else {
      onCharactersChange([]);
    }
  }, [novelId]);

  const loadCharacters = async () => {
    if (!novelId) return;

    try {
      const response = await fetch(`/api/characters?novelId=${novelId}`);
      const result = await response.json();
      if (result.success) {
        onCharactersChange(result.data);
      }
    } catch (error) {
      console.error('加载人物设定失败:', error);
    }
  };

  const handleAddCharacter = async () => {
    if (!newCharacter.name.trim() || !novelId) return;

    setLoading(true);
    try {
      const response = await fetch('/api/characters', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          novelId,
          name: newCharacter.name,
          role: newCharacter.role,
          description: newCharacter.description,
        }),
      });

      const result = await response.json();
      if (result.success) {
        await loadCharacters(); // 重新加载列表
        setNewCharacter({ name: '', role: '其他', description: '' });
        setShowAddForm(false);
      } else {
        alert(`添加失败: ${result.error}`);
      }
    } catch (error) {
      console.error('添加人物失败:', error);
      alert('添加人物失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveCharacter = async (id: string) => {
    if (!confirm('确定要删除这个人物设定吗？')) return;

    try {
      const response = await fetch(`/api/characters?id=${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      if (result.success) {
        await loadCharacters(); // 重新加载列表
      } else {
        alert(`删除失败: ${result.error}`);
      }
    } catch (error) {
      console.error('删除人物失败:', error);
      alert('删除人物失败');
    }
  };

  const characterTypes = ['男主', '女主', '配角', '反派', '其他'];

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-lg font-semibold text-gray-800 flex items-center">
          <Users className="mr-2" size={18} />
          人物设定
        </h2>
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          disabled={disabled}
          className="p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50"
          title="添加人物"
        >
          <Plus size={16} />
        </button>
      </div>

      {/* 添加人物表单 */}
      {showAddForm && (
        <div className="mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="space-y-2">
            <div className="flex space-x-2">
              <input
                type="text"
                placeholder="人物名称"
                value={newCharacter.name}
                onChange={(e) => setNewCharacter({ ...newCharacter, name: e.target.value })}
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <select
                value={newCharacter.role}
                onChange={(e) => setNewCharacter({ ...newCharacter, role: e.target.value })}
                className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                {characterTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
            <input
              type="text"
              placeholder="备注描述"
              value={newCharacter.description}
              onChange={(e) => setNewCharacter({ ...newCharacter, description: e.target.value })}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            <div className="flex space-x-2">
              <button
                onClick={handleAddCharacter}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                添加
              </button>
              <button
                onClick={() => setShowAddForm(false)}
                className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 人物列表 */}
      <div className="space-y-2">
        {characters.length === 0 ? (
          <div className="text-center py-4 text-gray-500 text-sm">
            暂无人物设定
          </div>
        ) : (
          characters.map((character) => (
            <div key={character.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-800 text-sm">{character.name}</span>
                  <span className={`px-2 py-0.5 text-xs rounded ${
                    character.role === '男主' ? 'bg-blue-100 text-blue-800' :
                    character.role === '女主' ? 'bg-pink-100 text-pink-800' :
                    character.role === '配角' ? 'bg-green-100 text-green-800' :
                    character.role === '反派' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {character.role}
                  </span>
                </div>
                {character.description && (
                  <div className="text-xs text-gray-600 mt-1 truncate">
                    {character.description}
                  </div>
                )}
              </div>
              <button
                onClick={() => handleRemoveCharacter(character.id)}
                disabled={disabled}
                className="p-1 text-gray-400 hover:text-red-600 disabled:opacity-50"
                title="删除"
              >
                <X size={14} />
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
