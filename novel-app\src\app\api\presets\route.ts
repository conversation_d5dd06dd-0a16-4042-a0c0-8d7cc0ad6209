import { NextRequest, NextResponse } from 'next/server';
import { presetDb } from '@/lib/database';

// GET - 获取所有预设
export async function GET() {
  try {
    const presets = presetDb.getAll();
    return NextResponse.json({
      success: true,
      data: presets,
    });
  } catch (error) {
    console.error('获取预设列表失败:', error);
    return NextResponse.json(
      { success: false, error: '获取预设列表失败' },
      { status: 500 }
    );
  }
}

// POST - 保存自定义预设
export async function POST(request: NextRequest) {
  try {
    const { name, description, rules } = await request.json();

    if (!name || !rules) {
      return NextResponse.json(
        { success: false, error: '名称和规则不能为空' },
        { status: 400 }
      );
    }

    // 保存预设到数据库
    const preset = presetDb.create({
      name,
      description: description || '',
      rules,
    });

    return NextResponse.json({
      success: true,
      data: preset,
      message: '预设保存成功',
    });

  } catch (error) {
    console.error('保存预设失败:', error);
    return NextResponse.json(
      { success: false, error: '保存预设失败' },
      { status: 500 }
    );
  }
}
