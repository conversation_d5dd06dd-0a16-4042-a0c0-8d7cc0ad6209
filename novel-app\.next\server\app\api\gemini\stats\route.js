var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/gemini/stats/route.js")
R.c("server/chunks/node_modules_next_f6ff5dee._.js")
R.c("server/chunks/[root-of-the-server]__81b25462._.js")
R.m("[project]/.next-internal/server/app/api/gemini/stats/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/gemini/stats/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/gemini/stats/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
