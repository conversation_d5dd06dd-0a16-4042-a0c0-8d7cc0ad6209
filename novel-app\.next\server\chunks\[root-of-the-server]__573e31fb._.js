module.exports = [
"[project]/.next-internal/server/app/api/rewrite/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[project]/src/lib/database.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "chapterDb",
    ()=>chapterDb,
    "characterDb",
    ()=>characterDb,
    "jobDb",
    ()=>jobDb,
    "novelDb",
    ()=>novelDb,
    "presetDb",
    ()=>presetDb,
    "ruleDb",
    ()=>ruleDb
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
// 数据存储路径
const DATA_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const NOVELS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'novels.json');
const CHAPTERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'chapters.json');
const RULES_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'rewrite_rules.json');
const JOBS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'rewrite_jobs.json');
const CHARACTERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'characters.json');
const PRESETS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'presets.json');
// 确保数据目录存在
function ensureDataDir() {
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DATA_DIR)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DATA_DIR, {
            recursive: true
        });
    }
}
// 读取JSON文件
function readJsonFile(filePath) {
    ensureDataDir();
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath)) {
        return [];
    }
    try {
        const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`Error reading ${filePath}:`, error);
        return [];
    }
}
// 写入JSON文件
function writeJsonFile(filePath, data) {
    ensureDataDir();
    try {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
    } catch (error) {
        console.error(`Error writing ${filePath}:`, error);
        throw error;
    }
}
// 生成唯一ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
const novelDb = {
    getAll: ()=>readJsonFile(NOVELS_FILE),
    getById: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        return novels.find((novel)=>novel.id === id);
    },
    create: (novel)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const newNovel = {
            ...novel,
            id: generateId(),
            createdAt: new Date().toISOString()
        };
        novels.push(newNovel);
        writeJsonFile(NOVELS_FILE, novels);
        return newNovel;
    },
    update: (id, updates)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return null;
        novels[index] = {
            ...novels[index],
            ...updates
        };
        writeJsonFile(NOVELS_FILE, novels);
        return novels[index];
    },
    delete: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return false;
        novels.splice(index, 1);
        writeJsonFile(NOVELS_FILE, novels);
        return true;
    }
};
const chapterDb = {
    getAll: ()=>readJsonFile(CHAPTERS_FILE),
    getByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.filter((chapter)=>chapter.novelId === novelId);
    },
    getById: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.find((chapter)=>chapter.id === id);
    },
    create: (chapter)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const newChapter = {
            ...chapter,
            id: generateId(),
            createdAt: new Date().toISOString()
        };
        chapters.push(newChapter);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return newChapter;
    },
    createBatch: (chapters)=>{
        const existingChapters = readJsonFile(CHAPTERS_FILE);
        const newChapters = chapters.map((chapter)=>({
                ...chapter,
                id: generateId(),
                createdAt: new Date().toISOString()
            }));
        existingChapters.push(...newChapters);
        writeJsonFile(CHAPTERS_FILE, existingChapters);
        return newChapters;
    },
    delete: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const index = chapters.findIndex((chapter)=>chapter.id === id);
        if (index === -1) return false;
        chapters.splice(index, 1);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const filteredChapters = chapters.filter((chapter)=>chapter.novelId !== novelId);
        writeJsonFile(CHAPTERS_FILE, filteredChapters);
        return true;
    }
};
const ruleDb = {
    getAll: ()=>readJsonFile(RULES_FILE),
    getById: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        return rules.find((rule)=>rule.id === id);
    },
    create: (rule)=>{
        const rules = readJsonFile(RULES_FILE);
        const newRule = {
            ...rule,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        rules.push(newRule);
        writeJsonFile(RULES_FILE, rules);
        return newRule;
    },
    update: (id, updates)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return null;
        rules[index] = {
            ...rules[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(RULES_FILE, rules);
        return rules[index];
    },
    delete: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return false;
        rules.splice(index, 1);
        writeJsonFile(RULES_FILE, rules);
        return true;
    }
};
const jobDb = {
    getAll: ()=>readJsonFile(JOBS_FILE),
    getById: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        return jobs.find((job)=>job.id === id);
    },
    create: (job)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const newJob = {
            ...job,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        jobs.push(newJob);
        writeJsonFile(JOBS_FILE, jobs);
        return newJob;
    },
    update: (id, updates)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return null;
        jobs[index] = {
            ...jobs[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(JOBS_FILE, jobs);
        return jobs[index];
    },
    delete: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return false;
        jobs.splice(index, 1);
        writeJsonFile(JOBS_FILE, jobs);
        return true;
    }
};
const characterDb = {
    getAll: ()=>readJsonFile(CHARACTERS_FILE),
    getByNovelId: (novelId)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        return characters.filter((character)=>character.novelId === novelId);
    },
    getById: (id)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        return characters.find((character)=>character.id === id);
    },
    create: (character)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const newCharacter = {
            ...character,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        characters.push(newCharacter);
        writeJsonFile(CHARACTERS_FILE, characters);
        return newCharacter;
    },
    update: (id, updates)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const index = characters.findIndex((character)=>character.id === id);
        if (index === -1) return null;
        characters[index] = {
            ...characters[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(CHARACTERS_FILE, characters);
        return characters[index];
    },
    delete: (id)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const index = characters.findIndex((character)=>character.id === id);
        if (index === -1) return false;
        characters.splice(index, 1);
        writeJsonFile(CHARACTERS_FILE, characters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const filteredCharacters = characters.filter((character)=>character.novelId !== novelId);
        writeJsonFile(CHARACTERS_FILE, filteredCharacters);
        return true;
    }
};
const presetDb = {
    getAll: ()=>readJsonFile(PRESETS_FILE),
    getById: (id)=>{
        const presets = readJsonFile(PRESETS_FILE);
        return presets.find((preset)=>preset.id === id);
    },
    create: (preset)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const newPreset = {
            ...preset,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        presets.push(newPreset);
        writeJsonFile(PRESETS_FILE, presets);
        return newPreset;
    },
    update: (id, updates)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const index = presets.findIndex((preset)=>preset.id === id);
        if (index === -1) return null;
        presets[index] = {
            ...presets[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(PRESETS_FILE, presets);
        return presets[index];
    },
    delete: (id)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const index = presets.findIndex((preset)=>preset.id === id);
        if (index === -1) return false;
        presets.splice(index, 1);
        writeJsonFile(PRESETS_FILE, presets);
        return true;
    }
};
}),
"[project]/src/lib/gemini.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// Gemini API 集成 - 多Key池管理
// API Keys配置 - 第一个key是其他4个的4倍强度
__turbopack_context__.s([
    "PRESET_RULES",
    ()=>PRESET_RULES,
    "addCustomPreset",
    ()=>addCustomPreset,
    "getApiKeyStats",
    ()=>getApiKeyStats,
    "loadCustomPresets",
    ()=>loadCustomPresets,
    "resetApiKeyStats",
    ()=>resetApiKeyStats,
    "rewriteChapters",
    ()=>rewriteChapters,
    "rewriteText",
    ()=>rewriteText,
    "testGeminiConnection",
    ()=>testGeminiConnection
]);
const API_KEYS = [
    {
        key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw',
        name: 'My First Project',
        weight: 4,
        requestCount: 0,
        lastUsed: 0,
        cooldownUntil: 0
    },
    {
        key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y',
        name: 'ankibot',
        weight: 1,
        requestCount: 0,
        lastUsed: 0,
        cooldownUntil: 0
    },
    {
        key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY',
        name: 'Generative Language Client',
        weight: 1,
        requestCount: 0,
        lastUsed: 0,
        cooldownUntil: 0
    },
    {
        key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc',
        name: 'In The Novel',
        weight: 1,
        requestCount: 0,
        lastUsed: 0,
        cooldownUntil: 0
    },
    {
        key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk',
        name: 'chat',
        weight: 1,
        requestCount: 0,
        lastUsed: 0,
        cooldownUntil: 0
    }
];
// API配置
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent';
const REQUEST_DELAY = 1000; // 请求间隔（毫秒）
const COOLDOWN_DURATION = 60000; // 429错误后的冷却时间（毫秒）
const MAX_RETRIES = 3; // 最大重试次数
// API Key管理类
class ApiKeyManager {
    keys = [
        ...API_KEYS
    ];
    // 获取最佳可用的API Key
    getBestAvailableKey() {
        const now = Date.now();
        // 过滤掉冷却中的key
        const availableKeys = this.keys.filter((key)=>key.cooldownUntil <= now);
        if (availableKeys.length === 0) {
            // 如果所有key都在冷却中，返回冷却时间最短的
            return this.keys.reduce((min, key)=>key.cooldownUntil < min.cooldownUntil ? key : min);
        }
        // 根据权重和使用频率选择最佳key
        const bestKey = availableKeys.reduce((best, key)=>{
            const keyScore = key.weight / (key.requestCount + 1);
            const bestScore = best.weight / (best.requestCount + 1);
            return keyScore > bestScore ? key : best;
        });
        return bestKey;
    }
    // 记录API使用
    recordUsage(keyName, success) {
        const key = this.keys.find((k)=>k.name === keyName);
        if (key) {
            key.requestCount++;
            key.lastUsed = Date.now();
            if (!success) {
                // 如果失败，设置冷却时间
                key.cooldownUntil = Date.now() + COOLDOWN_DURATION;
            }
        }
    }
    // 获取统计信息
    getStats() {
        return this.keys.map((key)=>({
                name: key.name,
                requestCount: key.requestCount,
                weight: key.weight,
                isAvailable: key.cooldownUntil <= Date.now(),
                cooldownRemaining: Math.max(0, key.cooldownUntil - Date.now())
            }));
    }
}
const keyManager = new ApiKeyManager();
// 构建改写提示词
function buildPrompt(request) {
    const { originalText, rules, chapterTitle, chapterNumber } = request;
    return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：

改写规则：
${rules}

${chapterTitle ? `${chapterTitle}` : ''}

原文内容：
${originalText}

请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：
1. 遵循所有指定的改写规则
2. 保持原文的基本故事框架（除非规则要求修改）
3. 确保文字流畅自然
4. 保持角色的基本性格特征（除非规则要求修改）

请直接输出改写后的内容，不要添加任何解释或说明：`;
}
async function rewriteText(request) {
    const startTime = Date.now();
    let lastError = '';
    for(let attempt = 0; attempt < MAX_RETRIES; attempt++){
        try {
            const apiKey = keyManager.getBestAvailableKey();
            // 如果key在冷却中，等待一段时间
            if (apiKey.cooldownUntil > Date.now()) {
                const waitTime = Math.min(apiKey.cooldownUntil - Date.now(), 5000); // 最多等待5秒
                await new Promise((resolve)=>setTimeout(resolve, waitTime));
            }
            const prompt = buildPrompt(request);
            const response = await fetch(`${GEMINI_API_URL}?key=${apiKey.key}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents: [
                        {
                            parts: [
                                {
                                    text: prompt
                                }
                            ]
                        }
                    ],
                    generationConfig: {
                        temperature: 0.6,
                        topK: 10,
                        topP: 0.8,
                        "thinkingConfig": {
                            "thinkingBudget": 0
                        }
                    }
                })
            });
            const processingTime = Date.now() - startTime;
            if (response.status === 429) {
                // 429错误，记录失败并尝试下一个key
                keyManager.recordUsage(apiKey.name, false);
                lastError = `API限流 (${apiKey.name})`;
                if (attempt < MAX_RETRIES - 1) {
                    await new Promise((resolve)=>setTimeout(resolve, REQUEST_DELAY * (attempt + 1)));
                    continue;
                }
            }
            if (!response.ok) {
                const errorData = await response.text();
                console.error('Gemini API error:', errorData);
                keyManager.recordUsage(apiKey.name, false);
                lastError = `API请求失败: ${response.status} ${response.statusText}`;
                if (attempt < MAX_RETRIES - 1) {
                    await new Promise((resolve)=>setTimeout(resolve, REQUEST_DELAY));
                    continue;
                }
                return {
                    rewrittenText: '',
                    success: false,
                    error: lastError,
                    apiKeyUsed: apiKey.name,
                    processingTime
                };
            }
            const data = await response.json();
            // 记录成功使用
            keyManager.recordUsage(apiKey.name, true);
            if (!data.candidates || data.candidates.length === 0) {
                return {
                    rewrittenText: '',
                    success: false,
                    error: '没有收到有效的响应内容',
                    apiKeyUsed: apiKey.name,
                    processingTime
                };
            }
            const candidate = data.candidates[0];
            if (candidate.finishReason === 'SAFETY') {
                return {
                    rewrittenText: '',
                    success: false,
                    error: '内容被安全过滤器拦截，请调整改写规则或原文内容',
                    apiKeyUsed: apiKey.name,
                    processingTime
                };
            }
            if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
                return {
                    rewrittenText: '',
                    success: false,
                    error: '响应内容格式错误',
                    apiKeyUsed: apiKey.name,
                    processingTime
                };
            }
            const rewrittenText = candidate.content.parts[0].text;
            // 尝试从响应中提取token使用信息
            const tokensUsed = data.usageMetadata?.totalTokenCount || 0;
            return {
                rewrittenText: rewrittenText.trim(),
                success: true,
                apiKeyUsed: apiKey.name,
                tokensUsed,
                model: 'gemini-2.5-flash-lite',
                processingTime
            };
        } catch (error) {
            console.error('Gemini API调用错误:', error);
            lastError = `网络错误: ${error instanceof Error ? error.message : '未知错误'}`;
            if (attempt < MAX_RETRIES - 1) {
                await new Promise((resolve)=>setTimeout(resolve, REQUEST_DELAY * (attempt + 1)));
            }
        }
    }
    return {
        rewrittenText: '',
        success: false,
        error: `重试${MAX_RETRIES}次后仍然失败: ${lastError}`,
        processingTime: Date.now() - startTime
    };
}
async function rewriteChapters(chapters, rules, onProgress, onChapterComplete, concurrency = 8 // 降低并发数以避免429错误
) {
    const results = new Array(chapters.length);
    let completed = 0;
    let totalTokensUsed = 0;
    const startTime = Date.now();
    // 使用更保守的并发策略
    const semaphore = new Semaphore(concurrency);
    const processChapter = async (chapter, index)=>{
        await semaphore.acquire();
        const chapterStartTime = Date.now();
        try {
            const result = await rewriteText({
                originalText: chapter.content,
                rules,
                chapterTitle: chapter.title,
                chapterNumber: chapter.number
            });
            const chapterProcessingTime = Date.now() - chapterStartTime;
            if (result.tokensUsed) {
                totalTokensUsed += result.tokensUsed;
            }
            const chapterResult = {
                success: result.success,
                content: result.rewrittenText,
                error: result.error,
                details: {
                    apiKeyUsed: result.apiKeyUsed,
                    tokensUsed: result.tokensUsed,
                    model: result.model,
                    processingTime: chapterProcessingTime,
                    chapterNumber: chapter.number,
                    chapterTitle: chapter.title
                }
            };
            results[index] = chapterResult;
            completed++;
            // 实时回调章节完成
            if (onChapterComplete) {
                onChapterComplete(index, chapterResult);
            }
            // 更新进度，包含详细信息
            if (onProgress) {
                const progressDetails = {
                    completed,
                    total: chapters.length,
                    totalTokensUsed,
                    totalTime: Date.now() - startTime,
                    averageTimePerChapter: (Date.now() - startTime) / completed,
                    apiKeyStats: keyManager.getStats(),
                    currentChapter: {
                        number: chapter.number,
                        title: chapter.title,
                        processingTime: chapterProcessingTime,
                        apiKey: result.apiKeyUsed,
                        tokens: result.tokensUsed
                    }
                };
                onProgress(completed / chapters.length * 100, chapter.number, progressDetails);
            }
            // 添加请求间隔
            await new Promise((resolve)=>setTimeout(resolve, REQUEST_DELAY));
            return result;
        } catch (error) {
            const chapterErrorTime = Date.now();
            const errorResult = {
                success: false,
                content: '',
                error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,
                details: {
                    chapterNumber: chapter.number,
                    chapterTitle: chapter.title,
                    processingTime: chapterErrorTime - chapterStartTime
                }
            };
            results[index] = errorResult;
            completed++;
            if (onChapterComplete) {
                onChapterComplete(index, errorResult);
            }
            if (onProgress) {
                const progressDetails = {
                    completed,
                    total: chapters.length,
                    totalTokensUsed,
                    totalTime: Date.now() - startTime,
                    averageTimePerChapter: (Date.now() - startTime) / completed,
                    apiKeyStats: keyManager.getStats(),
                    currentChapter: {
                        number: chapter.number,
                        title: chapter.title,
                        error: error instanceof Error ? error.message : '未知错误'
                    }
                };
                onProgress(completed / chapters.length * 100, chapter.number, progressDetails);
            }
            return null;
        } finally{
            semaphore.release();
        }
    };
    // 并发处理所有章节
    const promises = chapters.map((chapter, index)=>processChapter(chapter, index));
    await Promise.all(promises);
    return results;
}
// 信号量类，用于控制并发
class Semaphore {
    permits;
    waitQueue = [];
    constructor(permits){
        this.permits = permits;
    }
    async acquire() {
        if (this.permits > 0) {
            this.permits--;
            return Promise.resolve();
        }
        return new Promise((resolve)=>{
            this.waitQueue.push(resolve);
        });
    }
    release() {
        this.permits++;
        if (this.waitQueue.length > 0) {
            const resolve = this.waitQueue.shift();
            if (resolve) {
                this.permits--;
                resolve();
            }
        }
    }
}
async function testGeminiConnection() {
    try {
        const testResult = await rewriteText({
            originalText: '这是一个测试文本。',
            rules: '保持原文不变'
        });
        return {
            success: testResult.success,
            error: testResult.error,
            details: {
                apiKeyUsed: testResult.apiKeyUsed,
                tokensUsed: testResult.tokensUsed,
                model: testResult.model,
                processingTime: testResult.processingTime,
                apiKeyStats: keyManager.getStats()
            }
        };
    } catch (error) {
        return {
            success: false,
            error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
            details: {
                apiKeyStats: keyManager.getStats()
            }
        };
    }
}
function getApiKeyStats() {
    return keyManager.getStats();
}
function resetApiKeyStats() {
    API_KEYS.forEach((key)=>{
        key.requestCount = 0;
        key.lastUsed = 0;
        key.cooldownUntil = 0;
    });
}
let PRESET_RULES = {
    romance_focus: {
        name: '感情戏增强',
        description: '扩写男女主互动内容，对非感情戏部分一笔带过',
        rules: `请按照以下规则改写：
1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节
2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过
3. 增加角色间的情感张力和暧昧氛围
4. 保持故事主线不变，但重点突出感情发展`
    },
    character_fix: {
        name: '人设修正',
        description: '修正主角人设和对话风格',
        rules: `请按照以下规则改写：
1. 修正主角的性格设定，使其更加立体和讨喜
2. 改善对话风格，使其更加自然流畅
3. 去除过于中二或不合理的行为描写
4. 保持角色的核心特征，但优化表现方式`
    },
    toxic_content_removal: {
        name: '毒点清除',
        description: '移除送女、绿帽等毒点情节',
        rules: `请按照以下规则改写：
1. 完全移除或修改送女、绿帽、圣母等毒点情节
2. 删除或改写让读者不适的桥段
3. 保持故事逻辑的完整性
4. 用更合理的情节替代被删除的内容`
    },
    pacing_improvement: {
        name: '节奏优化',
        description: '优化故事节奏，删除拖沓内容',
        rules: `请按照以下规则改写：
1. 删除重复和拖沓的描写
2. 加快故事节奏，突出重点情节
3. 简化过于冗长的对话和心理描写
4. 保持故事的紧凑性和可读性`
    },
    custom: {
        name: '自定义规则',
        description: '用户自定义的改写规则',
        rules: ''
    }
};
function loadCustomPresets() {
    // 这个函数只在服务端使用，客户端组件应该通过 API 获取预设
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    try {
        const { presetDb } = __turbopack_context__.r("[project]/src/lib/database.ts [app-route] (ecmascript)");
        const customPresets = presetDb.getAll();
        // 将数据库中的预设添加到 PRESET_RULES
        customPresets.forEach((preset)=>{
            PRESET_RULES[`custom_${preset.id}`] = {
                name: preset.name,
                description: preset.description,
                rules: preset.rules
            };
        });
    } catch (error) {
        console.error('加载自定义预设失败:', error);
    }
}
function addCustomPreset(name, description, rules) {
    const key = `custom_${Date.now()}`;
    PRESET_RULES = {
        ...PRESET_RULES,
        [key]: {
            name,
            description,
            rules
        }
    };
    return key;
}
}),
"[project]/src/lib/file-manager.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FileManager",
    ()=>FileManager,
    "fileManager",
    ()=>fileManager
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
class FileManager {
    static instance;
    baseDir;
    constructor(){
        this.baseDir = process.cwd();
    }
    static getInstance() {
        if (!FileManager.instance) {
            FileManager.instance = new FileManager();
        }
        return FileManager.instance;
    }
    // 确保目录存在
    ensureDir(dirPath) {
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(dirPath, {
                recursive: true
            });
        }
    }
    // 获取novels目录路径
    getNovelsDir() {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, '..', 'novels');
    }
    // 获取chapters目录路径
    getChaptersDir() {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, '..', 'chapters');
    }
    // 获取数据目录路径
    getDataDir() {
        const dataDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, 'data');
        this.ensureDir(dataDir);
        return dataDir;
    }
    // 获取改写结果目录路径
    getRewrittenDir() {
        const rewrittenDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.getDataDir(), 'rewritten');
        this.ensureDir(rewrittenDir);
        return rewrittenDir;
    }
    // 获取特定小说的改写结果目录
    getNovelRewrittenDir(novelTitle) {
        const novelDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.getRewrittenDir(), this.sanitizeFilename(novelTitle));
        this.ensureDir(novelDir);
        return novelDir;
    }
    // 获取特定小说的章节目录
    getNovelChaptersDir(novelTitle) {
        const chaptersDir = this.getChaptersDir();
        this.ensureDir(chaptersDir);
        const novelDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(chaptersDir, this.sanitizeFilename(novelTitle));
        this.ensureDir(novelDir);
        return novelDir;
    }
    // 清理文件名中的非法字符
    sanitizeFilename(filename) {
        return filename.replace(/[<>:"/\\|?*]/g, '_').trim();
    }
    // 读取文件内容
    readFile(filePath) {
        try {
            return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
        } catch (error) {
            console.error(`读取文件失败: ${filePath}`, error);
            throw error;
        }
    }
    // 写入文件内容
    writeFile(filePath, content) {
        try {
            const dir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(filePath);
            this.ensureDir(dir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, content, 'utf-8');
        } catch (error) {
            console.error(`写入文件失败: ${filePath}`, error);
            throw error;
        }
    }
    // 检查文件是否存在
    fileExists(filePath) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath);
    }
    // 获取目录中的所有文件
    listFiles(dirPath, extensions) {
        try {
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                return [];
            }
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(dirPath);
            if (extensions) {
                return files.filter((file)=>{
                    const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(file).toLowerCase();
                    return extensions.includes(ext);
                });
            }
            return files;
        } catch (error) {
            console.error(`读取目录失败: ${dirPath}`, error);
            return [];
        }
    }
    // 获取文件信息
    getFileStats(filePath) {
        try {
            return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filePath);
        } catch (error) {
            console.error(`获取文件信息失败: ${filePath}`, error);
            return null;
        }
    }
    // 删除文件
    deleteFile(filePath) {
        try {
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(filePath);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`删除文件失败: ${filePath}`, error);
            return false;
        }
    }
    // 删除目录
    deleteDir(dirPath) {
        try {
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].rmSync(dirPath, {
                    recursive: true,
                    force: true
                });
                return true;
            }
            return false;
        } catch (error) {
            console.error(`删除目录失败: ${dirPath}`, error);
            return false;
        }
    }
    // 复制文件
    copyFile(srcPath, destPath) {
        try {
            const destDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(destPath);
            this.ensureDir(destDir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].copyFileSync(srcPath, destPath);
            return true;
        } catch (error) {
            console.error(`复制文件失败: ${srcPath} -> ${destPath}`, error);
            return false;
        }
    }
    // 移动文件
    moveFile(srcPath, destPath) {
        try {
            const destDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(destPath);
            this.ensureDir(destDir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].renameSync(srcPath, destPath);
            return true;
        } catch (error) {
            console.error(`移动文件失败: ${srcPath} -> ${destPath}`, error);
            return false;
        }
    }
    // 获取目录大小
    getDirSize(dirPath) {
        let totalSize = 0;
        try {
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                return 0;
            }
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(dirPath);
            for (const file of files){
                const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file);
                const stats = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filePath);
                if (stats.isDirectory()) {
                    totalSize += this.getDirSize(filePath);
                } else {
                    totalSize += stats.size;
                }
            }
        } catch (error) {
            console.error(`计算目录大小失败: ${dirPath}`, error);
        }
        return totalSize;
    }
    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = [
            'B',
            'KB',
            'MB',
            'GB'
        ];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    // 创建备份
    createBackup(filePath) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filePath);
            const baseName = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].basename(filePath, ext);
            const dir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(filePath);
            const backupPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dir, `${baseName}_backup_${timestamp}${ext}`);
            if (this.copyFile(filePath, backupPath)) {
                return backupPath;
            }
            return null;
        } catch (error) {
            console.error(`创建备份失败: ${filePath}`, error);
            return null;
        }
    }
    // 清理旧备份文件
    cleanupBackups(dirPath, maxBackups = 5) {
        try {
            const files = this.listFiles(dirPath);
            const backupFiles = files.filter((file)=>file.includes('_backup_')).map((file)=>({
                    name: file,
                    path: __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file),
                    stats: this.getFileStats(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file))
                })).filter((item)=>item.stats !== null).sort((a, b)=>b.stats.mtime.getTime() - a.stats.mtime.getTime());
            // 删除超出数量限制的备份文件
            if (backupFiles.length > maxBackups) {
                const filesToDelete = backupFiles.slice(maxBackups);
                for (const file of filesToDelete){
                    this.deleteFile(file.path);
                }
            }
        } catch (error) {
            console.error(`清理备份文件失败: ${dirPath}`, error);
        }
    }
}
const fileManager = FileManager.getInstance();
}),
"[project]/src/lib/novel-parser.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getAvailableNovels",
    ()=>getAvailableNovels,
    "isNovelParsed",
    ()=>isNovelParsed,
    "parseChapterRange",
    ()=>parseChapterRange,
    "parseNovelFile",
    ()=>parseNovelFile,
    "reparseNovel",
    ()=>reparseNovel
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/file-manager.ts [app-route] (ecmascript)");
;
;
;
;
// 分卷/分集匹配模式（用于分栏，不作为章节）
const VOLUME_PATTERNS = [
    /^\s*(?:第[一二三四五六七八九十百千万\d]+[卷集部])\s*.*$/gmi,
    /^\s*(?:[卷集部][一二三四五六七八九十百千万\d]+)\s*.*$/gmi
];
// 章节匹配模式
const CHAPTER_PATTERNS = [
    /^\s*(?:第[一二三四五六七八九十百千万\d]+[章节回])\s*.*$/gmi,
    /^\s*(?:Chapter\s+\d+)\s*.*$/gmi
];
async function parseNovelFile(filePath) {
    const filename = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].basename(filePath);
    const title = filename.replace(/\.(txt|md)$/i, '');
    // 读取文件内容
    const content = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
    // 创建小说记录
    const novel = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].create({
        title,
        filename
    });
    // 解析章节
    const chapters = parseChapters(content, novel.id);
    // 批量创建章节记录
    const createdChapters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chapterDb"].createBatch(chapters);
    // 更新小说的章节数量
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].update(novel.id, {
        chapterCount: createdChapters.length
    });
    // 保存章节文件
    await saveChapterFiles(createdChapters);
    return {
        novel: {
            ...novel,
            chapterCount: createdChapters.length
        },
        chapters: createdChapters
    };
}
// 解析章节内容
function parseChapters(content, novelId) {
    const chapters = [];
    // 首先识别分卷/分集标记
    const volumeMatches = findVolumeMarkers(content);
    // 然后在每个分卷内或整个文本中查找章节
    if (volumeMatches.length > 0) {
        // 有分卷的情况
        console.log(`Found ${volumeMatches.length} volumes`);
        let chapterNumber = 1;
        for(let i = 0; i < volumeMatches.length; i++){
            const volumeStart = volumeMatches[i].index;
            const volumeEnd = i + 1 < volumeMatches.length ? volumeMatches[i + 1].index : content.length;
            const volumeContent = content.slice(volumeStart, volumeEnd);
            // 在分卷内查找章节
            const volumeChapters = parseChaptersInVolume(volumeContent, novelId, chapterNumber, volumeMatches[i].title);
            chapters.push(...volumeChapters);
            chapterNumber += volumeChapters.length;
        }
    } else {
        // 没有分卷，直接解析章节
        const directChapters = parseChaptersInVolume(content, novelId, 1);
        chapters.push(...directChapters);
    }
    console.log(`Successfully parsed ${chapters.length} chapters`);
    return chapters;
}
// 查找分卷标记
function findVolumeMarkers(content) {
    const volumeMarkers = [];
    for (const pattern of VOLUME_PATTERNS){
        const matches = Array.from(content.matchAll(pattern));
        for (const match of matches){
            volumeMarkers.push({
                index: match.index,
                title: extractChapterTitle(match[0])
            });
        }
    }
    // 按位置排序
    return volumeMarkers.sort((a, b)=>a.index - b.index);
}
// 在指定内容中解析章节
function parseChaptersInVolume(content, novelId, startChapterNumber, volumeTitle) {
    const chapters = [];
    // 尝试不同的章节匹配模式
    let bestPattern = null;
    let bestMatches = [];
    for (const pattern of CHAPTER_PATTERNS){
        const matches = Array.from(content.matchAll(pattern));
        console.log(`Pattern ${pattern.source} found ${matches.length} matches in ${volumeTitle || 'content'}`);
        if (matches.length > bestMatches.length) {
            bestPattern = pattern;
            bestMatches = matches;
        }
    }
    if (!bestPattern || bestMatches.length === 0) {
        // 如果没有找到章节标记，将整个内容作为一章（但要检查长度）
        if (content.trim().length > 100) {
            chapters.push({
                novelId,
                chapterNumber: startChapterNumber,
                title: volumeTitle || '全文',
                content: content.trim(),
                filename: `chapter_${startChapterNumber}.txt`
            });
        }
        return chapters;
    }
    // 根据匹配结果分割章节
    const chapterPositions = bestMatches.map((match, index)=>({
            index: match.index,
            title: extractChapterTitle(match[0]),
            chapterNumber: startChapterNumber + index
        }));
    for(let i = 0; i < chapterPositions.length; i++){
        const currentPos = chapterPositions[i];
        const nextPos = chapterPositions[i + 1];
        const startIndex = currentPos.index;
        const endIndex = nextPos ? nextPos.index : content.length;
        const chapterContent = content.slice(startIndex, endIndex).trim();
        if (chapterContent.length > 100) {
            chapters.push({
                novelId,
                chapterNumber: currentPos.chapterNumber,
                title: currentPos.title || `第${currentPos.chapterNumber}章`,
                content: chapterContent,
                filename: `chapter_${currentPos.chapterNumber}.txt`
            });
        }
    }
    return chapters;
}
// 提取章节标题
function extractChapterTitle(chapterText) {
    const lines = chapterText.split('\n');
    const firstLine = lines[0].trim();
    // 如果第一行看起来像标题，使用它
    if (firstLine.length < 100 && firstLine.length > 0) {
        return firstLine;
    }
    // 否则尝试从前几行中找到标题
    for(let i = 0; i < Math.min(3, lines.length); i++){
        const line = lines[i].trim();
        if (line.length > 0 && line.length < 100) {
            return line;
        }
    }
    return '未命名章节';
}
// 保存章节文件到chapters目录
async function saveChapterFiles(chapters) {
    // 为每个小说创建子目录
    const novelIds = [
        ...new Set(chapters.map((ch)=>ch.novelId))
    ];
    for (const novelId of novelIds){
        const novel = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getById(novelId);
        if (!novel) continue;
        const novelDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelChaptersDir(novel.title);
        // 保存该小说的所有章节
        const novelChapters = chapters.filter((ch)=>ch.novelId === novelId);
        for (const chapter of novelChapters){
            const chapterPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(novelDir, chapter.filename);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].writeFile(chapterPath, chapter.content);
        }
    }
}
function getAvailableNovels() {
    const novelsDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelsDir();
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].listFiles(novelsDir, [
        '.txt',
        '.md'
    ]);
}
function isNovelParsed(filename) {
    const novels = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getAll();
    return novels.some((novel)=>novel.filename === filename);
}
async function reparseNovel(filename) {
    const novelsDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelsDir();
    const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(novelsDir, filename);
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].fileExists(filePath)) {
        return null;
    }
    // 删除旧的小说和章节数据
    const existingNovels = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getAll();
    const existingNovel = existingNovels.find((novel)=>novel.filename === filename);
    if (existingNovel) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chapterDb"].deleteByNovelId(existingNovel.id);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].delete(existingNovel.id);
    }
    // 重新解析
    return await parseNovelFile(filePath);
}
function parseChapterRange(rangeStr, maxChapter) {
    const chapters = [];
    const parts = rangeStr.split(',').map((part)=>part.trim());
    for (const part of parts){
        if (part.includes('-')) {
            // 范围格式 (例如: "1-5")
            const [start, end] = part.split('-').map((num)=>parseInt(num.trim()));
            if (!isNaN(start) && !isNaN(end) && start <= end) {
                for(let i = start; i <= Math.min(end, maxChapter); i++){
                    if (i > 0 && !chapters.includes(i)) {
                        chapters.push(i);
                    }
                }
            }
        } else {
            // 单个章节
            const chapterNum = parseInt(part);
            if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {
                chapters.push(chapterNum);
            }
        }
    }
    return chapters.sort((a, b)=>a - b);
}
}),
"[project]/src/app/api/rewrite/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gemini$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/gemini.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$novel$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/novel-parser.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/file-manager.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
;
;
async function POST(request) {
    try {
        const { novelId, chapterRange, rules } = await request.json();
        if (!novelId || !chapterRange || !rules) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '参数不完整'
            }, {
                status: 400
            });
        }
        // 获取小说信息
        const novel = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getById(novelId);
        if (!novel) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '小说不存在'
            }, {
                status: 404
            });
        }
        // 获取所有章节
        const allChapters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chapterDb"].getByNovelId(novelId);
        if (allChapters.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '该小说没有章节'
            }, {
                status: 404
            });
        }
        // 解析章节范围
        const chapterNumbers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$novel$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseChapterRange"])(chapterRange, allChapters.length);
        if (chapterNumbers.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '无效的章节范围'
            }, {
                status: 400
            });
        }
        // 获取要改写的章节
        const chaptersToRewrite = allChapters.filter((chapter)=>chapterNumbers.includes(chapter.chapterNumber));
        if (chaptersToRewrite.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '没有找到指定的章节'
            }, {
                status: 404
            });
        }
        // 创建改写任务
        const job = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].create({
            novelId,
            chapters: chapterNumbers,
            ruleId: 'custom',
            status: 'pending',
            progress: 0,
            details: {
                totalChapters: chaptersToRewrite.length,
                completedChapters: 0,
                failedChapters: 0,
                totalTokensUsed: 0,
                totalProcessingTime: 0,
                averageTimePerChapter: 0,
                apiKeyStats: [],
                chapterResults: [],
                model: 'gemini-2.5-flash-lite',
                concurrency: 3
            }
        });
        // 异步执行改写任务
        executeRewriteJob(job.id, chaptersToRewrite, rules, novel.title);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                jobId: job.id,
                chaptersCount: chaptersToRewrite.length,
                message: '改写任务已创建，正在处理中...'
            }
        });
    } catch (error) {
        console.error('创建改写任务失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '创建改写任务失败'
        }, {
            status: 500
        });
    }
}
// 异步执行改写任务 - 支持实时写入和详细进度
async function executeRewriteJob(jobId, chapters, rules, novelTitle) {
    const startTime = Date.now();
    try {
        // 更新任务状态为处理中
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
            status: 'processing',
            progress: 0,
            details: {
                totalChapters: chapters.length,
                completedChapters: 0,
                failedChapters: 0,
                totalTokensUsed: 0,
                totalProcessingTime: 0,
                averageTimePerChapter: 0,
                apiKeyStats: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId)?.details?.apiKeyStats || [],
                chapterResults: [],
                model: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId)?.details?.model || 'gemini-2.5-flash-lite',
                concurrency: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId)?.details?.concurrency || 3
            }
        });
        // 准备章节数据
        const chaptersData = chapters.map((chapter)=>({
                content: chapter.content,
                title: chapter.title,
                number: chapter.chapterNumber
            }));
        // 创建输出目录
        const outputDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelRewrittenDir(novelTitle);
        // 执行改写（降低并发数以避免429错误）
        const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gemini$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rewriteChapters"])(chaptersData, rules, // 进度回调 - 更新详细信息
        (progress, _currentChapter, details)=>{
            const currentJob = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId);
            if (currentJob && details) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
                    progress: Math.round(progress),
                    details: {
                        totalChapters: currentJob.details?.totalChapters || chapters.length,
                        completedChapters: details.completed,
                        failedChapters: currentJob.details?.failedChapters || 0,
                        totalTokensUsed: details.totalTokensUsed,
                        totalProcessingTime: details.totalTime,
                        averageTimePerChapter: details.averageTimePerChapter,
                        apiKeyStats: details.apiKeyStats,
                        chapterResults: currentJob.details?.chapterResults || [],
                        model: currentJob.details?.model || 'gemini-2.5-flash-lite',
                        concurrency: currentJob.details?.concurrency || 3
                    }
                });
            }
        }, // 章节完成回调 - 实时写入
        (chapterIndex, result)=>{
            if (result.success) {
                // 立即写入完成的章节
                const chapter = chapters[chapterIndex];
                const filename = `chapter_${chapter.chapterNumber}_rewritten.txt`;
                const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(outputDir, filename);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].writeFile(filePath, result.content);
            }
            // 更新章节结果到数据库
            const currentJob = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId);
            if (currentJob?.details) {
                const chapterResults = [
                    ...currentJob.details.chapterResults || []
                ];
                chapterResults[chapterIndex] = {
                    chapterNumber: result.details?.chapterNumber || chapterIndex + 1,
                    chapterTitle: result.details?.chapterTitle || `第${chapterIndex + 1}章`,
                    success: result.success,
                    error: result.error,
                    apiKeyUsed: result.details?.apiKeyUsed,
                    tokensUsed: result.details?.tokensUsed,
                    processingTime: result.details?.processingTime,
                    completedAt: new Date().toISOString()
                };
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
                    details: {
                        ...currentJob.details,
                        chapterResults,
                        failedChapters: chapterResults.filter((r)=>!r.success).length
                    }
                });
            }
        }, 3 // 并发数量
        );
        // 统计结果
        let successCount = 0;
        let totalTokensUsed = 0;
        const resultSummary = [];
        for(let i = 0; i < results.length; i++){
            const result = results[i];
            const chapter = chapters[i];
            if (result.success) {
                successCount++;
            }
            if (result.details?.tokensUsed) {
                totalTokensUsed += result.details.tokensUsed;
            }
            resultSummary.push({
                chapterNumber: chapter.chapterNumber,
                chapterTitle: chapter.title,
                success: result.success,
                error: result.error,
                apiKeyUsed: result.details?.apiKeyUsed,
                tokensUsed: result.details?.tokensUsed,
                processingTime: result.details?.processingTime
            });
        }
        // 保存详细的结果摘要
        const summaryPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(outputDir, 'rewrite_summary.json');
        const totalProcessingTime = Date.now() - startTime;
        const summaryData = JSON.stringify({
            jobId,
            novelTitle,
            rules,
            totalChapters: chapters.length,
            successCount,
            failedCount: chapters.length - successCount,
            totalTokensUsed,
            totalProcessingTime,
            averageTimePerChapter: totalProcessingTime / chapters.length,
            results: resultSummary,
            completedAt: new Date().toISOString(),
            model: 'gemini-2.5-flash-lite',
            concurrency: 3
        }, null, 2);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].writeFile(summaryPath, summaryData);
        // 更新任务状态为完成
        const currentJob = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
            status: 'completed',
            progress: 100,
            result: `成功改写 ${successCount}/${chapters.length} 章节，结果保存在: ${outputDir}`,
            details: {
                totalChapters: chapters.length,
                completedChapters: successCount,
                failedChapters: chapters.length - successCount,
                totalTokensUsed,
                totalProcessingTime,
                averageTimePerChapter: totalProcessingTime / chapters.length,
                apiKeyStats: currentJob?.details?.apiKeyStats || [],
                chapterResults: resultSummary,
                model: currentJob?.details?.model || 'gemini-2.5-flash-lite',
                concurrency: currentJob?.details?.concurrency || 3
            }
        });
    } catch (error) {
        console.error('执行改写任务失败:', error);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
            status: 'failed',
            result: `改写失败: ${error instanceof Error ? error.message : '未知错误'}`
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__573e31fb._.js.map