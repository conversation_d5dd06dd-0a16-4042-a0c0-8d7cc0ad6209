'use client';

import { useState, useEffect } from 'react';
import { Settings, Wand2, Save } from 'lucide-react';
import { PRESET_RULES } from '@/lib/gemini';

interface RuleEditorProps {
  rules: string;
  onRulesChange: (rules: string) => void;
  disabled?: boolean;
  onSaveToPreset?: (rules: string) => void;
}

interface Preset {
  id: string;
  name: string;
  description: string;
  rules: string;
}

export default function RuleEditor({ rules, onRulesChange, disabled, onSaveToPreset }: RuleEditorProps) {
  const [showPresets, setShowPresets] = useState(false);
  const [customPresets, setCustomPresets] = useState<Preset[]>([]);
  const [allPresets, setAllPresets] = useState<Record<string, { name: string; description: string; rules: string }>>({});

  // 加载自定义预设
  useEffect(() => {
    loadCustomPresets();
  }, []);

  const loadCustomPresets = async () => {
    try {
      const response = await fetch('/api/presets');
      const result = await response.json();
      if (result.success) {
        setCustomPresets(result.data);

        // 合并内置预设和自定义预设
        const combined = { ...PRESET_RULES };
        result.data.forEach((preset: Preset) => {
          combined[`custom_${preset.id}`] = {
            name: preset.name,
            description: preset.description,
            rules: preset.rules
          };
        });
        setAllPresets(combined);
      }
    } catch (error) {
      console.error('加载自定义预设失败:', error);
      setAllPresets(PRESET_RULES);
    }
  };

  const handlePresetSelect = (presetKey: string) => {
    const preset = allPresets[presetKey];
    if (preset) {
      onRulesChange(preset.rules);
      setShowPresets(false);
    }
  };

  const handleSaveToPreset = async () => {
    if (rules.trim() && onSaveToPreset) {
      await onSaveToPreset(rules);
      // 重新加载预设列表
      await loadCustomPresets();
    }
  };

  const presetButtons = Object.entries(allPresets).filter(([key]) => key !== 'custom');

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center">
          <Settings className="mr-2" size={20} />
          改写规则
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={handleSaveToPreset}
            disabled={disabled || !rules.trim()}
            className="p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
            title="保存为预设"
          >
            <Save size={16} />
          </button>
          <button
            onClick={() => setShowPresets(!showPresets)}
            disabled={disabled}
            className="p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
            title="预设规则"
          >
            <Wand2 size={16} />
          </button>
        </div>
      </div>



      {/* 预设规则 */}
      {showPresets && (
        <div className="mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <h3 className="font-medium text-gray-800 mb-2 text-sm">选择预设规则</h3>
          <div className="grid grid-cols-1 gap-1">
            {presetButtons.map(([key, preset]) => (
              <button
                key={key}
                onClick={() => handlePresetSelect(key)}
                disabled={disabled}
                className="text-left p-2 border border-gray-200 rounded hover:border-blue-300 hover:bg-blue-50 disabled:opacity-50 transition-colors"
              >
                <div className="font-medium text-gray-800 text-sm">{preset.name}</div>
                <div className="text-xs text-gray-600">{preset.description}</div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 规则编辑器 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          改写规则内容
        </label>
        <textarea
          value={rules}
          onChange={(e) => onRulesChange(e.target.value)}
          disabled={disabled}
          placeholder="请输入详细的改写规则，例如：&#10;&#10;1. 扩写男女主角之间的互动情节&#10;2. 对战斗场面一笔带过&#10;3. 增加情感描写和心理活动&#10;4. 修改不合理的人物行为&#10;..."
          className="w-full h-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none disabled:bg-gray-100"
        />
        <div className="mt-2 text-xs text-gray-500">
          {rules.length} 字符 • 建议详细描述改写要求以获得更好的效果
        </div>
      </div>


    </div>
  );
}
