[{"novelId": "mfwcydxezrka3jsfl1", "chapters": [1, 2, 3], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfwd0brwg9jbziwin", "createdAt": "2025-09-23T09:35:13.052Z", "updatedAt": "2025-09-23T09:35:38.465Z", "result": "成功改写 3/3 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfwkav1po6vf2u39jc", "createdAt": "2025-09-23T12:59:21.901Z", "updatedAt": "2025-09-23T12:59:24.350Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1, 2], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfwkuenrx64sqx171q", "createdAt": "2025-09-23T13:14:33.783Z", "updatedAt": "2025-09-23T13:14:49.213Z", "result": "成功改写 2/2 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1, 2], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfwl4437bqfb6k3g3ft", "createdAt": "2025-09-23T13:22:06.643Z", "updatedAt": "2025-09-23T13:22:36.920Z", "result": "成功改写 2/2 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1, 2], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfwl8mdqk3hp4w4ddzp", "createdAt": "2025-09-23T13:25:36.974Z", "updatedAt": "2025-09-23T13:25:52.881Z", "result": "成功改写 2/2 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxj9zhewme6f6vsk4", "createdAt": "2025-09-24T05:18:27.554Z", "updatedAt": "2025-09-24T05:18:34.342Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxjw1rnw2qr3lgdsfi", "createdAt": "2025-09-24T05:35:36.947Z", "updatedAt": "2025-09-24T05:35:56.120Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxkpt0uy1h8dneb7h", "createdAt": "2025-09-24T05:58:45.294Z", "updatedAt": "2025-09-24T05:59:19.046Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxku40q8w0t9qz5h05", "createdAt": "2025-09-24T06:02:06.170Z", "updatedAt": "2025-09-24T06:02:09.468Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxkx85qbasapiih92c", "createdAt": "2025-09-24T06:04:31.502Z", "updatedAt": "2025-09-24T06:04:37.318Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxkzi4m5p0es4a7d86", "createdAt": "2025-09-24T06:06:17.734Z", "updatedAt": "2025-09-24T06:06:22.895Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxl0l5xc0958393djh", "createdAt": "2025-09-24T06:07:08.325Z", "updatedAt": "2025-09-24T06:07:25.755Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxl2ts8y4zgbzax21a", "createdAt": "2025-09-24T06:08:52.808Z", "updatedAt": "2025-09-24T06:09:26.452Z", "result": "成功改写 5/5 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxly8i8rxb8ko4lln", "createdAt": "2025-09-24T06:33:18.224Z", "updatedAt": "2025-09-24T06:36:09.406Z", "result": "成功改写 25/35 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "failed", "progress": 13, "details": {"totalChapters": 40, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 16214, "totalProcessingTime": 8783, "averageTimePerChapter": 1756.6, "apiKeyStats": [{"name": "My First Project", "requestCount": 4, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4133, "processingTime": 7724, "completedAt": "2025-09-24T09:13:29.297Z"}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4356, "processingTime": 3196, "completedAt": "2025-09-24T09:13:24.774Z"}, null, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3797, "processingTime": 4567, "completedAt": "2025-09-24T09:13:30.353Z"}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxro2hsc2uoz2w2wfw", "createdAt": "2025-09-24T09:13:21.568Z", "updatedAt": "2025-09-24T09:13:30.354Z", "result": "改写失败: Cannot read properties of null (reading 'success')"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "failed", "progress": 20, "details": {"totalChapters": 40, "completedChapters": 8, "failedChapters": 0, "totalTokensUsed": 31719, "totalProcessingTime": 14367, "averageTimePerChapter": 1795.875, "apiKeyStats": [{"name": "My First Project", "requestCount": 6, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 1, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3325, "processingTime": 3838, "completedAt": "2025-09-24T09:30:12.024Z"}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4826, "processingTime": 4253, "completedAt": "2025-09-24T09:30:12.444Z"}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4161, "processingTime": 4247, "completedAt": "2025-09-24T09:30:12.437Z"}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4297, "processingTime": 6704, "completedAt": "2025-09-24T09:30:19.737Z"}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4612, "processingTime": 3893, "completedAt": "2025-09-24T09:30:17.342Z"}, null, {"chapterNumber": 7, "chapterTitle": "第七章 三更", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4361, "processingTime": 4202, "completedAt": "2025-09-24T09:30:22.550Z"}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxs9n7ar7iyllh6ypl", "createdAt": "2025-09-24T09:30:08.182Z", "updatedAt": "2025-09-24T09:30:22.551Z", "result": "改写失败: Cannot read properties of null (reading 'success')"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 5, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 20239, "totalProcessingTime": 9550, "averageTimePerChapter": 1910, "apiKeyStats": [{"name": "My First Project", "requestCount": 5, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3285, "processingTime": 3218}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4615, "processingTime": 3400}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3939, "processingTime": 4205}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4073, "processingTime": 3416}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4327, "processingTime": 4117}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxsfqns5rpzs3ltefb", "createdAt": "2025-09-24T09:34:52.600Z", "updatedAt": "2025-09-24T09:35:02.153Z", "result": "成功改写 5/5 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 5, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 23650, "totalProcessingTime": 11727, "averageTimePerChapter": 2345.4, "apiKeyStats": [{"name": "My First Project", "requestCount": 5, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4551, "processingTime": 4937}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4940, "processingTime": 4153}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4550, "processingTime": 7614}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4407, "processingTime": 4002}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 5202, "processingTime": 4769}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxswqjjt5ufaqrad1d", "createdAt": "2025-09-24T09:48:05.599Z", "updatedAt": "2025-09-24T09:48:17.329Z", "result": "成功改写 5/5 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 5, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 17874, "totalProcessingTime": 8375, "averageTimePerChapter": 1675, "apiKeyStats": [{"name": "My First Project", "requestCount": 5, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 3, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 1, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4465, "processingTime": 6643, "completedAt": "2025-09-24T09:52:42.550Z"}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4517, "processingTime": 3412, "completedAt": "2025-09-24T09:52:39.319Z"}, null, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 4139, "processingTime": 3956, "completedAt": "2025-09-24T09:52:44.279Z"}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxt2j3zd1quh6bf66n", "createdAt": "2025-09-24T09:52:35.903Z", "updatedAt": "2025-09-24T09:52:44.281Z", "result": "改写失败: Cannot read properties of null (reading 'success')"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 5, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 19967, "totalProcessingTime": 10157, "averageTimePerChapter": 2031.4, "apiKeyStats": [{"name": "My First Project", "requestCount": 5, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 3, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 2, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 3, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 1, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4520, "processingTime": 6785, "completedAt": "2025-09-24T09:56:10.388Z"}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 5509, "processingTime": 4819, "completedAt": "2025-09-24T09:56:08.424Z"}, null, null, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "chat", "tokensUsed": 5138, "processingTime": 4317, "completedAt": "2025-09-24T09:56:13.758Z"}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxt6zdayla9urwqv9", "createdAt": "2025-09-24T09:56:03.598Z", "updatedAt": "2025-09-24T09:56:13.759Z", "result": "改写失败: Cannot read properties of null (reading 'success')"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 5, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 24388, "totalProcessingTime": 14044, "averageTimePerChapter": 2808.8, "apiKeyStats": [{"name": "My First Project", "requestCount": 5, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4619, "processingTime": 4998}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 5232, "processingTime": 5992}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4609, "processingTime": 5040}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4764, "processingTime": 6600}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 5164, "processingTime": 6977}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxtbmboomutmqh53y", "createdAt": "2025-09-24T09:59:39.972Z", "updatedAt": "2025-09-24T09:59:54.018Z", "result": "成功改写 5/5 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 5, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 18693, "totalProcessingTime": 9715, "averageTimePerChapter": 1943, "apiKeyStats": [{"name": "My First Project", "requestCount": 5, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 3, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 1, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4584, "processingTime": 5184, "completedAt": "2025-09-24T10:01:32.710Z"}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4820, "processingTime": 3743, "completedAt": "2025-09-24T10:01:31.270Z"}, null, null, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 5019, "processingTime": 4763, "completedAt": "2025-09-24T10:01:37.239Z"}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxtdxb78b30z2yjz64", "createdAt": "2025-09-24T10:01:27.523Z", "updatedAt": "2025-09-24T10:01:37.240Z", "result": "改写失败: Cannot read properties of null (reading 'success')"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 5, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 20041, "totalProcessingTime": 8214, "averageTimePerChapter": 1642.8, "apiKeyStats": [{"name": "My First Project", "requestCount": 5, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 3, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 2, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 3, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 2, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 3073, "processingTime": 1792}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4253, "processingTime": 2624}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 3660, "processingTime": 4581}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "chat", "tokensUsed": 4563, "processingTime": 4396}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "chat", "tokensUsed": 4492, "processingTime": 3499}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxtjhlhcoaocqpam9w", "createdAt": "2025-09-24T10:05:47.093Z", "updatedAt": "2025-09-24T10:05:55.310Z", "result": "成功改写 5/5 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 5, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 20033, "totalProcessingTime": 8522, "averageTimePerChapter": 1704.4, "apiKeyStats": [{"name": "My First Project", "requestCount": 10, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 3, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 2, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 3, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 2, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3193, "processingTime": 1896}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4600, "processingTime": 3122}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4119, "processingTime": 5160}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3555, "processingTime": 2278}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4566, "processingTime": 3383}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxtnx8785tucwgd1bv", "createdAt": "2025-09-24T10:09:13.975Z", "updatedAt": "2025-09-24T10:09:22.499Z", "result": "成功改写 5/5 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 5, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 20568, "totalProcessingTime": 12650, "averageTimePerChapter": 2530, "apiKeyStats": [{"name": "My First Project", "requestCount": 5, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3981, "processingTime": 6796}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4724, "processingTime": 5335}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3941, "processingTime": 7557}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3784, "processingTime": 5281}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4138, "processingTime": 3780}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxtsw3p6vr5nlrwbsd", "createdAt": "2025-09-24T10:13:05.797Z", "updatedAt": "2025-09-24T10:13:18.449Z", "result": "成功改写 5/5 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "failed", "progress": 40, "details": {"totalChapters": 40, "completedChapters": 16, "failedChapters": 0, "totalTokensUsed": 55690, "totalProcessingTime": 34491, "averageTimePerChapter": 2155.6875, "apiKeyStats": [{"name": "My First Project", "requestCount": 6, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 3, "weight": 1, "isAvailable": false, "cooldownRemaining": 52392}, {"name": "Generative Language Client", "requestCount": 2, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 1, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 2, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4042, "processingTime": 7340, "completedAt": "2025-09-24T10:19:06.090Z"}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4798, "processingTime": 5832, "completedAt": "2025-09-24T10:19:04.582Z"}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4003, "processingTime": 7889, "completedAt": "2025-09-24T10:19:06.640Z"}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4058, "processingTime": 7215, "completedAt": "2025-09-24T10:19:12.816Z"}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4280, "processingTime": 4894, "completedAt": "2025-09-24T10:19:12.002Z"}, {"chapterNumber": 6, "chapterTitle": "第六章 小殿下", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 5485, "processingTime": 7563, "completedAt": "2025-09-24T10:19:15.212Z"}, {"chapterNumber": 7, "chapterTitle": "第七章 三更", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4345, "processingTime": 5207, "completedAt": "2025-09-24T10:19:18.230Z"}, {"chapterNumber": 8, "chapterTitle": "第八章 榕树与日落", "success": true, "apiKeyUsed": "chat", "tokensUsed": 3949, "processingTime": 16369, "completedAt": "2025-09-24T10:19:30.200Z"}, {"chapterNumber": 9, "chapterTitle": "第九章 刀剑入夜", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3703, "processingTime": 2326, "completedAt": "2025-09-24T10:19:18.549Z"}, null, null, null, null, {"chapterNumber": 14, "chapterTitle": "第十四章 湖上狐影", "success": true, "apiKeyUsed": "chat", "tokensUsed": 4780, "processingTime": 4326, "completedAt": "2025-09-24T10:19:33.236Z"}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxu0gfmkf9kck9tva7", "createdAt": "2025-09-24T10:18:58.738Z", "updatedAt": "2025-09-24T10:19:33.237Z", "result": "改写失败: Cannot read properties of null (reading 'success')"}]