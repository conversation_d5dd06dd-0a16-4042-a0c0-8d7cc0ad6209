{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/NovelSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Novel } from '@/lib/database';\nimport { BookOpen, RefreshCw, Upload } from 'lucide-react';\n\ninterface NovelSelectorProps {\n  selectedNovel: Novel | null;\n  onNovelSelect: (novel: Novel | null) => void;\n  disabled?: boolean;\n}\n\ninterface NovelFile {\n  filename: string;\n  parsed: boolean;\n  novel: Novel | null;\n}\n\nexport default function NovelSelector({ selectedNovel, onNovelSelect, disabled }: NovelSelectorProps) {\n  const [novels, setNovels] = useState<Novel[]>([]);\n  const [availableFiles, setAvailableFiles] = useState<NovelFile[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [parsing, setParsing] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadNovels();\n  }, []);\n\n  const loadNovels = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/novels');\n      const result = await response.json();\n      \n      if (result.success) {\n        setNovels(result.data.novels);\n        setAvailableFiles(result.data.availableFiles);\n      } else {\n        console.error('加载小说列表失败:', result.error);\n      }\n    } catch (error) {\n      console.error('加载小说列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleParseNovel = async (filename: string, reparse = false) => {\n    setParsing(filename);\n    try {\n      const response = await fetch('/api/novels', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ filename, reparse }),\n      });\n\n      const result = await response.json();\n      \n      if (result.success) {\n        await loadNovels(); // 重新加载列表\n        alert(result.message);\n      } else {\n        alert(`解析失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('解析小说失败:', error);\n      alert('解析小说失败');\n    } finally {\n      setParsing(null);\n    }\n  };\n\n  const handleNovelSelect = (novel: Novel) => {\n    if (disabled) return;\n    onNovelSelect(novel);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h2 className=\"text-lg font-semibold text-gray-800 flex items-center\">\n          <BookOpen className=\"mr-2\" size={18} />\n          选择小说\n        </h2>\n        <button\n          onClick={loadNovels}\n          disabled={loading || disabled}\n          className=\"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n          title=\"刷新列表\"\n        >\n          <RefreshCw className={`${loading ? 'animate-spin' : ''}`} size={16} />\n        </button>\n      </div>\n\n      {loading ? (\n        <div className=\"text-center py-8 text-gray-500\">\n          加载中...\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {/* 已解析的小说 */}\n          {novels.length > 0 && (\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">已解析的小说</h3>\n              <div className=\"space-y-1\">\n                {novels.map((novel) => (\n                  <div\n                    key={novel.id}\n                    onClick={() => handleNovelSelect(novel)}\n                    className={`p-2 border rounded cursor-pointer transition-colors ${\n                      selectedNovel?.id === novel.id\n                        ? 'border-blue-500 bg-blue-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n                  >\n                    <div className=\"font-medium text-gray-800 text-sm\">{novel.title}</div>\n                    <div className=\"text-xs text-gray-500\">\n                      {novel.chapterCount || 0} 章节 • {novel.filename}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* 未解析的文件 */}\n          {availableFiles.filter(file => !file.parsed).length > 0 && (\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">未解析的文件</h3>\n              <div className=\"space-y-1\">\n                {availableFiles\n                  .filter(file => !file.parsed)\n                  .map((file) => (\n                    <div\n                      key={file.filename}\n                      className=\"p-2 border border-gray-200 rounded bg-gray-50\"\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"min-w-0 flex-1\">\n                          <div className=\"font-medium text-gray-800 text-sm truncate\">{file.filename}</div>\n                          <div className=\"text-xs text-gray-500\">未解析</div>\n                        </div>\n                        <button\n                          onClick={() => handleParseNovel(file.filename)}\n                          disabled={parsing === file.filename || disabled}\n                          className=\"flex items-center px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 ml-2\"\n                        >\n                          {parsing === file.filename ? (\n                            <>\n                              <RefreshCw className=\"animate-spin mr-1\" size={12} />\n                              解析中\n                            </>\n                          ) : (\n                            <>\n                              <Upload className=\"mr-1\" size={12} />\n                              解析\n                            </>\n                          )}\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n              </div>\n            </div>\n          )}\n\n          {availableFiles.length === 0 && (\n            <div className=\"text-center py-6 text-gray-500\">\n              <BookOpen className=\"mx-auto mb-2\" size={32} />\n              <p className=\"text-sm\">novels 文件夹中没有找到小说文件</p>\n              <p className=\"text-xs\">请将 .txt 或 .md 文件放入 novels 文件夹</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {selectedNovel && (\n        <div className=\"mt-3 p-2 bg-blue-50 border border-blue-200 rounded\">\n          <div className=\"text-sm text-blue-800\">\n            <strong>已选择:</strong> {selectedNovel.title}\n          </div>\n          <div className=\"text-xs text-blue-600\">\n            {selectedNovel.chapterCount || 0} 章节\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;AAAA;;;AAJA;;;AAkBe,SAAS,cAAc,KAA8D;QAA9D,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAsB,GAA9D;;IACpC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAU,EAAE;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAc,EAAE;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAgB;IAEtD,IAAA,0KAAS;mCAAC;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,IAAI,CAAC,MAAM;gBAC5B,kBAAkB,OAAO,IAAI,CAAC,cAAc;YAC9C,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,eAAO;YAAkB,2EAAU;QAC1D,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAU;gBAAQ;YAC3C;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,cAAc,SAAS;gBAC7B,MAAM,OAAO,OAAO;YACtB,OAAO;gBACL,MAAM,AAAC,SAAqB,OAAb,OAAO,KAAK;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU;QACd,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,6NAAQ;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGzC,6LAAC;wBACC,SAAS;wBACT,UAAU,WAAW;wBACrB,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,gOAAS;4BAAC,WAAW,AAAC,GAAgC,OAA9B,UAAU,iBAAiB;4BAAM,MAAM;;;;;;;;;;;;;;;;;YAInE,wBACC,6LAAC;gBAAI,WAAU;0BAAiC;;;;;qCAIhD,6LAAC;gBAAI,WAAU;;oBAEZ,OAAO,MAAM,GAAG,mBACf,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;wCAEC,SAAS,IAAM,kBAAkB;wCACjC,WAAW,AAAC,uDAIR,OAHF,CAAA,0BAAA,oCAAA,cAAe,EAAE,MAAK,MAAM,EAAE,GAC1B,+BACA,yCACL,KAAmD,OAAhD,WAAW,kCAAkC;;0DAEjD,6LAAC;gDAAI,WAAU;0DAAqC,MAAM,KAAK;;;;;;0DAC/D,6LAAC;gDAAI,WAAU;;oDACZ,MAAM,YAAY,IAAI;oDAAE;oDAAO,MAAM,QAAQ;;;;;;;;uCAV3C,MAAM,EAAE;;;;;;;;;;;;;;;;oBAmBtB,eAAe,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,MAAM,EAAE,MAAM,GAAG,mBACpD,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,eACE,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,MAAM,EAC3B,GAAG,CAAC,CAAC,qBACJ,6LAAC;wCAEC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA8C,KAAK,QAAQ;;;;;;sEAC1E,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDACC,SAAS,IAAM,iBAAiB,KAAK,QAAQ;oDAC7C,UAAU,YAAY,KAAK,QAAQ,IAAI;oDACvC,WAAU;8DAET,YAAY,KAAK,QAAQ,iBACxB;;0EACE,6LAAC,gOAAS;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;qFAIvD;;0EACE,6LAAC,mNAAM;gEAAC,WAAU;gEAAO,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;uCApBxC,KAAK,QAAQ;;;;;;;;;;;;;;;;oBAgC7B,eAAe,MAAM,KAAK,mBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6NAAQ;gCAAC,WAAU;gCAAe,MAAM;;;;;;0CACzC,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;;;;;;;YAM9B,+BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAO;;;;;;4BAAa;4BAAE,cAAc,KAAK;;;;;;;kCAE5C,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,YAAY,IAAI;4BAAE;;;;;;;;;;;;;;;;;;;AAM7C;GA5KwB;KAAA", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/ChapterSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Novel, Chapter } from '@/lib/database';\nimport { FileText, Info } from 'lucide-react';\n\ninterface ChapterSelectorProps {\n  novel: Novel | null;\n  selectedChapters: string;\n  onChaptersChange: (chapters: string) => void;\n  disabled?: boolean;\n}\n\nexport default function ChapterSelector({ \n  novel, \n  selectedChapters, \n  onChaptersChange, \n  disabled \n}: ChapterSelectorProps) {\n  const [chapters, setChapters] = useState<Chapter[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [previewChapters, setPreviewChapters] = useState<number[]>([]);\n\n  useEffect(() => {\n    if (novel) {\n      loadChapters(novel.id);\n    } else {\n      setChapters([]);\n      setPreviewChapters([]);\n    }\n  }, [novel]);\n\n  useEffect(() => {\n    // 解析章节范围并预览\n    if (selectedChapters && chapters.length > 0) {\n      const parsed = parseChapterRange(selectedChapters, chapters.length);\n      setPreviewChapters(parsed);\n    } else {\n      setPreviewChapters([]);\n    }\n  }, [selectedChapters, chapters]);\n\n  const loadChapters = async (novelId: string) => {\n    setLoading(true);\n    try {\n      const response = await fetch(`/api/chapters?novelId=${novelId}`);\n      const result = await response.json();\n      \n      if (result.success) {\n        setChapters(result.data);\n      } else {\n        console.error('加载章节列表失败:', result.error);\n      }\n    } catch (error) {\n      console.error('加载章节列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const parseChapterRange = (rangeStr: string, maxChapter: number): number[] => {\n    const chapters: number[] = [];\n    const parts = rangeStr.split(',').map(part => part.trim());\n    \n    for (const part of parts) {\n      if (part.includes('-')) {\n        // 范围格式 (例如: \"1-5\")\n        const [start, end] = part.split('-').map(num => parseInt(num.trim()));\n        if (!isNaN(start) && !isNaN(end) && start <= end) {\n          for (let i = start; i <= Math.min(end, maxChapter); i++) {\n            if (i > 0 && !chapters.includes(i)) {\n              chapters.push(i);\n            }\n          }\n        }\n      } else {\n        // 单个章节\n        const chapterNum = parseInt(part);\n        if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {\n          chapters.push(chapterNum);\n        }\n      }\n    }\n    \n    return chapters.sort((a, b) => a - b);\n  };\n\n  const handleQuickSelect = (type: 'all' | 'first10' | 'last10') => {\n    if (disabled || chapters.length === 0) return;\n\n    let range = '';\n    switch (type) {\n      case 'all':\n        range = `1-${chapters.length}`;\n        break;\n      case 'first10':\n        range = `1-${Math.min(10, chapters.length)}`;\n        break;\n      case 'last10':\n        range = `${Math.max(1, chapters.length - 9)}-${chapters.length}`;\n        break;\n    }\n    onChaptersChange(range);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <h2 className=\"text-lg font-semibold text-gray-800 mb-3 flex items-center\">\n        <FileText className=\"mr-2\" size={18} />\n        选择章节 {novel && <span className=\"text-sm text-gray-500 ml-2\">共 {chapters.length} 章</span>}\n      </h2>\n\n      {!novel ? (\n        <div className=\"text-center py-6 text-gray-500\">\n          <FileText className=\"mx-auto mb-2\" size={32} />\n          <p className=\"text-sm\">请先选择一部小说</p>\n        </div>\n      ) : loading ? (\n        <div className=\"text-center py-6 text-gray-500 text-sm\">\n          加载中...\n        </div>\n      ) : (\n        <div className=\"space-y-3\">\n          {/* 章节范围输入 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              章节范围\n            </label>\n            <input\n              type=\"text\"\n              value={selectedChapters}\n              onChange={(e) => onChaptersChange(e.target.value)}\n              disabled={disabled}\n              placeholder=\"例如: 1-5,7,10-12\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n            />\n            <div className=\"mt-1 flex items-center text-xs text-gray-500\">\n              <Info className=\"mr-1\" size={12} />\n              支持范围(1-5)、单个章节(7)、组合(1-5,7,10-12)\n            </div>\n          </div>\n\n          {/* 快速选择按钮 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              快速选择\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => handleQuickSelect('all')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                全部章节 (1-{chapters.length})\n              </button>\n              <button\n                onClick={() => handleQuickSelect('first10')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                前10章\n              </button>\n              <button\n                onClick={() => handleQuickSelect('last10')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                后10章\n              </button>\n            </div>\n          </div>\n\n          {/* 章节预览 */}\n          {previewChapters.length > 0 && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                将要改写的章节 ({previewChapters.length} 章)\n              </label>\n              <div className=\"max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50\">\n                <div className=\"grid grid-cols-1 gap-1 text-sm\">\n                  {previewChapters.map((chapterNum) => {\n                    const chapter = chapters.find(ch => ch.chapterNumber === chapterNum);\n                    return (\n                      <div key={chapterNum} className=\"flex items-center\">\n                        <span className=\"font-medium text-blue-600 w-12\">\n                          第{chapterNum}章\n                        </span>\n                        <span className=\"text-gray-700 truncate\">\n                          {chapter?.title || '未知标题'}\n                        </span>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 章节列表 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              所有章节 ({chapters.length} 章)\n            </label>\n            <div className=\"max-h-60 overflow-y-auto border border-gray-200 rounded-md\">\n              {chapters.map((chapter) => (\n                <div\n                  key={chapter.id}\n                  className={`p-2 border-b border-gray-100 last:border-b-0 ${\n                    previewChapters.includes(chapter.chapterNumber)\n                      ? 'bg-blue-50 border-l-4 border-l-blue-500'\n                      : 'hover:bg-gray-50'\n                  }`}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"font-medium text-gray-800 truncate\">\n                        第{chapter.chapterNumber}章 {chapter.title}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        {chapter.content.length} 字符\n                      </div>\n                    </div>\n                    {previewChapters.includes(chapter.chapterNumber) && (\n                      <div className=\"ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\">\n                        已选择\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;;;AAJA;;;AAae,SAAS,gBAAgB,KAKjB;QALiB,EACtC,KAAK,EACL,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACa,GALiB;;IAMtC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,yKAAQ,EAAW,EAAE;IAEnE,IAAA,0KAAS;qCAAC;YACR,IAAI,OAAO;gBACT,aAAa,MAAM,EAAE;YACvB,OAAO;gBACL,YAAY,EAAE;gBACd,mBAAmB,EAAE;YACvB;QACF;oCAAG;QAAC;KAAM;IAEV,IAAA,0KAAS;qCAAC;YACR,YAAY;YACZ,IAAI,oBAAoB,SAAS,MAAM,GAAG,GAAG;gBAC3C,MAAM,SAAS,kBAAkB,kBAAkB,SAAS,MAAM;gBAClE,mBAAmB;YACrB,OAAO;gBACL,mBAAmB,EAAE;YACvB;QACF;oCAAG;QAAC;QAAkB;KAAS;IAE/B,MAAM,eAAe,OAAO;QAC1B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,yBAAgC,OAAR;YACtD,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,OAAO,IAAI;YACzB,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,UAAkB;QAC3C,MAAM,WAAqB,EAAE;QAC7B,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAEvD,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,QAAQ,CAAC,MAAM;gBACtB,mBAAmB;gBACnB,MAAM,CAAC,OAAO,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,SAAS,IAAI,IAAI;gBACjE,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,SAAS,KAAK;oBAChD,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,GAAG,CAAC,KAAK,aAAa,IAAK;wBACvD,IAAI,IAAI,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAI;4BAClC,SAAS,IAAI,CAAC;wBAChB;oBACF;gBACF;YACF,OAAO;gBACL,OAAO;gBACP,MAAM,aAAa,SAAS;gBAC5B,IAAI,CAAC,MAAM,eAAe,aAAa,KAAK,cAAc,cAAc,CAAC,SAAS,QAAQ,CAAC,aAAa;oBACtG,SAAS,IAAI,CAAC;gBAChB;YACF;QACF;QAEA,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACrC;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY,SAAS,MAAM,KAAK,GAAG;QAEvC,IAAI,QAAQ;QACZ,OAAQ;YACN,KAAK;gBACH,QAAQ,AAAC,KAAoB,OAAhB,SAAS,MAAM;gBAC5B;YACF,KAAK;gBACH,QAAQ,AAAC,KAAkC,OAA9B,KAAK,GAAG,CAAC,IAAI,SAAS,MAAM;gBACzC;YACF,KAAK;gBACH,QAAQ,AAAC,GAAsC,OAApC,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,IAAG,KAAmB,OAAhB,SAAS,MAAM;gBAC9D;QACJ;QACA,iBAAiB;IACnB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC,6NAAQ;wBAAC,WAAU;wBAAO,MAAM;;;;;;oBAAM;oBACjC,uBAAS,6LAAC;wBAAK,WAAU;;4BAA6B;4BAAG,SAAS,MAAM;4BAAC;;;;;;;;;;;;;YAGhF,CAAC,sBACA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6NAAQ;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCACzC,6LAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;uBAEvB,wBACF,6LAAC;gBAAI,WAAU;0BAAyC;;;;;qCAIxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,UAAU;gCACV,aAAY;gCACZ,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAI;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;;;;;;;kCAMvC,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;;4CACX;4CACU,SAAS,MAAM;4CAAC;;;;;;;kDAE3B,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;;oBAOJ,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAA+C;oCACpD,gBAAgB,MAAM;oCAAC;;;;;;;0CAEnC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC;wCACpB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,aAAa,KAAK;wCACzD,qBACE,6LAAC;4CAAqB,WAAU;;8DAC9B,6LAAC;oDAAK,WAAU;;wDAAiC;wDAC7C;wDAAW;;;;;;;8DAEf,6LAAC;oDAAK,WAAU;8DACb,CAAA,oBAAA,8BAAA,QAAS,KAAK,KAAI;;;;;;;2CALb;;;;;oCASd;;;;;;;;;;;;;;;;;kCAOR,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAA+C;oCACvD,SAAS,MAAM;oCAAC;;;;;;;0CAEzB,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAEC,WAAW,AAAC,gDAIX,OAHC,gBAAgB,QAAQ,CAAC,QAAQ,aAAa,IAC1C,4CACA;kDAGN,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEAAqC;gEAChD,QAAQ,aAAa;gEAAC;gEAAG,QAAQ,KAAK;;;;;;;sEAE1C,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,OAAO,CAAC,MAAM;gEAAC;;;;;;;;;;;;;gDAG3B,gBAAgB,QAAQ,CAAC,QAAQ,aAAa,mBAC7C,6LAAC;oDAAI,WAAU;8DAA2D;;;;;;;;;;;;uCAjBzE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BjC;GA/NwB;KAAA", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/database.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\n// 数据类型定义\nexport interface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n}\n\nexport interface Chapter {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  title: string;\n  content: string;\n  filename: string;\n  createdAt: string;\n}\n\nexport interface RewriteRule {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string; // 角色类型：男主、女主、配角、反派、其他\n  description: string;\n  personality?: string; // 性格特点\n  appearance?: string; // 外貌描述\n  relationships?: string; // 人物关系\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Preset {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RewriteJob {\n  id: string;\n  novelId: string;\n  chapters: number[];\n  ruleId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  // 新增详细信息字段\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    averageTimePerChapter: number;\n    apiKeyStats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n    }>;\n    chapterResults: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n      completedAt?: string;\n    }>;\n    model?: string;\n    concurrency?: number;\n  };\n}\n\n// 数据存储路径\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst NOVELS_FILE = path.join(DATA_DIR, 'novels.json');\nconst CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');\nconst RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');\nconst JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');\nconst CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');\nconst PRESETS_FILE = path.join(DATA_DIR, 'presets.json');\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  if (!fs.existsSync(DATA_DIR)) {\n    fs.mkdirSync(DATA_DIR, { recursive: true });\n  }\n}\n\n// 读取JSON文件\nfunction readJsonFile<T>(filePath: string): T[] {\n  ensureDataDir();\n  if (!fs.existsSync(filePath)) {\n    return [];\n  }\n  try {\n    const data = fs.readFileSync(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filePath}:`, error);\n    return [];\n  }\n}\n\n// 写入JSON文件\nfunction writeJsonFile<T>(filePath: string, data: T[]) {\n  ensureDataDir();\n  try {\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');\n  } catch (error) {\n    console.error(`Error writing ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n\n// 小说相关操作\nexport const novelDb = {\n  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),\n\n  getById: (id: string): Novel | undefined => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    return novels.find(novel => novel.id === id);\n  },\n\n  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const newNovel: Novel = {\n      ...novel,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    novels.push(newNovel);\n    writeJsonFile(NOVELS_FILE, novels);\n    return newNovel;\n  },\n\n  update: (id: string, updates: Partial<Novel>): Novel | null => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return null;\n\n    novels[index] = { ...novels[index], ...updates };\n    writeJsonFile(NOVELS_FILE, novels);\n    return novels[index];\n  },\n\n  delete: (id: string): boolean => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return false;\n\n    novels.splice(index, 1);\n    writeJsonFile(NOVELS_FILE, novels);\n    return true;\n  }\n};\n\n// 章节相关操作\nexport const chapterDb = {\n  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),\n\n  getByNovelId: (novelId: string): Chapter[] => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.filter(chapter => chapter.novelId === novelId);\n  },\n\n  getById: (id: string): Chapter | undefined => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.find(chapter => chapter.id === id);\n  },\n\n  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapter: Chapter = {\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    chapters.push(newChapter);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return newChapter;\n  },\n\n  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {\n    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapters = chapters.map(chapter => ({\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    }));\n    existingChapters.push(...newChapters);\n    writeJsonFile(CHAPTERS_FILE, existingChapters);\n    return newChapters;\n  },\n\n  delete: (id: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const index = chapters.findIndex(chapter => chapter.id === id);\n    if (index === -1) return false;\n\n    chapters.splice(index, 1);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);\n    writeJsonFile(CHAPTERS_FILE, filteredChapters);\n    return true;\n  }\n};\n\n// 改写规则相关操作\nexport const ruleDb = {\n  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),\n\n  getById: (id: string): RewriteRule | undefined => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    return rules.find(rule => rule.id === id);\n  },\n\n  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const newRule: RewriteRule = {\n      ...rule,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    rules.push(newRule);\n    writeJsonFile(RULES_FILE, rules);\n    return newRule;\n  },\n\n  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return null;\n\n    rules[index] = {\n      ...rules[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(RULES_FILE, rules);\n    return rules[index];\n  },\n\n  delete: (id: string): boolean => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return false;\n\n    rules.splice(index, 1);\n    writeJsonFile(RULES_FILE, rules);\n    return true;\n  }\n};\n\n// 改写任务相关操作\nexport const jobDb = {\n  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),\n\n  getById: (id: string): RewriteJob | undefined => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    return jobs.find(job => job.id === id);\n  },\n\n  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const newJob: RewriteJob = {\n      ...job,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    jobs.push(newJob);\n    writeJsonFile(JOBS_FILE, jobs);\n    return newJob;\n  },\n\n  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return null;\n\n    jobs[index] = {\n      ...jobs[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(JOBS_FILE, jobs);\n    return jobs[index];\n  },\n\n  delete: (id: string): boolean => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return false;\n\n    jobs.splice(index, 1);\n    writeJsonFile(JOBS_FILE, jobs);\n    return true;\n  }\n};\n\n// 人物设定相关操作\nexport const characterDb = {\n  getAll: (): Character[] => readJsonFile<Character>(CHARACTERS_FILE),\n\n  getByNovelId: (novelId: string): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId);\n  },\n\n  getById: (id: string): Character | undefined => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.find(character => character.id === id);\n  },\n\n  create: (character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Character => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const newCharacter: Character = {\n      ...character,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    characters.push(newCharacter);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return newCharacter;\n  },\n\n  update: (id: string, updates: Partial<Character>): Character | null => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return null;\n\n    characters[index] = {\n      ...characters[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return characters[index];\n  },\n\n  delete: (id: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return false;\n\n    characters.splice(index, 1);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const filteredCharacters = characters.filter(character => character.novelId !== novelId);\n    writeJsonFile(CHARACTERS_FILE, filteredCharacters);\n    return true;\n  }\n};\n\n// 预设相关操作\nexport const presetDb = {\n  getAll: (): Preset[] => readJsonFile<Preset>(PRESETS_FILE),\n\n  getById: (id: string): Preset | undefined => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    return presets.find(preset => preset.id === id);\n  },\n\n  create: (preset: Omit<Preset, 'id' | 'createdAt' | 'updatedAt'>): Preset => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const newPreset: Preset = {\n      ...preset,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    presets.push(newPreset);\n    writeJsonFile(PRESETS_FILE, presets);\n    return newPreset;\n  },\n\n  update: (id: string, updates: Partial<Preset>): Preset | null => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return null;\n\n    presets[index] = {\n      ...presets[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(PRESETS_FILE, presets);\n    return presets[index];\n  },\n\n  delete: (id: string): boolean => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return false;\n\n    presets.splice(index, 1);\n    writeJsonFile(PRESETS_FILE, presets);\n    return true;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AA6F2B;;;;;;AA5F3B;;;AA2FA,SAAS;AACT,MAAM,WAAW,qLAAI,CAAC,IAAI,CAAC,2KAAO,CAAC,GAAG,IAAI;AAC1C,MAAM,cAAc,qLAAI,CAAC,IAAI,CAAC,UAAU;;AACxC,MAAM,gBAAgB,qLAAI,CAAC,IAAI,CAAC,UAAU;;AAC1C,MAAM,aAAa,qLAAI,CAAC,IAAI,CAAC,UAAU;;AACvC,MAAM,YAAY,qLAAI,CAAC,IAAI,CAAC,UAAU;;AACtC,MAAM,kBAAkB,qLAAI,CAAC,IAAI,CAAC,UAAU;;AAC5C,MAAM,eAAe,qLAAI,CAAC,IAAI,CAAC,UAAU;;AAEzC,WAAW;AACX,SAAS;IACP,IAAI,CAAC,GAAG,UAAU,CAAC,WAAW;QAC5B,GAAG,SAAS,CAAC,UAAU;YAAE,WAAW;QAAK;IAC3C;AACF;AAEA,WAAW;AACX,SAAS,aAAgB,QAAgB;IACvC;IACA,IAAI,CAAC,GAAG,UAAU,CAAC,WAAW;QAC5B,OAAO,EAAE;IACX;IACA,IAAI;QACF,MAAM,OAAO,GAAG,YAAY,CAAC,UAAU;QACvC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,AAAC,iBAAyB,OAAT,UAAS,MAAI;QAC5C,OAAO,EAAE;IACX;AACF;AAEA,WAAW;AACX,SAAS,cAAiB,QAAgB,EAAE,IAAS;IACnD;IACA,IAAI;QACF,GAAG,aAAa,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,AAAC,iBAAyB,OAAT,UAAS,MAAI;QAC5C,MAAM;IACR;AACF;AAEA,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAGO,MAAM,UAAU;IACrB,QAAQ,IAAe,aAAoB;IAE3C,SAAS,CAAC;QACR,MAAM,SAAS,aAAoB;QACnC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC3C;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,WAAkB;YACtB,GAAG,KAAK;YACR,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,OAAO,IAAI,CAAC;QACZ,cAAc,aAAa;QAC3B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,CAAC,MAAM,GAAG;YAAE,GAAG,MAAM,CAAC,MAAM;YAAE,GAAG,OAAO;QAAC;QAC/C,cAAc,aAAa;QAC3B,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,MAAM,CAAC,OAAO;QACrB,cAAc,aAAa;QAC3B,OAAO;IACT;AACF;AAGO,MAAM,YAAY;IACvB,QAAQ,IAAiB,aAAsB;IAE/C,cAAc,CAAC;QACb,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACxD;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,aAAsB;YAC1B,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,aAAa,CAAC;QACZ,MAAM,mBAAmB,aAAsB;QAC/C,MAAM,cAAc,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC3C,GAAG,OAAO;gBACV,IAAI;gBACJ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;QACD,iBAAiB,IAAI,IAAI;QACzB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,WAAW,aAAsB;QACvC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QACxE,cAAc,eAAe;QAC7B,OAAO;IACT;AACF;AAGO,MAAM,SAAS;IACpB,QAAQ,IAAqB,aAA0B;IAEvD,SAAS,CAAC;QACR,MAAM,QAAQ,aAA0B;QACxC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxC;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,UAAuB;YAC3B,GAAG,IAAI;YACP,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,MAAM,IAAI,CAAC;QACX,cAAc,YAAY;QAC1B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,CAAC,MAAM,GAAG;YACb,GAAG,KAAK,CAAC,MAAM;YACf,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,YAAY;QAC1B,OAAO,KAAK,CAAC,MAAM;IACrB;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,MAAM,CAAC,OAAO;QACpB,cAAc,YAAY;QAC1B,OAAO;IACT;AACF;AAGO,MAAM,QAAQ;IACnB,QAAQ,IAAoB,aAAyB;IAErD,SAAS,CAAC;QACR,MAAM,OAAO,aAAyB;QACtC,OAAO,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACrC;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,SAAqB;YACzB,GAAG,GAAG;YACN,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,KAAK,IAAI,CAAC;QACV,cAAc,WAAW;QACzB,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,WAAW;QACzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,MAAM,CAAC,OAAO;QACnB,cAAc,WAAW;QACzB,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ,IAAmB,aAAwB;IAEnD,cAAc,CAAC;QACb,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;IAC9D;IAEA,SAAS,CAAC;QACR,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;IACvD;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,eAA0B;YAC9B,GAAG,SAAS;YACZ,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,WAAW,IAAI,CAAC;QAChB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,UAAU,CAAC,MAAM,GAAG;YAClB,GAAG,UAAU,CAAC,MAAM;YACpB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,iBAAiB;QAC/B,OAAO,UAAU,CAAC,MAAM;IAC1B;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,WAAW,MAAM,CAAC,OAAO;QACzB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,aAAa,aAAwB;QAC3C,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;QAChF,cAAc,iBAAiB;QAC/B,OAAO;IACT;AACF;AAGO,MAAM,WAAW;IACtB,QAAQ,IAAgB,aAAqB;IAE7C,SAAS,CAAC;QACR,MAAM,UAAU,aAAqB;QACrC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;IAC9C;IAEA,QAAQ,CAAC;QACP,MAAM,UAAU,aAAqB;QACrC,MAAM,YAAoB;YACxB,GAAG,MAAM;YACT,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,QAAQ,IAAI,CAAC;QACb,cAAc,cAAc;QAC5B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,UAAU,aAAqB;QACrC,MAAM,QAAQ,QAAQ,SAAS,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QACxD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,CAAC,MAAM,GAAG;YACf,GAAG,OAAO,CAAC,MAAM;YACjB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,cAAc;QAC5B,OAAO,OAAO,CAAC,MAAM;IACvB;IAEA,QAAQ,CAAC;QACP,MAAM,UAAU,aAAqB;QACrC,MAAM,QAAQ,QAAQ,SAAS,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QACxD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,QAAQ,MAAM,CAAC,OAAO;QACtB,cAAc,cAAc;QAC5B,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/gemini.ts"], "sourcesContent": ["// Gemini API 集成 - 多Key池管理\n// API Keys配置 - 第一个key是其他4个的4倍强度\nconst API_KEYS = [\n  {\n    key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw', // My First Project - 4倍强度\n    name: 'My First Project',\n    weight: 4, // 权重，表示相对强度\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0, // 冷却时间\n  },\n  {\n    key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y', // ankibot\n    name: 'ankibot',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY', // Generative Language Client\n    name: 'Generative Language Client',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc', // In The Novel\n    name: 'In The Novel',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk', // chat\n    name: 'chat',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  }\n];\n\n// API配置\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent';\nconst REQUEST_DELAY = 1000; // 请求间隔（毫秒）\nconst COOLDOWN_DURATION = 60000; // 429错误后的冷却时间（毫秒）\nconst MAX_RETRIES = 3; // 最大重试次数\n\n// API Key管理类\nclass ApiKeyManager {\n  private keys = [...API_KEYS];\n\n  // 获取最佳可用的API Key\n  getBestAvailableKey() {\n    const now = Date.now();\n\n    // 过滤掉冷却中的key\n    const availableKeys = this.keys.filter(key => key.cooldownUntil <= now);\n\n    if (availableKeys.length === 0) {\n      // 如果所有key都在冷却中，返回冷却时间最短的\n      return this.keys.reduce((min, key) =>\n        key.cooldownUntil < min.cooldownUntil ? key : min\n      );\n    }\n\n    // 根据权重和使用频率选择最佳key\n    const bestKey = availableKeys.reduce((best, key) => {\n      const keyScore = key.weight / (key.requestCount + 1);\n      const bestScore = best.weight / (best.requestCount + 1);\n      return keyScore > bestScore ? key : best;\n    });\n\n    return bestKey;\n  }\n\n  // 记录API使用\n  recordUsage(keyName: string, success: boolean) {\n    const key = this.keys.find(k => k.name === keyName);\n    if (key) {\n      key.requestCount++;\n      key.lastUsed = Date.now();\n\n      if (!success) {\n        // 如果失败，设置冷却时间\n        key.cooldownUntil = Date.now() + COOLDOWN_DURATION;\n      }\n    }\n  }\n\n  // 获取统计信息\n  getStats() {\n    return this.keys.map(key => ({\n      name: key.name,\n      requestCount: key.requestCount,\n      weight: key.weight,\n      isAvailable: key.cooldownUntil <= Date.now(),\n      cooldownRemaining: Math.max(0, key.cooldownUntil - Date.now()),\n    }));\n  }\n}\n\nconst keyManager = new ApiKeyManager();\n\nexport interface RewriteRequest {\n  originalText: string;\n  rules: string;\n  chapterTitle?: string;\n  chapterNumber?: number;\n}\n\nexport interface RewriteResponse {\n  rewrittenText: string;\n  success: boolean;\n  error?: string;\n  apiKeyUsed?: string;\n  tokensUsed?: number;\n  model?: string;\n  processingTime?: number;\n}\n\n// 构建改写提示词\nfunction buildPrompt(request: RewriteRequest): string {\n  const { originalText, rules, chapterTitle, chapterNumber } = request;\n\n  return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：\n\n改写规则：\n${rules}\n\n${chapterTitle ? `${chapterTitle}` : ''}\n// ${chapterNumber ? `章节编号：第${chapterNumber}章` : ''}\n\n原文内容：\n${originalText}\n\n请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：\n1. 遵循所有指定的改写规则\n2. 保持原文的基本故事框架（除非规则要求修改）\n3. 确保文字流畅自然\n4. 保持角色的基本性格特征（除非规则要求修改）\n\n请直接输出改写后的内容，不要添加任何解释或说明：`;\n}\n\n// 调用Gemini API进行文本改写 - 支持多Key和重试\nexport async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {\n  const startTime = Date.now();\n  let lastError = '';\n\n  for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {\n    try {\n      const apiKey = keyManager.getBestAvailableKey();\n\n      // 如果key在冷却中，等待一段时间\n      if (apiKey.cooldownUntil > Date.now()) {\n        const waitTime = Math.min(apiKey.cooldownUntil - Date.now(), 5000); // 最多等待5秒\n        await new Promise(resolve => setTimeout(resolve, waitTime));\n      }\n\n      const prompt = buildPrompt(request);\n\n      const response = await fetch(`${GEMINI_API_URL}?key=${apiKey.key}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          contents: [{\n            parts: [{\n              text: prompt\n            }]\n          }],\n          generationConfig: {\n            temperature: 0.7,\n            topK: 40,\n            topP: 0.95,\n            maxOutputTokens: 8192,\n          },\n          safetySettings: [\n            {\n              category: \"HARM_CATEGORY_HARASSMENT\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            },\n            {\n              category: \"HARM_CATEGORY_HATE_SPEECH\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            },\n            {\n              category: \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            },\n            {\n              category: \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            }\n          ]\n        }),\n      });\n\n      const processingTime = Date.now() - startTime;\n\n      if (response.status === 429) {\n        // 429错误，记录失败并尝试下一个key\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API限流 (${apiKey.name})`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY * (attempt + 1)));\n          continue;\n        }\n      }\n\n      if (!response.ok) {\n        const errorData = await response.text();\n        console.error('Gemini API error:', errorData);\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API请求失败: ${response.status} ${response.statusText}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      const data = await response.json();\n\n      // 记录成功使用\n      keyManager.recordUsage(apiKey.name, true);\n\n      if (!data.candidates || data.candidates.length === 0) {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '没有收到有效的响应内容',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      const candidate = data.candidates[0];\n\n      if (candidate.finishReason === 'SAFETY') {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '内容被安全过滤器拦截，请调整改写规则或原文内容',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '响应内容格式错误',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      const rewrittenText = candidate.content.parts[0].text;\n\n      // 尝试从响应中提取token使用信息\n      const tokensUsed = data.usageMetadata?.totalTokenCount || 0;\n\n      return {\n        rewrittenText: rewrittenText.trim(),\n        success: true,\n        apiKeyUsed: apiKey.name,\n        tokensUsed,\n        model: 'gemini-2.5-flash-lite',\n        processingTime,\n      };\n\n    } catch (error) {\n      console.error('Gemini API调用错误:', error);\n      lastError = `网络错误: ${error instanceof Error ? error.message : '未知错误'}`;\n\n      if (attempt < MAX_RETRIES - 1) {\n        await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY * (attempt + 1)));\n      }\n    }\n  }\n\n  return {\n    rewrittenText: '',\n    success: false,\n    error: `重试${MAX_RETRIES}次后仍然失败: ${lastError}`,\n    processingTime: Date.now() - startTime,\n  };\n}\n\n// 改进的批量改写函数 - 支持实时写入和详细进度跟踪\nexport async function rewriteChapters(\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number, details?: any) => void,\n  onChapterComplete?: (chapterIndex: number, result: any) => void,\n  concurrency: number = 3 // 降低并发数以避免429错误\n): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {\n  const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);\n  let completed = 0;\n  let totalTokensUsed = 0;\n  const startTime = Date.now();\n\n  // 使用更保守的并发策略\n  const semaphore = new Semaphore(concurrency);\n\n  const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {\n    await semaphore.acquire();\n    const chapterStartTime = Date.now();\n\n    try {\n\n      const result = await rewriteText({\n        originalText: chapter.content,\n        rules,\n        chapterTitle: chapter.title,\n        chapterNumber: chapter.number,\n      });\n\n      const chapterProcessingTime = Date.now() - chapterStartTime;\n\n      if (result.tokensUsed) {\n        totalTokensUsed += result.tokensUsed;\n      }\n\n      const chapterResult = {\n        success: result.success,\n        content: result.rewrittenText,\n        error: result.error,\n        details: {\n          apiKeyUsed: result.apiKeyUsed,\n          tokensUsed: result.tokensUsed,\n          model: result.model,\n          processingTime: chapterProcessingTime,\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n        }\n      };\n\n      results[index] = chapterResult;\n      completed++;\n\n      // 实时回调章节完成\n      if (onChapterComplete) {\n        onChapterComplete(index, chapterResult);\n      }\n\n      // 更新进度，包含详细信息\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            processingTime: chapterProcessingTime,\n            apiKey: result.apiKeyUsed,\n            tokens: result.tokensUsed,\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      // 添加请求间隔\n      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));\n\n      return result;\n    } catch (error) {\n      const chapterErrorTime = Date.now();\n      const errorResult = {\n        success: false,\n        content: '',\n        error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        details: {\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n          processingTime: chapterErrorTime - chapterStartTime,\n        }\n      };\n\n      results[index] = errorResult;\n      completed++;\n\n      if (onChapterComplete) {\n        onChapterComplete(index, errorResult);\n      }\n\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            error: error instanceof Error ? error.message : '未知错误',\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      return null;\n    } finally {\n      semaphore.release();\n    }\n  };\n\n  // 并发处理所有章节\n  const promises = chapters.map((chapter, index) => processChapter(chapter, index));\n  await Promise.all(promises);\n\n  return results;\n}\n\n// 信号量类，用于控制并发\nclass Semaphore {\n  private permits: number;\n  private waitQueue: Array<() => void> = [];\n\n  constructor(permits: number) {\n    this.permits = permits;\n  }\n\n  async acquire(): Promise<void> {\n    if (this.permits > 0) {\n      this.permits--;\n      return Promise.resolve();\n    }\n\n    return new Promise<void>((resolve) => {\n      this.waitQueue.push(resolve);\n    });\n  }\n\n  release(): void {\n    this.permits++;\n    if (this.waitQueue.length > 0) {\n      const resolve = this.waitQueue.shift();\n      if (resolve) {\n        this.permits--;\n        resolve();\n      }\n    }\n  }\n}\n\n// 测试API连接 - 增强版\nexport async function testGeminiConnection(): Promise<{\n  success: boolean;\n  error?: string;\n  details?: any;\n}> {\n  try {\n    const testResult = await rewriteText({\n      originalText: '这是一个测试文本。',\n      rules: '保持原文不变',\n    });\n\n    return {\n      success: testResult.success,\n      error: testResult.error,\n      details: {\n        apiKeyUsed: testResult.apiKeyUsed,\n        tokensUsed: testResult.tokensUsed,\n        model: testResult.model,\n        processingTime: testResult.processingTime,\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,\n      details: {\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  }\n}\n\n// 获取API Key使用统计\nexport function getApiKeyStats() {\n  return keyManager.getStats();\n}\n\n// 重置API Key统计\nexport function resetApiKeyStats() {\n  API_KEYS.forEach(key => {\n    key.requestCount = 0;\n    key.lastUsed = 0;\n    key.cooldownUntil = 0;\n  });\n}\n\n// 预设的改写规则模板\nexport let PRESET_RULES: Record<string, { name: string; description: string; rules: string }> = {\n  romance_focus: {\n    name: '感情戏增强',\n    description: '扩写男女主互动内容，对非感情戏部分一笔带过',\n    rules: `请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展`\n  },\n\n  character_fix: {\n    name: '人设修正',\n    description: '修正主角人设和对话风格',\n    rules: `请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式`\n  },\n\n  toxic_content_removal: {\n    name: '毒点清除',\n    description: '移除送女、绿帽等毒点情节',\n    rules: `请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容`\n  },\n\n  pacing_improvement: {\n    name: '节奏优化',\n    description: '优化故事节奏，删除拖沓内容',\n    rules: `请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性`\n  },\n\n  custom: {\n    name: '自定义规则',\n    description: '用户自定义的改写规则',\n    rules: ''\n  }\n};\n\n// 从数据库加载自定义预设并合并到 PRESET_RULES\nexport function loadCustomPresets() {\n  try {\n    const { presetDb } = require('@/lib/database');\n    const customPresets = presetDb.getAll();\n\n    // 将数据库中的预设添加到 PRESET_RULES\n    customPresets.forEach((preset: any) => {\n      PRESET_RULES[`custom_${preset.id}`] = {\n        name: preset.name,\n        description: preset.description,\n        rules: preset.rules\n      };\n    });\n  } catch (error) {\n    console.error('加载自定义预设失败:', error);\n  }\n}\n\n// 添加自定义预设规则（保持向后兼容）\nexport function addCustomPreset(name: string, description: string, rules: string): string {\n  const key = `custom_${Date.now()}`;\n  PRESET_RULES = {\n    ...PRESET_RULES,\n    [key]: {\n      name,\n      description,\n      rules\n    }\n  };\n  return key;\n}\n\n// 初始化时加载自定义预设\nloadCustomPresets();\n"], "names": [], "mappings": "AAAA,0BAA0B;AAC1B,gCAAgC;;;;;;;;;;;;;;;;;;;;;AAChC,MAAM,WAAW;IACf;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;CACD;AAED,QAAQ;AACR,MAAM,iBAAiB;AACvB,MAAM,gBAAgB,MAAM,WAAW;AACvC,MAAM,oBAAoB,OAAO,kBAAkB;AACnD,MAAM,cAAc,GAAG,SAAS;AAEhC,aAAa;AACb,MAAM;IAGJ,iBAAiB;IACjB,sBAAsB;QACpB,MAAM,MAAM,KAAK,GAAG;QAEpB,aAAa;QACb,MAAM,gBAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,aAAa,IAAI;QAEnE,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,yBAAyB;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,MAC5B,IAAI,aAAa,GAAG,IAAI,aAAa,GAAG,MAAM;QAElD;QAEA,mBAAmB;QACnB,MAAM,UAAU,cAAc,MAAM,CAAC,CAAC,MAAM;YAC1C,MAAM,WAAW,IAAI,MAAM,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC;YACnD,MAAM,YAAY,KAAK,MAAM,GAAG,CAAC,KAAK,YAAY,GAAG,CAAC;YACtD,OAAO,WAAW,YAAY,MAAM;QACtC;QAEA,OAAO;IACT;IAEA,UAAU;IACV,YAAY,OAAe,EAAE,OAAgB,EAAE;QAC7C,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAC3C,IAAI,KAAK;YACP,IAAI,YAAY;YAChB,IAAI,QAAQ,GAAG,KAAK,GAAG;YAEvB,IAAI,CAAC,SAAS;gBACZ,cAAc;gBACd,IAAI,aAAa,GAAG,KAAK,GAAG,KAAK;YACnC;QACF;IACF;IAEA,SAAS;IACT,WAAW;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3B,MAAM,IAAI,IAAI;gBACd,cAAc,IAAI,YAAY;gBAC9B,QAAQ,IAAI,MAAM;gBAClB,aAAa,IAAI,aAAa,IAAI,KAAK,GAAG;gBAC1C,mBAAmB,KAAK,GAAG,CAAC,GAAG,IAAI,aAAa,GAAG,KAAK,GAAG;YAC7D,CAAC;IACH;;QAjDA,+KAAQ,QAAO;eAAI;SAAS;;AAkD9B;AAEA,MAAM,aAAa,IAAI;AAmBvB,UAAU;AACV,SAAS,YAAY,OAAuB;IAC1C,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;IAE7D,OAAO,AAAC,6CAKR,OAFA,OAAM,QAGH,OADH,eAAe,AAAC,GAAe,OAAb,gBAAiB,IAAG,SAItC,OAHG,gBAAgB,AAAC,SAAsB,OAAd,eAAc,OAAK,IAAG,eAGrC,OAAb,cAAa;AASf;AAGO,eAAe,YAAY,OAAuB;IACvD,MAAM,YAAY,KAAK,GAAG;IAC1B,IAAI,YAAY;IAEhB,IAAK,IAAI,UAAU,GAAG,UAAU,aAAa,UAAW;QACtD,IAAI;gBA0HiB;YAzHnB,MAAM,SAAS,WAAW,mBAAmB;YAE7C,mBAAmB;YACnB,IAAI,OAAO,aAAa,GAAG,KAAK,GAAG,IAAI;gBACrC,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,aAAa,GAAG,KAAK,GAAG,IAAI,OAAO,SAAS;gBAC7E,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,MAAM,SAAS,YAAY;YAE3B,MAAM,WAAW,MAAM,MAAM,AAAC,GAAwB,OAAtB,gBAAe,SAAkB,OAAX,OAAO,GAAG,GAAI;gBAClE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;wBAAC;4BACT,OAAO;gCAAC;oCACN,MAAM;gCACR;6BAAE;wBACJ;qBAAE;oBACF,kBAAkB;wBAChB,aAAa;wBACb,MAAM;wBACN,MAAM;wBACN,iBAAiB;oBACnB;oBACA,gBAAgB;wBACd;4BACE,UAAU;4BACV,WAAW;wBACb;wBACA;4BACE,UAAU;4BACV,WAAW;wBACb;wBACA;4BACE,UAAU;4BACV,WAAW;wBACb;wBACA;4BACE,UAAU;4BACV,WAAW;wBACb;qBACD;gBACH;YACF;YAEA,MAAM,iBAAiB,KAAK,GAAG,KAAK;YAEpC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,sBAAsB;gBACtB,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;gBACpC,YAAY,AAAC,UAAqB,OAAZ,OAAO,IAAI,EAAC;gBAElC,IAAI,UAAU,cAAc,GAAG;oBAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,gBAAgB,CAAC,UAAU,CAAC;oBAC7E;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;gBACpC,YAAY,AAAC,YAA8B,OAAnB,SAAS,MAAM,EAAC,KAAuB,OAApB,SAAS,UAAU;gBAE9D,IAAI,UAAU,cAAc,GAAG;oBAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,SAAS;YACT,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;YAEpC,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;gBACpD,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,MAAM,YAAY,KAAK,UAAU,CAAC,EAAE;YAEpC,IAAI,UAAU,YAAY,KAAK,UAAU;gBACvC,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK,IAAI,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;gBAC1F,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,MAAM,gBAAgB,UAAU,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;YAErD,oBAAoB;YACpB,MAAM,aAAa,EAAA,sBAAA,KAAK,aAAa,cAAlB,0CAAA,oBAAoB,eAAe,KAAI;YAE1D,OAAO;gBACL,eAAe,cAAc,IAAI;gBACjC,SAAS;gBACT,YAAY,OAAO,IAAI;gBACvB;gBACA,OAAO;gBACP;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,YAAY,AAAC,SAAwD,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAE9D,IAAI,UAAU,cAAc,GAAG;gBAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,gBAAgB,CAAC,UAAU,CAAC;YAC/E;QACF;IACF;IAEA,OAAO;QACL,eAAe;QACf,SAAS;QACT,OAAO,AAAC,KAA0B,OAAtB,aAAY,YAAoB,OAAV;QAClC,gBAAgB,KAAK,GAAG,KAAK;IAC/B;AACF;AAGO,eAAe,gBACpB,QAAmE,EACnE,KAAa,EACb,UAA8E,EAC9E,iBAA+D;QAC/D,cAAA,gDAAwB,gBAAgB;sBAAlB;IAEtB,MAAM,UAAuF,IAAI,MAAM,SAAS,MAAM;IACtH,IAAI,YAAY;IAChB,IAAI,kBAAkB;IACtB,MAAM,YAAY,KAAK,GAAG;IAE1B,aAAa;IACb,MAAM,YAAY,IAAI,UAAU;IAEhC,MAAM,iBAAiB,OAAO,SAA6D;QACzF,MAAM,UAAU,OAAO;QACvB,MAAM,mBAAmB,KAAK,GAAG;QAEjC,IAAI;YAEF,MAAM,SAAS,MAAM,YAAY;gBAC/B,cAAc,QAAQ,OAAO;gBAC7B;gBACA,cAAc,QAAQ,KAAK;gBAC3B,eAAe,QAAQ,MAAM;YAC/B;YAEA,MAAM,wBAAwB,KAAK,GAAG,KAAK;YAE3C,IAAI,OAAO,UAAU,EAAE;gBACrB,mBAAmB,OAAO,UAAU;YACtC;YAEA,MAAM,gBAAgB;gBACpB,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,aAAa;gBAC7B,OAAO,OAAO,KAAK;gBACnB,SAAS;oBACP,YAAY,OAAO,UAAU;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,OAAO,OAAO,KAAK;oBACnB,gBAAgB;oBAChB,eAAe,QAAQ,MAAM;oBAC7B,cAAc,QAAQ,KAAK;gBAC7B;YACF;YAEA,OAAO,CAAC,MAAM,GAAG;YACjB;YAEA,WAAW;YACX,IAAI,mBAAmB;gBACrB,kBAAkB,OAAO;YAC3B;YAEA,cAAc;YACd,IAAI,YAAY;gBACd,MAAM,kBAAkB;oBACtB;oBACA,OAAO,SAAS,MAAM;oBACtB;oBACA,WAAW,KAAK,GAAG,KAAK;oBACxB,uBAAuB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;oBAClD,aAAa,WAAW,QAAQ;oBAChC,gBAAgB;wBACd,QAAQ,QAAQ,MAAM;wBACtB,OAAO,QAAQ,KAAK;wBACpB,gBAAgB;wBAChB,QAAQ,OAAO,UAAU;wBACzB,QAAQ,OAAO,UAAU;oBAC3B;gBACF;gBAEA,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM,EAAE;YAClE;YAEA,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,mBAAmB,KAAK,GAAG;YACjC,MAAM,cAAc;gBAClB,SAAS;gBACT,SAAS;gBACT,OAAO,AAAC,SAAwD,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,SAAS;oBACP,eAAe,QAAQ,MAAM;oBAC7B,cAAc,QAAQ,KAAK;oBAC3B,gBAAgB,mBAAmB;gBACrC;YACF;YAEA,OAAO,CAAC,MAAM,GAAG;YACjB;YAEA,IAAI,mBAAmB;gBACrB,kBAAkB,OAAO;YAC3B;YAEA,IAAI,YAAY;gBACd,MAAM,kBAAkB;oBACtB;oBACA,OAAO,SAAS,MAAM;oBACtB;oBACA,WAAW,KAAK,GAAG,KAAK;oBACxB,uBAAuB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;oBAClD,aAAa,WAAW,QAAQ;oBAChC,gBAAgB;wBACd,QAAQ,QAAQ,MAAM;wBACtB,OAAO,QAAQ,KAAK;wBACpB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAClD;gBACF;gBAEA,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM,EAAE;YAClE;YAEA,OAAO;QACT,SAAU;YACR,UAAU,OAAO;QACnB;IACF;IAEA,WAAW;IACX,MAAM,WAAW,SAAS,GAAG,CAAC,CAAC,SAAS,QAAU,eAAe,SAAS;IAC1E,MAAM,QAAQ,GAAG,CAAC;IAElB,OAAO;AACT;AAEA,cAAc;AACd,MAAM;IAQJ,MAAM,UAAyB;QAC7B,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG;YACpB,IAAI,CAAC,OAAO;YACZ,OAAO,QAAQ,OAAO;QACxB;QAEA,OAAO,IAAI,QAAc,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACtB;IACF;IAEA,UAAgB;QACd,IAAI,CAAC,OAAO;QACZ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG;YAC7B,MAAM,UAAU,IAAI,CAAC,SAAS,CAAC,KAAK;YACpC,IAAI,SAAS;gBACX,IAAI,CAAC,OAAO;gBACZ;YACF;QACF;IACF;IAxBA,YAAY,OAAe,CAAE;QAH7B,+KAAQ,WAAR,KAAA;QACA,+KAAQ,aAA+B,EAAE;QAGvC,IAAI,CAAC,OAAO,GAAG;IACjB;AAuBF;AAGO,eAAe;IAKpB,IAAI;QACF,MAAM,aAAa,MAAM,YAAY;YACnC,cAAc;YACd,OAAO;QACT;QAEA,OAAO;YACL,SAAS,WAAW,OAAO;YAC3B,OAAO,WAAW,KAAK;YACvB,SAAS;gBACP,YAAY,WAAW,UAAU;gBACjC,YAAY,WAAW,UAAU;gBACjC,OAAO,WAAW,KAAK;gBACvB,gBAAgB,WAAW,cAAc;gBACzC,aAAa,WAAW,QAAQ;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,AAAC,WAA0D,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3D,SAAS;gBACP,aAAa,WAAW,QAAQ;YAClC;QACF;IACF;AACF;AAGO,SAAS;IACd,OAAO,WAAW,QAAQ;AAC5B;AAGO,SAAS;IACd,SAAS,OAAO,CAAC,CAAA;QACf,IAAI,YAAY,GAAG;QACnB,IAAI,QAAQ,GAAG;QACf,IAAI,aAAa,GAAG;IACtB;AACF;AAGO,IAAI,eAAqF;IAC9F,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,uBAAuB;QACrB,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,OAAQ;IAKV;IAEA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;AACF;AAGO,SAAS;IACd,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE;QAClB,MAAM,gBAAgB,SAAS,MAAM;QAErC,2BAA2B;QAC3B,cAAc,OAAO,CAAC,CAAC;YACrB,YAAY,CAAC,AAAC,UAAmB,OAAV,OAAO,EAAE,EAAG,GAAG;gBACpC,MAAM,OAAO,IAAI;gBACjB,aAAa,OAAO,WAAW;gBAC/B,OAAO,OAAO,KAAK;YACrB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;IAC9B;AACF;AAGO,SAAS,gBAAgB,IAAY,EAAE,WAAmB,EAAE,KAAa;IAC9E,MAAM,MAAM,AAAC,UAAoB,OAAX,KAAK,GAAG;IAC9B,eAAe;QACb,GAAG,YAAY;QACf,CAAC,IAAI,EAAE;YACL;YACA;YACA;QACF;IACF;IACA,OAAO;AACT;AAEA,cAAc;AACd", "debugId": null}}, {"offset": {"line": 1662, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/RuleEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Settings, Wand2, Save } from 'lucide-react';\nimport { PRESET_RULES } from '@/lib/gemini';\n\ninterface RuleEditorProps {\n  rules: string;\n  onRulesChange: (rules: string) => void;\n  disabled?: boolean;\n  onSaveToPreset?: (rules: string) => void;\n}\n\ninterface Preset {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n}\n\nexport default function RuleEditor({ rules, onRulesChange, disabled, onSaveToPreset }: RuleEditorProps) {\n  const [showPresets, setShowPresets] = useState(false);\n  const [customPresets, setCustomPresets] = useState<Preset[]>([]);\n  const [allPresets, setAllPresets] = useState<Record<string, { name: string; description: string; rules: string }>>({});\n\n  // 加载自定义预设\n  useEffect(() => {\n    loadCustomPresets();\n  }, []);\n\n  const loadCustomPresets = async () => {\n    try {\n      const response = await fetch('/api/presets');\n      const result = await response.json();\n      if (result.success) {\n        setCustomPresets(result.data);\n\n        // 合并内置预设和自定义预设\n        const combined = { ...PRESET_RULES };\n        result.data.forEach((preset: Preset) => {\n          combined[`custom_${preset.id}`] = {\n            name: preset.name,\n            description: preset.description,\n            rules: preset.rules\n          };\n        });\n        setAllPresets(combined);\n      }\n    } catch (error) {\n      console.error('加载自定义预设失败:', error);\n      setAllPresets(PRESET_RULES);\n    }\n  };\n\n  const handlePresetSelect = (presetKey: string) => {\n    const preset = allPresets[presetKey];\n    if (preset) {\n      onRulesChange(preset.rules);\n      setShowPresets(false);\n    }\n  };\n\n  const handleSaveToPreset = async () => {\n    if (rules.trim() && onSaveToPreset) {\n      await onSaveToPreset(rules);\n      // 重新加载预设列表\n      await loadCustomPresets();\n    }\n  };\n\n  const presetButtons = Object.entries(allPresets).filter(([key]) => key !== 'custom');\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n          <Settings className=\"mr-2\" size={20} />\n          改写规则\n        </h2>\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={handleSaveToPreset}\n            disabled={disabled || !rules.trim()}\n            className=\"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n            title=\"保存为预设\"\n          >\n            <Save size={16} />\n          </button>\n          <button\n            onClick={() => setShowPresets(!showPresets)}\n            disabled={disabled}\n            className=\"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n            title=\"预设规则\"\n          >\n            <Wand2 size={16} />\n          </button>\n        </div>\n      </div>\n\n\n\n      {/* 预设规则 */}\n      {showPresets && (\n        <div className=\"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <h3 className=\"font-medium text-gray-800 mb-2 text-sm\">选择预设规则</h3>\n          <div className=\"grid grid-cols-1 gap-1\">\n            {presetButtons.map(([key, preset]) => (\n              <button\n                key={key}\n                onClick={() => handlePresetSelect(key)}\n                disabled={disabled}\n                className=\"text-left p-2 border border-gray-200 rounded hover:border-blue-300 hover:bg-blue-50 disabled:opacity-50 transition-colors\"\n              >\n                <div className=\"font-medium text-gray-800 text-sm\">{preset.name}</div>\n                <div className=\"text-xs text-gray-600\">{preset.description}</div>\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 规则编辑器 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          改写规则内容\n        </label>\n        <textarea\n          value={rules}\n          onChange={(e) => onRulesChange(e.target.value)}\n          disabled={disabled}\n          placeholder=\"请输入详细的改写规则，例如：&#10;&#10;1. 扩写男女主角之间的互动情节&#10;2. 对战斗场面一笔带过&#10;3. 增加情感描写和心理活动&#10;4. 修改不合理的人物行为&#10;...\"\n          className=\"w-full h-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none disabled:bg-gray-100\"\n        />\n        <div className=\"mt-2 text-xs text-gray-500\">\n          {rules.length} 字符 • 建议详细描述改写要求以获得更好的效果\n        </div>\n      </div>\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAoBe,SAAS,WAAW,KAAmE;QAAnE,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,EAAmB,GAAnE;;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAW,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAuE,CAAC;IAEpH,UAAU;IACV,IAAA,0KAAS;gCAAC;YACR;QACF;+BAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,iBAAiB,OAAO,IAAI;gBAE5B,eAAe;gBACf,MAAM,WAAW;oBAAE,GAAG,uIAAY;gBAAC;gBACnC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;oBACnB,QAAQ,CAAC,AAAC,UAAmB,OAAV,OAAO,EAAE,EAAG,GAAG;wBAChC,MAAM,OAAO,IAAI;wBACjB,aAAa,OAAO,WAAW;wBAC/B,OAAO,OAAO,KAAK;oBACrB;gBACF;gBACA,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,cAAc,uIAAY;QAC5B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAS,UAAU,CAAC,UAAU;QACpC,IAAI,QAAQ;YACV,cAAc,OAAO,KAAK;YAC1B,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,MAAM,IAAI,MAAM,gBAAgB;YAClC,MAAM,eAAe;YACrB,WAAW;YACX,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,OAAO,OAAO,CAAC,YAAY,MAAM,CAAC;YAAC,CAAC,IAAI;eAAK,QAAQ;;IAE3E,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,yNAAQ;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU,YAAY,CAAC,MAAM,IAAI;gCACjC,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,6MAAI;oCAAC,MAAM;;;;;;;;;;;0CAEd,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,UAAU;gCACV,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,2NAAK;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;YAQlB,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC;gCAAC,CAAC,KAAK,OAAO;iDAC/B,6LAAC;gCAEC,SAAS,IAAM,mBAAmB;gCAClC,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDAAqC,OAAO,IAAI;;;;;;kDAC/D,6LAAC;wCAAI,WAAU;kDAAyB,OAAO,WAAW;;;;;;;+BANrD;;;;;;;;;;;;;;;;;0BAcf,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,UAAU;wBACV,aAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;AAOxB;GAzHwB;KAAA", "debugId": null}}, {"offset": {"line": 1915, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/CharacterManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Users, Plus, X } from 'lucide-react';\n\ninterface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string;\n  description: string;\n  personality?: string;\n  appearance?: string;\n  relationships?: string;\n}\n\ninterface CharacterManagerProps {\n  novelId?: string;\n  characters: Character[];\n  onCharactersChange: (characters: Character[]) => void;\n  disabled?: boolean;\n}\n\nexport default function CharacterManager({ novelId, characters, onCharactersChange, disabled }: CharacterManagerProps) {\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [newCharacter, setNewCharacter] = useState({\n    name: '',\n    role: '其他',\n    description: ''\n  });\n\n  // 当小说ID变化时，加载对应的人物设定\n  useEffect(() => {\n    if (novelId) {\n      loadCharacters();\n    } else {\n      onCharactersChange([]);\n    }\n  }, [novelId]);\n\n  const loadCharacters = async () => {\n    if (!novelId) return;\n\n    try {\n      const response = await fetch(`/api/characters?novelId=${novelId}`);\n      const result = await response.json();\n      if (result.success) {\n        onCharactersChange(result.data);\n      }\n    } catch (error) {\n      console.error('加载人物设定失败:', error);\n    }\n  };\n\n  const handleAddCharacter = async () => {\n    if (!newCharacter.name.trim() || !novelId) return;\n\n    setLoading(true);\n    try {\n      const response = await fetch('/api/characters', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          novelId,\n          name: newCharacter.name,\n          role: newCharacter.role,\n          description: newCharacter.description,\n        }),\n      });\n\n      const result = await response.json();\n      if (result.success) {\n        await loadCharacters(); // 重新加载列表\n        setNewCharacter({ name: '', role: '其他', description: '' });\n        setShowAddForm(false);\n      } else {\n        alert(`添加失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('添加人物失败:', error);\n      alert('添加人物失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRemoveCharacter = async (id: string) => {\n    if (!confirm('确定要删除这个人物设定吗？')) return;\n\n    try {\n      const response = await fetch(`/api/characters?id=${id}`, {\n        method: 'DELETE',\n      });\n\n      const result = await response.json();\n      if (result.success) {\n        await loadCharacters(); // 重新加载列表\n      } else {\n        alert(`删除失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('删除人物失败:', error);\n      alert('删除人物失败');\n    }\n  };\n\n  const characterTypes = ['男主', '女主', '配角', '反派', '其他'];\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h2 className=\"text-lg font-semibold text-gray-800 flex items-center\">\n          <Users className=\"mr-2\" size={18} />\n          人物设定\n        </h2>\n        <button\n          onClick={() => setShowAddForm(!showAddForm)}\n          disabled={disabled}\n          className=\"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n          title=\"添加人物\"\n        >\n          <Plus size={16} />\n        </button>\n      </div>\n\n      {/* 添加人物表单 */}\n      {showAddForm && (\n        <div className=\"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <div className=\"space-y-2\">\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                placeholder=\"人物名称\"\n                value={newCharacter.name}\n                onChange={(e) => setNewCharacter({ ...newCharacter, name: e.target.value })}\n                className=\"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n              />\n              <select\n                value={newCharacter.role}\n                onChange={(e) => setNewCharacter({ ...newCharacter, role: e.target.value })}\n                className=\"px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n              >\n                {characterTypes.map(type => (\n                  <option key={type} value={type}>{type}</option>\n                ))}\n              </select>\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"备注描述\"\n              value={newCharacter.description}\n              onChange={(e) => setNewCharacter({ ...newCharacter, description: e.target.value })}\n              className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n            />\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={handleAddCharacter}\n                className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                添加\n              </button>\n              <button\n                onClick={() => setShowAddForm(false)}\n                className=\"px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600\"\n              >\n                取消\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 人物列表 */}\n      <div className=\"space-y-2\">\n        {characters.length === 0 ? (\n          <div className=\"text-center py-4 text-gray-500 text-sm\">\n            暂无人物设定\n          </div>\n        ) : (\n          characters.map((character) => (\n            <div key={character.id} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"font-medium text-gray-800 text-sm\">{character.name}</span>\n                  <span className={`px-2 py-0.5 text-xs rounded ${\n                    character.role === '男主' ? 'bg-blue-100 text-blue-800' :\n                    character.role === '女主' ? 'bg-pink-100 text-pink-800' :\n                    character.role === '配角' ? 'bg-green-100 text-green-800' :\n                    character.role === '反派' ? 'bg-red-100 text-red-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {character.role}\n                  </span>\n                </div>\n                {character.description && (\n                  <div className=\"text-xs text-gray-600 mt-1 truncate\">\n                    {character.description}\n                  </div>\n                )}\n              </div>\n              <button\n                onClick={() => handleRemoveCharacter(character.id)}\n                disabled={disabled}\n                className=\"p-1 text-gray-400 hover:text-red-600 disabled:opacity-50\"\n                title=\"删除\"\n              >\n                <X size={14} />\n              </button>\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAuBe,SAAS,iBAAiB,KAA4E;QAA5E,EAAE,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE,QAAQ,EAAyB,GAA5E;;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;QAC/C,MAAM;QACN,MAAM;QACN,aAAa;IACf;IAEA,qBAAqB;IACrB,IAAA,0KAAS;sCAAC;YACR,IAAI,SAAS;gBACX;YACF,OAAO;gBACL,mBAAmB,EAAE;YACvB;QACF;qCAAG;QAAC;KAAQ;IAEZ,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,2BAAkC,OAAR;YACxD,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,mBAAmB,OAAO,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS;QAE3C,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,MAAM,aAAa,IAAI;oBACvB,MAAM,aAAa,IAAI;oBACvB,aAAa,aAAa,WAAW;gBACvC;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,kBAAkB,SAAS;gBACjC,gBAAgB;oBAAE,MAAM;oBAAI,MAAM;oBAAM,aAAa;gBAAG;gBACxD,eAAe;YACjB,OAAO;gBACL,MAAM,AAAC,SAAqB,OAAb,OAAO,KAAK;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI,CAAC,QAAQ,kBAAkB;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,sBAAwB,OAAH,KAAM;gBACvD,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,kBAAkB,SAAS;YACnC,OAAO;gBACL,MAAM,AAAC,SAAqB,OAAb,OAAO,KAAK;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IAErD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,gNAAK;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGtC,6LAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,UAAU;wBACV,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,6MAAI;4BAAC,MAAM;;;;;;;;;;;;;;;;;YAKf,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,aAAa,IAAI;oCACxB,UAAU,CAAC,IAAM,gBAAgB;4CAAE,GAAG,YAAY;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACzE,WAAU;;;;;;8CAEZ,6LAAC;oCACC,OAAO,aAAa,IAAI;oCACxB,UAAU,CAAC,IAAM,gBAAgB;4CAAE,GAAG,YAAY;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACzE,WAAU;8CAET,eAAe,GAAG,CAAC,CAAA,qBAClB,6LAAC;4CAAkB,OAAO;sDAAO;2CAApB;;;;;;;;;;;;;;;;sCAInB,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO,aAAa,WAAW;4BAC/B,UAAU,CAAC,IAAM,gBAAgB;oCAAE,GAAG,YAAY;oCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAChF,WAAU;;;;;;sCAEZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;0BACZ,WAAW,MAAM,KAAK,kBACrB,6LAAC;oBAAI,WAAU;8BAAyC;;;;;2BAIxD,WAAW,GAAG,CAAC,CAAC,0BACd,6LAAC;wBAAuB,WAAU;;0CAChC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAqC,UAAU,IAAI;;;;;;0DACnE,6LAAC;gDAAK,WAAW,AAAC,+BAMjB,OALC,UAAU,IAAI,KAAK,OAAO,8BAC1B,UAAU,IAAI,KAAK,OAAO,8BAC1B,UAAU,IAAI,KAAK,OAAO,gCAC1B,UAAU,IAAI,KAAK,OAAO,4BAC1B;0DAEC,UAAU,IAAI;;;;;;;;;;;;oCAGlB,UAAU,WAAW,kBACpB,6LAAC;wCAAI,WAAU;kDACZ,UAAU,WAAW;;;;;;;;;;;;0CAI5B,6LAAC;gCACC,SAAS,IAAM,sBAAsB,UAAU,EAAE;gCACjD,UAAU;gCACV,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,oMAAC;oCAAC,MAAM;;;;;;;;;;;;uBA1BH,UAAU,EAAE;;;;;;;;;;;;;;;;AAkClC;GAlMwB;KAAA", "debugId": null}}, {"offset": {"line": 2269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/RewriteProgress.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';\n\ninterface RewriteProgressProps {\n  jobId: string;\n  onComplete: () => void;\n}\n\ninterface JobStatus {\n  id: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    averageTimePerChapter: number;\n    apiKeyStats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n      cooldownRemaining?: number;\n    }>;\n    chapterResults: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n      completedAt?: string;\n    }>;\n    model?: string;\n    concurrency?: number;\n  };\n}\n\nexport default function RewriteProgress({ jobId, onComplete }: RewriteProgressProps) {\n  const [job, setJob] = useState<JobStatus | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const interval = setInterval(checkJobStatus, 2000); // 每2秒检查一次状态\n    checkJobStatus(); // 立即检查一次\n\n    return () => clearInterval(interval);\n  }, [jobId]);\n\n  const checkJobStatus = async () => {\n    try {\n      const response = await fetch(`/api/jobs?jobId=${jobId}`);\n      const result = await response.json();\n      \n      if (result.success) {\n        setJob(result.data);\n        setLoading(false);\n        \n        // 如果任务完成或失败，停止轮询并通知父组件\n        if (result.data.status === 'completed' || result.data.status === 'failed') {\n          setTimeout(() => {\n            onComplete();\n          }, 2000); // 2秒后通知完成\n        }\n      } else {\n        console.error('获取任务状态失败:', result.error);\n      }\n    } catch (error) {\n      console.error('获取任务状态失败:', error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"text-yellow-500\" size={20} />;\n      case 'processing':\n        return <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500\"></div>;\n      case 'completed':\n        return <CheckCircle className=\"text-green-500\" size={20} />;\n      case 'failed':\n        return <XCircle className=\"text-red-500\" size={20} />;\n      default:\n        return <AlertCircle className=\"text-gray-500\" size={20} />;\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return '等待处理';\n      case 'processing':\n        return '正在改写';\n      case 'completed':\n        return '改写完成';\n      case 'failed':\n        return '改写失败';\n      default:\n        return '未知状态';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n      case 'processing':\n        return 'text-blue-600 bg-blue-50 border-blue-200';\n      case 'completed':\n        return 'text-green-600 bg-green-50 border-green-200';\n      case 'failed':\n        return 'text-red-600 bg-red-50 border-red-200';\n      default:\n        return 'text-gray-600 bg-gray-50 border-gray-200';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">获取任务状态中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!job) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4 text-red-600\">\n          <XCircle className=\"mx-auto mb-2\" size={32} />\n          <p>无法获取任务状态</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">改写进度</h3>\n      \n      {/* 状态显示 */}\n      <div className={`p-4 rounded-lg border ${getStatusColor(job.status)} mb-4`}>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            {getStatusIcon(job.status)}\n            <span className=\"ml-2 font-medium\">{getStatusText(job.status)}</span>\n          </div>\n          <span className=\"text-sm\">\n            {job.progress}%\n          </span>\n        </div>\n      </div>\n\n      {/* 进度条 */}\n      <div className=\"mb-4\">\n        <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n          <span>进度</span>\n          <span>{job.progress}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div\n            className={`h-2 rounded-full transition-all duration-300 ${\n              job.status === 'completed'\n                ? 'bg-green-500'\n                : job.status === 'failed'\n                ? 'bg-red-500'\n                : 'bg-blue-500'\n            }`}\n            style={{ width: `${job.progress}%` }}\n          ></div>\n        </div>\n      </div>\n\n      {/* 详细统计信息 */}\n      {job.details && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n          {/* 章节统计 */}\n          <div className=\"bg-gray-50 p-3 rounded-lg\">\n            <h4 className=\"font-medium text-gray-800 mb-2\">章节统计</h4>\n            <div className=\"text-sm text-gray-600 space-y-1\">\n              <div>总章节: {job.details.totalChapters}</div>\n              <div>已完成: {job.details.completedChapters}</div>\n              <div>失败: {job.details.failedChapters}</div>\n              <div>剩余: {job.details.totalChapters - job.details.completedChapters - job.details.failedChapters}</div>\n            </div>\n          </div>\n\n          {/* 性能统计 */}\n          <div className=\"bg-gray-50 p-3 rounded-lg\">\n            <h4 className=\"font-medium text-gray-800 mb-2\">性能统计</h4>\n            <div className=\"text-sm text-gray-600 space-y-1\">\n              <div>总耗时: {Math.round(job.details.totalProcessingTime / 1000)}秒</div>\n              <div>平均每章: {Math.round(job.details.averageTimePerChapter / 1000)}秒</div>\n              <div>Token消耗: {job.details.totalTokensUsed.toLocaleString()}</div>\n              <div>模型: {job.details.model}</div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* API Key状态 */}\n      {job.details?.apiKeyStats && job.details.apiKeyStats.length > 0 && (\n        <div className=\"mb-4\">\n          <h4 className=\"font-medium text-gray-800 mb-2\">API Key 使用状态</h4>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2\">\n            {job.details.apiKeyStats.map((keyStats, index) => (\n              <div key={index} className={`p-2 rounded border text-xs ${\n                keyStats.isAvailable ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'\n              }`}>\n                <div className=\"font-medium\">{keyStats.name}</div>\n                <div>权重: {keyStats.weight}x</div>\n                <div>使用次数: {keyStats.requestCount}</div>\n                <div className={keyStats.isAvailable ? 'text-green-600' : 'text-red-600'}>\n                  {keyStats.isAvailable ? '可用' : '冷却中'}\n                </div>\n                {keyStats.cooldownRemaining && keyStats.cooldownRemaining > 0 && (\n                  <div className=\"text-red-500\">\n                    冷却: {Math.round(keyStats.cooldownRemaining / 1000)}s\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 时间信息 */}\n      <div className=\"text-sm text-gray-500 space-y-1\">\n        <div>开始时间: {new Date(job.createdAt).toLocaleString()}</div>\n        <div>更新时间: {new Date(job.updatedAt).toLocaleString()}</div>\n        {job.details?.totalProcessingTime && job.status === 'completed' && (\n          <div>总耗时: {Math.round(job.details.totalProcessingTime / 1000)} 秒</div>\n        )}\n      </div>\n\n      {/* 结果信息 */}\n      {job.result && (\n        <div className=\"mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <h4 className=\"font-medium text-gray-800 mb-2\">结果信息</h4>\n          <p className=\"text-sm text-gray-700 whitespace-pre-wrap\">{job.result}</p>\n        </div>\n      )}\n\n      {/* 操作提示 */}\n      {job.status === 'completed' && (\n        <div className=\"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg\">\n          <div className=\"flex items-center text-green-700\">\n            <CheckCircle className=\"mr-2\" size={16} />\n            <span className=\"text-sm\">\n              改写完成！改写后的文件已保存到 data/rewritten 目录中。\n            </span>\n          </div>\n        </div>\n      )}\n\n      {job.status === 'failed' && (\n        <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n          <div className=\"flex items-center text-red-700\">\n            <XCircle className=\"mr-2\" size={16} />\n            <span className=\"text-sm\">\n              改写失败，请检查错误信息并重试。\n            </span>\n          </div>\n        </div>\n      )}\n\n      {job.status === 'processing' && (\n        <div className=\"mt-4 space-y-3\">\n          <div className=\"p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n            <div className=\"flex items-center text-blue-700\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2\"></div>\n              <span className=\"text-sm\">\n                正在使用 Gemini AI 改写章节，请耐心等待...\n              </span>\n            </div>\n            {job.details && (\n              <div className=\"mt-2 text-xs text-blue-600\">\n                并发数: {job.details.concurrency} | 模型: {job.details.model}\n              </div>\n            )}\n          </div>\n\n          {/* 最近完成的章节 */}\n          {job.details?.chapterResults && job.details.chapterResults.length > 0 && (\n            <div className=\"bg-gray-50 p-3 rounded-lg\">\n              <h4 className=\"font-medium text-gray-800 mb-2\">最近完成的章节</h4>\n              <div className=\"max-h-32 overflow-y-auto space-y-1\">\n                {job.details.chapterResults\n                  .filter(result => result && result.completedAt)\n                  .sort((a, b) => new Date(b.completedAt!).getTime() - new Date(a.completedAt!).getTime())\n                  .slice(0, 5)\n                  .map((result, index) => (\n                    <div key={index} className={`text-xs p-2 rounded ${\n                      result.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'\n                    }`}>\n                      <div className=\"flex justify-between items-center\">\n                        <span>第{result.chapterNumber}章: {result.chapterTitle}</span>\n                        <span>{result.success ? '✓' : '✗'}</span>\n                      </div>\n                      <div className=\"flex justify-between text-xs opacity-75\">\n                        <span>{result.apiKeyUsed}</span>\n                        <span>{result.processingTime ? Math.round(result.processingTime / 1000) + 's' : ''}</span>\n                        <span>{result.tokensUsed ? result.tokensUsed + ' tokens' : ''}</span>\n                      </div>\n                      {result.error && (\n                        <div className=\"text-red-600 text-xs mt-1\">{result.error}</div>\n                      )}\n                    </div>\n                  ))}\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AA8Ce,SAAS,gBAAgB,KAA2C;QAA3C,EAAE,KAAK,EAAE,UAAU,EAAwB,GAA3C;QAsKjC,cA6BE,eAqDE;;IAvPT,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,yKAAQ,EAAmB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,IAAA,0KAAS;qCAAC;YACR,MAAM,WAAW,YAAY,gBAAgB,OAAO,YAAY;YAChE,kBAAkB,SAAS;YAE3B;6CAAO,IAAM,cAAc;;QAC7B;oCAAG;QAAC;KAAM;IAEV,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,mBAAwB,OAAN;YAChD,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;gBAClB,WAAW;gBAEX,uBAAuB;gBACvB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU;oBACzE,WAAW;wBACT;oBACF,GAAG,OAAO,UAAU;gBACtB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,gNAAK;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAClD,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,0NAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD;gBACE,qBAAO,6LAAC,sOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,KAAK;QACR,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0NAAO;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCACxC,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BAGzD,6LAAC;gBAAI,WAAW,AAAC,yBAAmD,OAA3B,eAAe,IAAI,MAAM,GAAE;0BAClE,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,IAAI,MAAM;8CACzB,6LAAC;oCAAK,WAAU;8CAAoB,cAAc,IAAI,MAAM;;;;;;;;;;;;sCAE9D,6LAAC;4BAAK,WAAU;;gCACb,IAAI,QAAQ;gCAAC;;;;;;;;;;;;;;;;;;0BAMpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAM,IAAI,QAAQ;oCAAC;;;;;;;;;;;;;kCAEtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAW,AAAC,gDAMX,OALC,IAAI,MAAM,KAAK,cACX,iBACA,IAAI,MAAM,KAAK,WACf,eACA;4BAEN,OAAO;gCAAE,OAAO,AAAC,GAAe,OAAb,IAAI,QAAQ,EAAC;4BAAG;;;;;;;;;;;;;;;;;YAMxC,IAAI,OAAO,kBACV,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAI;4CAAM,IAAI,OAAO,CAAC,aAAa;;;;;;;kDACpC,6LAAC;;4CAAI;4CAAM,IAAI,OAAO,CAAC,iBAAiB;;;;;;;kDACxC,6LAAC;;4CAAI;4CAAK,IAAI,OAAO,CAAC,cAAc;;;;;;;kDACpC,6LAAC;;4CAAI;4CAAK,IAAI,OAAO,CAAC,aAAa,GAAG,IAAI,OAAO,CAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;kCAKpG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAI;4CAAM,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,mBAAmB,GAAG;4CAAM;;;;;;;kDAC9D,6LAAC;;4CAAI;4CAAO,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,qBAAqB,GAAG;4CAAM;;;;;;;kDACjE,6LAAC;;4CAAI;4CAAU,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc;;;;;;;kDACzD,6LAAC;;4CAAI;4CAAK,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;YAOlC,EAAA,eAAA,IAAI,OAAO,cAAX,mCAAA,aAAa,WAAW,KAAI,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,mBAC5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,6LAAC;wBAAI,WAAU;kCACZ,IAAI,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,sBACtC,6LAAC;gCAAgB,WAAW,AAAC,8BAE5B,OADC,SAAS,WAAW,GAAG,iCAAiC;;kDAExD,6LAAC;wCAAI,WAAU;kDAAe,SAAS,IAAI;;;;;;kDAC3C,6LAAC;;4CAAI;4CAAK,SAAS,MAAM;4CAAC;;;;;;;kDAC1B,6LAAC;;4CAAI;4CAAO,SAAS,YAAY;;;;;;;kDACjC,6LAAC;wCAAI,WAAW,SAAS,WAAW,GAAG,mBAAmB;kDACvD,SAAS,WAAW,GAAG,OAAO;;;;;;oCAEhC,SAAS,iBAAiB,IAAI,SAAS,iBAAiB,GAAG,mBAC1D,6LAAC;wCAAI,WAAU;;4CAAe;4CACvB,KAAK,KAAK,CAAC,SAAS,iBAAiB,GAAG;4CAAM;;;;;;;;+BAX/C;;;;;;;;;;;;;;;;0BAqBlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAI;4BAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;kCAClD,6LAAC;;4BAAI;4BAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;oBACjD,EAAA,gBAAA,IAAI,OAAO,cAAX,oCAAA,cAAa,mBAAmB,KAAI,IAAI,MAAM,KAAK,6BAClD,6LAAC;;4BAAI;4BAAM,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,mBAAmB,GAAG;4BAAM;;;;;;;;;;;;;YAKjE,IAAI,MAAM,kBACT,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,6LAAC;wBAAE,WAAU;kCAA6C,IAAI,MAAM;;;;;;;;;;;;YAKvE,IAAI,MAAM,KAAK,6BACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6OAAW;4BAAC,WAAU;4BAAO,MAAM;;;;;;sCACpC,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;YAO/B,IAAI,MAAM,KAAK,0BACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,0NAAO;4BAAC,WAAU;4BAAO,MAAM;;;;;;sCAChC,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;YAO/B,IAAI,MAAM,KAAK,8BACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;4BAI3B,IAAI,OAAO,kBACV,6LAAC;gCAAI,WAAU;;oCAA6B;oCACpC,IAAI,OAAO,CAAC,WAAW;oCAAC;oCAAQ,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;;;oBAM5D,EAAA,gBAAA,IAAI,OAAO,cAAX,oCAAA,cAAa,cAAc,KAAI,IAAI,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,mBAClE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAI,WAAU;0CACZ,IAAI,OAAO,CAAC,cAAc,CACxB,MAAM,CAAC,CAAA,SAAU,UAAU,OAAO,WAAW,EAC7C,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAG,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAG,OAAO,IACpF,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,QAAQ,sBACZ,6LAAC;wCAAgB,WAAW,AAAC,uBAE5B,OADC,OAAO,OAAO,GAAG,gCAAgC;;0DAEjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAK;4DAAE,OAAO,aAAa;4DAAC;4DAAI,OAAO,YAAY;;;;;;;kEACpD,6LAAC;kEAAM,OAAO,OAAO,GAAG,MAAM;;;;;;;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,OAAO,UAAU;;;;;;kEACxB,6LAAC;kEAAM,OAAO,cAAc,GAAG,KAAK,KAAK,CAAC,OAAO,cAAc,GAAG,QAAQ,MAAM;;;;;;kEAChF,6LAAC;kEAAM,OAAO,UAAU,GAAG,OAAO,UAAU,GAAG,YAAY;;;;;;;;;;;;4CAE5D,OAAO,KAAK,kBACX,6LAAC;gDAAI,WAAU;0DAA6B,OAAO,KAAK;;;;;;;uCAblD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwB9B;GAzRwB;KAAA", "debugId": null}}, {"offset": {"line": 3097, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/JobHistory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { CheckCircle, XCircle, Clock, AlertCircle, Eye, Trash2, RefreshCw } from 'lucide-react';\n\ninterface JobHistoryProps {\n  onJobSelect?: (jobId: string) => void;\n}\n\ninterface JobSummary {\n  id: string;\n  novelId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    model?: string;\n  };\n}\n\nexport default function JobHistory({ onJobSelect }: JobHistoryProps) {\n  const [jobs, setJobs] = useState<JobSummary[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [novels, setNovels] = useState<Record<string, string>>({});\n\n  useEffect(() => {\n    loadJobs();\n    loadNovels();\n  }, []);\n\n  const loadJobs = async () => {\n    try {\n      const response = await fetch('/api/jobs');\n      const result = await response.json();\n      \n      if (result.success) {\n        // 按创建时间倒序排列\n        const sortedJobs = result.data.sort((a: JobSummary, b: JobSummary) => \n          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n        );\n        setJobs(sortedJobs);\n      }\n    } catch (error) {\n      console.error('加载任务历史失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadNovels = async () => {\n    try {\n      const response = await fetch('/api/novels');\n      const result = await response.json();\n      \n      if (result.success) {\n        const novelMap: Record<string, string> = {};\n        // result.data 包含 novels 和 availableFiles，我们需要 novels 数组\n        const novels = result.data.novels || [];\n        if (Array.isArray(novels)) {\n          novels.forEach((novel: any) => {\n            novelMap[novel.id] = novel.title;\n          });\n        }\n        setNovels(novelMap);\n      }\n    } catch (error) {\n      console.error('加载小说列表失败:', error);\n    }\n  };\n\n  const deleteJob = async (jobId: string) => {\n    if (!confirm('确定要删除这个任务记录吗？')) return;\n    \n    try {\n      const response = await fetch(`/api/jobs?jobId=${jobId}`, {\n        method: 'DELETE',\n      });\n      \n      if (response.ok) {\n        setJobs(jobs.filter(job => job.id !== jobId));\n      }\n    } catch (error) {\n      console.error('删除任务失败:', error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"text-yellow-500\" size={16} />;\n      case 'processing':\n        return <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"></div>;\n      case 'completed':\n        return <CheckCircle className=\"text-green-500\" size={16} />;\n      case 'failed':\n        return <XCircle className=\"text-red-500\" size={16} />;\n      default:\n        return <AlertCircle className=\"text-gray-500\" size={16} />;\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'pending': return '等待中';\n      case 'processing': return '处理中';\n      case 'completed': return '已完成';\n      case 'failed': return '失败';\n      default: return '未知';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending': return 'bg-yellow-50 border-yellow-200';\n      case 'processing': return 'bg-blue-50 border-blue-200';\n      case 'completed': return 'bg-green-50 border-green-200';\n      case 'failed': return 'bg-red-50 border-red-200';\n      default: return 'bg-gray-50 border-gray-200';\n    }\n  };\n\n  const formatDuration = (ms: number) => {\n    const seconds = Math.round(ms / 1000);\n    if (seconds < 60) return `${seconds}秒`;\n    const minutes = Math.round(seconds / 60);\n    if (minutes < 60) return `${minutes}分钟`;\n    const hours = Math.round(minutes / 60);\n    return `${hours}小时`;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">加载任务历史中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex justify-between items-center\">\n          <h3 className=\"text-lg font-semibold text-gray-800\">任务历史</h3>\n          <button\n            onClick={loadJobs}\n            className=\"flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800\"\n          >\n            <RefreshCw size={14} className=\"mr-1\" />\n            刷新\n          </button>\n        </div>\n      </div>\n\n      <div className=\"max-h-96 overflow-y-auto\">\n        {jobs.length === 0 ? (\n          <div className=\"p-6 text-center text-gray-500\">\n            暂无任务记录\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {jobs.map((job) => (\n              <div key={job.id} className=\"p-4 hover:bg-gray-50\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center mb-2\">\n                      {getStatusIcon(job.status)}\n                      <span className=\"ml-2 font-medium text-gray-800\">\n                        {novels[job.novelId] || '未知小说'}\n                      </span>\n                      <span className={`ml-2 px-2 py-1 text-xs rounded border ${getStatusColor(job.status)}`}>\n                        {getStatusText(job.status)}\n                      </span>\n                    </div>\n                    \n                    <div className=\"text-sm text-gray-600 space-y-1\">\n                      <div>创建时间: {new Date(job.createdAt).toLocaleString()}</div>\n                      {job.details && (\n                        <div className=\"flex space-x-4\">\n                          <span>章节: {job.details.completedChapters}/{job.details.totalChapters}</span>\n                          {job.details.totalTokensUsed > 0 && (\n                            <span>Token: {job.details.totalTokensUsed.toLocaleString()}</span>\n                          )}\n                          {job.details.totalProcessingTime > 0 && (\n                            <span>耗时: {formatDuration(job.details.totalProcessingTime)}</span>\n                          )}\n                          {job.details.model && (\n                            <span>模型: {job.details.model}</span>\n                          )}\n                        </div>\n                      )}\n                      {job.status !== 'completed' && job.status !== 'failed' && (\n                        <div>进度: {job.progress}%</div>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2 ml-4\">\n                    {onJobSelect && (\n                      <button\n                        onClick={() => onJobSelect(job.id)}\n                        className=\"p-1 text-blue-600 hover:text-blue-800\"\n                        title=\"查看详情\"\n                      >\n                        <Eye size={16} />\n                      </button>\n                    )}\n                    <button\n                      onClick={() => deleteJob(job.id)}\n                      className=\"p-1 text-red-600 hover:text-red-800\"\n                      title=\"删除记录\"\n                    >\n                      <Trash2 size={16} />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA2Be,SAAS,WAAW,KAAgC;QAAhC,EAAE,WAAW,EAAmB,GAAhC;;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAe,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAyB,CAAC;IAE9D,IAAA,0KAAS;gCAAC;YACR;YACA;QACF;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY;gBACZ,MAAM,aAAa,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,GAAe,IAClD,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBAEjE,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,WAAmC,CAAC;gBAC1C,wDAAwD;gBACxD,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE;gBACvC,IAAI,MAAM,OAAO,CAAC,SAAS;oBACzB,OAAO,OAAO,CAAC,CAAC;wBACd,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,KAAK;oBAClC;gBACF;gBACA,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,YAAY,OAAO;QACvB,IAAI,CAAC,QAAQ,kBAAkB;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,mBAAwB,OAAN,QAAS;gBACvD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,gNAAK;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAClD,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,0NAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD;gBACE,qBAAO,6LAAC,sOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK;QAChC,IAAI,UAAU,IAAI,OAAO,AAAC,GAAU,OAAR,SAAQ;QACpC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,IAAI,UAAU,IAAI,OAAO,AAAC,GAAU,OAAR,SAAQ;QACpC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,OAAO,AAAC,GAAQ,OAAN,OAAM;IAClB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,gOAAS;oCAAC,MAAM;oCAAI,WAAU;;;;;;gCAAS;;;;;;;;;;;;;;;;;;0BAM9C,6LAAC;gBAAI,WAAU;0BACZ,KAAK,MAAM,KAAK,kBACf,6LAAC;oBAAI,WAAU;8BAAgC;;;;;yCAI/C,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;4BAAiB,WAAU;sCAC1B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,IAAI,MAAM;kEACzB,6LAAC;wDAAK,WAAU;kEACb,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI;;;;;;kEAE1B,6LAAC;wDAAK,WAAW,AAAC,yCAAmE,OAA3B,eAAe,IAAI,MAAM;kEAChF,cAAc,IAAI,MAAM;;;;;;;;;;;;0DAI7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAI;4DAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;oDACjD,IAAI,OAAO,kBACV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAK;oEAAK,IAAI,OAAO,CAAC,iBAAiB;oEAAC;oEAAE,IAAI,OAAO,CAAC,aAAa;;;;;;;4DACnE,IAAI,OAAO,CAAC,eAAe,GAAG,mBAC7B,6LAAC;;oEAAK;oEAAQ,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc;;;;;;;4DAEzD,IAAI,OAAO,CAAC,mBAAmB,GAAG,mBACjC,6LAAC;;oEAAK;oEAAK,eAAe,IAAI,OAAO,CAAC,mBAAmB;;;;;;;4DAE1D,IAAI,OAAO,CAAC,KAAK,kBAChB,6LAAC;;oEAAK;oEAAK,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;;;oDAIjC,IAAI,MAAM,KAAK,eAAe,IAAI,MAAM,KAAK,0BAC5C,6LAAC;;4DAAI;4DAAK,IAAI,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;kDAK7B,6LAAC;wCAAI,WAAU;;4CACZ,6BACC,6LAAC;gDACC,SAAS,IAAM,YAAY,IAAI,EAAE;gDACjC,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,0MAAG;oDAAC,MAAM;;;;;;;;;;;0DAGf,6LAAC;gDACC,SAAS,IAAM,UAAU,IAAI,EAAE;gDAC/B,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,uNAAM;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;2BAlDZ,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;AA6D9B;GA7MwB;KAAA", "debugId": null}}, {"offset": {"line": 3549, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/ApiKeyStats.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { RefreshCw, Key, Activity, Clock, CheckCircle, XCircle } from 'lucide-react';\n\ninterface ApiKeyStatsProps {\n  refreshInterval?: number; // 刷新间隔（毫秒）\n}\n\ninterface ApiKeyStats {\n  name: string;\n  requestCount: number;\n  weight: number;\n  isAvailable: boolean;\n  cooldownRemaining?: number;\n}\n\nexport default function ApiKeyStats({ refreshInterval = 5000 }: ApiKeyStatsProps) {\n  const [stats, setStats] = useState<ApiKeyStats[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);\n\n  useEffect(() => {\n    loadStats();\n    \n    if (refreshInterval > 0) {\n      const interval = setInterval(loadStats, refreshInterval);\n      return () => clearInterval(interval);\n    }\n  }, [refreshInterval]);\n\n  const loadStats = async () => {\n    try {\n      const response = await fetch('/api/gemini/stats');\n      const result = await response.json();\n      \n      if (result.success) {\n        setStats(result.data);\n        setLastUpdated(new Date());\n      }\n    } catch (error) {\n      console.error('加载API统计失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testConnection = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/gemini/test');\n      const result = await response.json();\n      \n      if (result.success) {\n        alert(`连接测试成功！\\n使用的API Key: ${result.details?.apiKeyUsed}\\nToken消耗: ${result.details?.tokensUsed}\\n处理时间: ${result.details?.processingTime}ms`);\n      } else {\n        alert(`连接测试失败: ${result.error}`);\n      }\n      \n      // 测试后刷新统计\n      await loadStats();\n    } catch (error) {\n      alert(`连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetStats = async () => {\n    if (!confirm('确定要重置所有API Key统计吗？')) return;\n    \n    try {\n      const response = await fetch('/api/gemini/reset', {\n        method: 'POST',\n      });\n      \n      if (response.ok) {\n        await loadStats();\n        alert('统计已重置');\n      }\n    } catch (error) {\n      alert(`重置失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  };\n\n  const formatCooldown = (ms: number) => {\n    const seconds = Math.ceil(ms / 1000);\n    if (seconds < 60) return `${seconds}秒`;\n    const minutes = Math.ceil(seconds / 60);\n    return `${minutes}分钟`;\n  };\n\n  if (loading && stats.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">加载API统计中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <Key className=\"mr-2 text-blue-600\" size={20} />\n            <h3 className=\"text-lg font-semibold text-gray-800\">API Key 状态</h3>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {lastUpdated && (\n              <span className=\"text-xs text-gray-500\">\n                更新于 {lastUpdated.toLocaleTimeString()}\n              </span>\n            )}\n            <button\n              onClick={loadStats}\n              disabled={loading}\n              className=\"flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50\"\n            >\n              <RefreshCw size={14} className={`mr-1 ${loading ? 'animate-spin' : ''}`} />\n              刷新\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {stats.length === 0 ? (\n          <div className=\"text-center text-gray-500 py-4\">\n            暂无API Key统计数据\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {/* 总体统计 */}\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n              <div className=\"bg-blue-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <Activity className=\"text-blue-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-blue-600\">总请求数</div>\n                    <div className=\"text-lg font-semibold text-blue-800\">\n                      {stats.reduce((sum, stat) => sum + stat.requestCount, 0)}\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-green-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"text-green-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-green-600\">可用Key</div>\n                    <div className=\"text-lg font-semibold text-green-800\">\n                      {stats.filter(stat => stat.isAvailable).length}\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-red-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <XCircle className=\"text-red-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-red-600\">冷却中</div>\n                    <div className=\"text-lg font-semibold text-red-800\">\n                      {stats.filter(stat => !stat.isAvailable).length}\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-purple-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <Key className=\"text-purple-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-purple-600\">总权重</div>\n                    <div className=\"text-lg font-semibold text-purple-800\">\n                      {stats.reduce((sum, stat) => sum + stat.weight, 0)}x\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* 详细Key状态 */}\n            <div className=\"space-y-3\">\n              {stats.map((stat, index) => (\n                <div key={index} className={`p-4 rounded-lg border ${\n                  stat.isAvailable \n                    ? 'bg-green-50 border-green-200' \n                    : 'bg-red-50 border-red-200'\n                }`}>\n                  <div className=\"flex justify-between items-center\">\n                    <div className=\"flex items-center\">\n                      <div className={`w-3 h-3 rounded-full mr-3 ${\n                        stat.isAvailable ? 'bg-green-500' : 'bg-red-500'\n                      }`}></div>\n                      <div>\n                        <div className=\"font-medium text-gray-800\">{stat.name}</div>\n                        <div className=\"text-sm text-gray-600\">\n                          权重: {stat.weight}x | 使用次数: {stat.requestCount}\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"text-right\">\n                      <div className={`text-sm font-medium ${\n                        stat.isAvailable ? 'text-green-600' : 'text-red-600'\n                      }`}>\n                        {stat.isAvailable ? '可用' : '冷却中'}\n                      </div>\n                      {!stat.isAvailable && stat.cooldownRemaining && stat.cooldownRemaining > 0 && (\n                        <div className=\"text-xs text-red-500 flex items-center\">\n                          <Clock size={12} className=\"mr-1\" />\n                          {formatCooldown(stat.cooldownRemaining)}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* 操作按钮 */}\n        <div className=\"flex justify-center space-x-4 mt-6 pt-4 border-t border-gray-200\">\n          <button\n            onClick={testConnection}\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? '测试中...' : '测试连接'}\n          </button>\n          \n          <button\n            onClick={resetStats}\n            className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700\"\n          >\n            重置统计\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAiBe,SAAS,YAAY,KAA4C;QAA5C,EAAE,kBAAkB,IAAI,EAAoB,GAA5C;;IAClC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAgB,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAc;IAE5D,IAAA,0KAAS;iCAAC;YACR;YAEA,IAAI,kBAAkB,GAAG;gBACvB,MAAM,WAAW,YAAY,WAAW;gBACxC;6CAAO,IAAM,cAAc;;YAC7B;QACF;gCAAG;QAAC;KAAgB;IAEpB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,OAAO,IAAI;gBACpB,eAAe,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;QAC9B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;oBACY,iBAAwC,kBAAqC;gBAA3G,MAAM,AAtDd,AAsDe,gCAAuB,kBAAA,OAAO,OAAO,cAAd,sCAAA,gBAAgB,UAAU,EAAC,uBAAa,mBAAA,OAAO,OAAO,cAAd,uCAAA,iBAAgB,UAAU,EAAC,YAAyC,QAA/B,mBAAA,OAAO,OAAO,cAAd,uCAAA,iBAAgB,cAAc,EAAC;YAC5I,OAAO;gBACL,MAAM,AAAC,WAAuB,OAAb,OAAO,KAAK;YAC/B;YAEA,UAAU;YACV,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM,AAAC,WAA0D,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC5D,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,uBAAuB;QAEpC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM,AAAC,SAAwD,OAAhD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC1D;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK;QAC/B,IAAI,UAAU,IAAI,OAAO,AAAC,GAAU,OAAR,SAAQ;QACpC,MAAM,UAAU,KAAK,IAAI,CAAC,UAAU;QACpC,OAAO,AAAC,GAAU,OAAR,SAAQ;IACpB;IAEA,IAAI,WAAW,MAAM,MAAM,KAAK,GAAG;QACjC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0MAAG;oCAAC,WAAU;oCAAqB,MAAM;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;;gCACZ,6BACC,6LAAC;oCAAK,WAAU;;wCAAwB;wCACjC,YAAY,kBAAkB;;;;;;;8CAGvC,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC,gOAAS;4CAAC,MAAM;4CAAI,WAAW,AAAC,QAAqC,OAA9B,UAAU,iBAAiB;;;;;;wCAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAOnF,6LAAC;gBAAI,WAAU;;oBACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;wBAAI,WAAU;kCAAiC;;;;;6CAIhD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yNAAQ;oDAAC,WAAU;oDAAqB,MAAM;;;;;;8DAC/C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,6LAAC;4DAAI,WAAU;sEACZ,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAM9D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6OAAW;oDAAC,WAAU;oDAAsB,MAAM;;;;;;8DACnD,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAyB;;;;;;sEACxC,6LAAC;4DAAI,WAAU;sEACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAMtD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,0NAAO;oDAAC,WAAU;oDAAoB,MAAM;;;;;;8DAC7C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAuB;;;;;;sEACtC,6LAAC;4DAAI,WAAU;sEACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAMvD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,0MAAG;oDAAC,WAAU;oDAAuB,MAAM;;;;;;8DAC5C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAA0B;;;;;;sEACzC,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7D,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wCAAgB,WAAW,AAAC,yBAI5B,OAHC,KAAK,WAAW,GACZ,iCACA;kDAEJ,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,6BAEhB,OADC,KAAK,WAAW,GAAG,iBAAiB;;;;;;sEAEtC,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAA6B,KAAK,IAAI;;;;;;8EACrD,6LAAC;oEAAI,WAAU;;wEAAwB;wEAChC,KAAK,MAAM;wEAAC;wEAAW,KAAK,YAAY;;;;;;;;;;;;;;;;;;;8DAKnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,uBAEhB,OADC,KAAK,WAAW,GAAG,mBAAmB;sEAErC,KAAK,WAAW,GAAG,OAAO;;;;;;wDAE5B,CAAC,KAAK,WAAW,IAAI,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,GAAG,mBACvE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gNAAK;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAC1B,eAAe,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;;uCA3BtC;;;;;;;;;;;;;;;;kCAuClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,UAAU,WAAW;;;;;;0CAGxB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAvOwB;KAAA", "debugId": null}}, {"offset": {"line": 4133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/TaskManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nimport RewriteProgress from './RewriteProgress';\nimport <PERSON>Hist<PERSON> from './JobHistory';\nimport ApiKeyStats from './ApiKeyStats';\nimport { Activity, History, Key, Eye } from 'lucide-react';\n\ninterface TaskManagerProps {\n  currentJobId?: string;\n  onJobComplete?: () => void;\n}\n\nexport default function TaskManager({ currentJobId, onJobComplete }: TaskManagerProps) {\n  const [selectedJobId, setSelectedJobId] = useState<string | null>(currentJobId || null);\n  const [activeTab, setActiveTab] = useState(currentJobId ? 'current' : 'history');\n\n  const handleJobSelect = (jobId: string) => {\n    setSelectedJobId(jobId);\n    setActiveTab('current');\n  };\n\n  const handleJobComplete = () => {\n    if (onJobComplete) {\n      onJobComplete();\n    }\n    // 任务完成后切换到历史页面\n    setTimeout(() => {\n      setActiveTab('history');\n    }, 2000);\n  };\n\n  const tabs = [\n    { id: 'current', label: '当前任务', icon: Activity },\n    { id: 'history', label: '任务历史', icon: History },\n    { id: 'stats', label: 'API状态', icon: Key },\n  ];\n\n  return (\n    <div className=\"w-full max-w-6xl mx-auto\">\n      {/* Tab Navigation */}\n      <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\">\n        {tabs.map((tab) => {\n          const Icon = tab.icon;\n          return (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors flex-1 justify-center ${\n                activeTab === tab.id\n                  ? 'bg-white text-blue-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-800'\n              }`}\n            >\n              <Icon className=\"mr-2\" size={16} />\n              {tab.label}\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"mt-6\">\n        {activeTab === 'current' && (\n          <div>\n            {selectedJobId ? (\n              <RewriteProgress\n                jobId={selectedJobId}\n                onComplete={handleJobComplete}\n              />\n            ) : (\n              <div className=\"bg-white rounded-lg shadow-md p-8 text-center\">\n                <Eye className=\"mx-auto mb-4 text-gray-400\" size={48} />\n                <h3 className=\"text-lg font-medium text-gray-800 mb-2\">没有正在进行的任务</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  当前没有正在执行的改写任务。你可以：\n                </p>\n                <div className=\"space-y-2 text-sm text-gray-500\">\n                  <p>• 从任务历史中选择一个任务查看详情</p>\n                  <p>• 创建新的改写任务</p>\n                  <p>• 查看API Key使用状态</p>\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'history' && (\n          <JobHistory onJobSelect={handleJobSelect} />\n        )}\n\n        {activeTab === 'stats' && (\n          <ApiKeyStats refreshInterval={5000} />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AAce,SAAS,YAAY,KAAiD;QAAjD,EAAE,YAAY,EAAE,aAAa,EAAoB,GAAjD;;IAClC,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAgB,gBAAgB;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC,eAAe,YAAY;IAEtE,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,IAAI,eAAe;YACjB;QACF;QACA,eAAe;QACf,WAAW;YACT,aAAa;QACf,GAAG;IACL;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,OAAO;YAAQ,MAAM,yNAAQ;QAAC;QAC/C;YAAE,IAAI;YAAW,OAAO;YAAQ,MAAM,sNAAO;QAAC;QAC9C;YAAE,IAAI;YAAS,OAAO;YAAS,MAAM,0MAAG;QAAC;KAC1C;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC;oBACT,MAAM,OAAO,IAAI,IAAI;oBACrB,qBACE,6LAAC;wBAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wBAClC,WAAW,AAAC,sGAIX,OAHC,cAAc,IAAI,EAAE,GAChB,qCACA;;0CAGN,6LAAC;gCAAK,WAAU;gCAAO,MAAM;;;;;;4BAC5B,IAAI,KAAK;;uBATL,IAAI,EAAE;;;;;gBAYjB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,2BACb,6LAAC;kCACE,8BACC,6LAAC,mJAAe;4BACd,OAAO;4BACP,YAAY;;;;;iDAGd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0MAAG;oCAAC,WAAU;oCAA6B,MAAM;;;;;;8CAClD,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;oBAOZ,cAAc,2BACb,6LAAC,8IAAU;wBAAC,aAAa;;;;;;oBAG1B,cAAc,yBACb,6LAAC,+IAAW;wBAAC,iBAAiB;;;;;;;;;;;;;;;;;;AAKxC;GApFwB;KAAA", "debugId": null}}, {"offset": {"line": 4338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/Toast.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { CheckCircle, XCircle, AlertCircle, X } from 'lucide-react';\n\ninterface ToastProps {\n  message: string;\n  type: 'success' | 'error' | 'info';\n  onClose: () => void;\n  duration?: number;\n}\n\nexport default function Toast({ message, type, onClose, duration = 3000 }: ToastProps) {\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(onClose, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [duration, onClose]);\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"text-green-500\" size={20} />;\n      case 'error':\n        return <XCircle className=\"text-red-500\" size={20} />;\n      case 'info':\n        return <AlertCircle className=\"text-blue-500\" size={20} />;\n    }\n  };\n\n  const getBgColor = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200';\n      case 'error':\n        return 'bg-red-50 border-red-200';\n      case 'info':\n        return 'bg-blue-50 border-blue-200';\n    }\n  };\n\n  return (\n    <div className={`fixed top-4 right-4 z-50 max-w-sm w-full ${getBgColor()} border rounded-lg shadow-lg p-4 animate-in slide-in-from-right-full duration-300`}>\n      <div className=\"flex items-start space-x-3\">\n        {getIcon()}\n        <div className=\"flex-1 text-sm text-gray-800\">\n          {message}\n        </div>\n        <button\n          onClick={onClose}\n          className=\"text-gray-400 hover:text-gray-600\"\n        >\n          <X size={16} />\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAYe,SAAS,MAAM,KAAuD;QAAvD,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,IAAI,EAAc,GAAvD;;IAC5B,IAAA,0KAAS;2BAAC;YACR,IAAI,WAAW,GAAG;gBAChB,MAAM,QAAQ,WAAW,SAAS;gBAClC;uCAAO,IAAM,aAAa;;YAC5B;QACF;0BAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,0NAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD,KAAK;gBACH,qBAAO,6LAAC,sOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,4CAAwD,OAAb,cAAa;kBACvE,cAAA,6LAAC;YAAI,WAAU;;gBACZ;8BACD,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,oMAAC;wBAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;AAKnB;GA9CwB;KAAA", "debugId": null}}, {"offset": {"line": 4462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport NovelSelector from '@/components/NovelSelector';\nimport ChapterSelector from '@/components/ChapterSelector';\nimport RuleEditor from '@/components/RuleEditor';\nimport CharacterManager from '@/components/CharacterManager';\nimport RewriteProgress from '@/components/RewriteProgress';\nimport TaskManager from '@/components/TaskManager';\nimport Toast from '@/components/Toast';\nimport { Novel, Chapter } from '@/lib/database';\nimport { HelpCircle } from 'lucide-react';\n\ninterface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string;\n  description: string;\n  personality?: string;\n  appearance?: string;\n  relationships?: string;\n}\n\ninterface ToastState {\n  show: boolean;\n  message: string;\n  type: 'success' | 'error' | 'info';\n}\n\nexport default function Home() {\n  const [selectedNovel, setSelectedNovel] = useState<Novel | null>(null);\n  const [selectedChapters, setSelectedChapters] = useState<string>('');\n  const [rewriteRules, setRewriteRules] = useState<string>('');\n  const [characters, setCharacters] = useState<Character[]>([]);\n  const [isRewriting, setIsRewriting] = useState(false);\n  const [currentJobId, setCurrentJobId] = useState<string | null>(null);\n  const [showTaskManager, setShowTaskManager] = useState(false);\n  const [toast, setToast] = useState<ToastState>({ show: false, message: '', type: 'info' });\n\n  const showToast = (message: string, type: 'success' | 'error' | 'info') => {\n    setToast({ show: true, message, type });\n  };\n\n  const hideToast = () => {\n    setToast({ show: false, message: '', type: 'info' });\n  };\n\n  const handleSaveToPreset = async (rules: string) => {\n    const name = prompt('请输入预设名称:');\n    if (!name) return;\n\n    const description = prompt('请输入预设描述 (可选):') || '';\n\n    try {\n      const response = await fetch('/api/presets', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name,\n          description,\n          rules,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        showToast('规则已保存到预设', 'success');\n      } else {\n        showToast(`保存失败: ${result.error}`, 'error');\n      }\n    } catch (error) {\n      console.error('保存预设失败:', error);\n      showToast('保存预设失败', 'error');\n    }\n  };\n\n  const handleStartRewrite = async () => {\n    if (!selectedNovel || !selectedChapters || !rewriteRules) {\n      showToast('请完整填写所有信息', 'error');\n      return;\n    }\n\n    setIsRewriting(true);\n\n    try {\n      // 构建包含人物信息的改写规则\n      let enhancedRules = rewriteRules;\n      if (characters.length > 0) {\n        const characterInfo = characters.map(char =>\n          `${char.name}(${char.role}${char.description ? ': ' + char.description : ''})`\n        ).join('、');\n        enhancedRules = `人物设定：${characterInfo}\\n\\n${rewriteRules}`;\n      }\n\n      const response = await fetch('/api/rewrite', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          novelId: selectedNovel.id,\n          chapterRange: selectedChapters,\n          rules: enhancedRules,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        setCurrentJobId(result.data.jobId);\n        showToast('改写任务已开始', 'info');\n      } else {\n        showToast(`改写失败: ${result.error}`, 'error');\n        setIsRewriting(false);\n      }\n    } catch (error) {\n      console.error('改写请求失败:', error);\n      showToast('改写请求失败', 'error');\n      setIsRewriting(false);\n    }\n  };\n\n  const handleRewriteComplete = () => {\n    setIsRewriting(false);\n    setCurrentJobId(null);\n    showToast('改写完成！', 'success');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <h1 className=\"text-2xl font-bold text-gray-800\">\n            小说改写工具\n          </h1>\n          <div className=\"flex items-center space-x-4\">\n            {/* 任务管理按钮 */}\n            <button\n              onClick={() => setShowTaskManager(!showTaskManager)}\n              className=\"flex items-center px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\"\n            >\n              <svg className=\"mr-1\" width={18} height={18} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/>\n                <path d=\"M9 9h6v6H9z\"/>\n              </svg>\n              任务管理\n            </button>\n            {/* 开始改写按钮 */}\n            <button\n              onClick={handleStartRewrite}\n              disabled={isRewriting || !selectedNovel || !selectedChapters || !rewriteRules}\n              className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-6 rounded-lg transition-colors\"\n            >\n              {isRewriting ? '改写中...' : '开始改写'}\n            </button>\n            <Link\n              href=\"/help\"\n              className=\"flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors\"\n            >\n              <HelpCircle className=\"mr-1\" size={18} />\n              帮助\n            </Link>\n          </div>\n        </div>\n\n        {/* 任务管理器 */}\n        {showTaskManager && (\n          <div className=\"mb-6\">\n            <TaskManager\n              currentJobId={currentJobId}\n              onJobComplete={handleRewriteComplete}\n            />\n          </div>\n        )}\n\n        {/* 进度显示 */}\n        {isRewriting && currentJobId && !showTaskManager && (\n          <div className=\"mb-4\">\n            <RewriteProgress\n              jobId={currentJobId}\n              onComplete={handleRewriteComplete}\n            />\n          </div>\n        )}\n\n        {!showTaskManager && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n          {/* 左侧：改写规则 */}\n          <div className=\"lg:col-span-1\">\n            <RuleEditor\n              rules={rewriteRules}\n              onRulesChange={setRewriteRules}\n              onSaveToPreset={handleSaveToPreset}\n              disabled={isRewriting}\n            />\n          </div>\n\n          {/* 中间：小说选择和人物管理 */}\n          <div className=\"lg:col-span-1 space-y-4\">\n            <NovelSelector\n              selectedNovel={selectedNovel}\n              onNovelSelect={setSelectedNovel}\n              disabled={isRewriting}\n            />\n            <CharacterManager\n              novelId={selectedNovel?.id}\n              characters={characters}\n              onCharactersChange={setCharacters}\n              disabled={isRewriting}\n            />\n          </div>\n\n          {/* 右侧：章节选择 */}\n          <div className=\"lg:col-span-1\">\n            <ChapterSelector\n              novel={selectedNovel}\n              selectedChapters={selectedChapters}\n              onChaptersChange={setSelectedChapters}\n              disabled={isRewriting}\n            />\n          </div>\n        </div>\n        )}\n      </div>\n\n      {/* Toast 通知 */}\n      {toast.show && (\n        <Toast\n          message={toast.message}\n          type={toast.type}\n          onClose={hideToast}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAZA;;;;;;;;;;;AA+Be,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAe;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAS;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAS;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAc,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAgB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,yKAAQ,EAAC;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAa;QAAE,MAAM;QAAO,SAAS;QAAI,MAAM;IAAO;IAExF,MAAM,YAAY,CAAC,SAAiB;QAClC,SAAS;YAAE,MAAM;YAAM;YAAS;QAAK;IACvC;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;YAAO,SAAS;YAAI,MAAM;QAAO;IACpD;IAEA,MAAM,qBAAqB,OAAO;QAChC,MAAM,OAAO,OAAO;QACpB,IAAI,CAAC,MAAM;QAEX,MAAM,cAAc,OAAO,oBAAoB;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,YAAY;YACxB,OAAO;gBACL,UAAU,AAAC,SAAqB,OAAb,OAAO,KAAK,GAAI;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,UAAU,UAAU;QACtB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,cAAc;YACxD,UAAU,aAAa;YACvB;QACF;QAEA,eAAe;QAEf,IAAI;YACF,gBAAgB;YAChB,IAAI,gBAAgB;YACpB,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAA,OACnC,AAAC,GAAe,OAAb,KAAK,IAAI,EAAC,KAAe,OAAZ,KAAK,IAAI,EAAmD,OAAhD,KAAK,WAAW,GAAG,OAAO,KAAK,WAAW,GAAG,IAAG,MAC5E,IAAI,CAAC;gBACP,gBAAgB,AAAC,QAA2B,OAApB,eAAc,QAAmB,OAAb;YAC9C;YAEA,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,cAAc,EAAE;oBACzB,cAAc;oBACd,OAAO;gBACT;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,gBAAgB,OAAO,IAAI,CAAC,KAAK;gBACjC,UAAU,WAAW;YACvB,OAAO;gBACL,UAAU,AAAC,SAAqB,OAAb,OAAO,KAAK,GAAI;gBACnC,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,UAAU,UAAU;YACpB,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB;QAC5B,eAAe;QACf,gBAAgB;QAChB,UAAU,SAAS;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,SAAS,IAAM,mBAAmB,CAAC;wCACnC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAO,OAAO;gDAAI,QAAQ;gDAAI,SAAQ;gDAAY,MAAK;gDAAO,QAAO;gDAAe,aAAY;;kEAC7G,6LAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,OAAM;wDAAK,QAAO;wDAAK,IAAG;wDAAI,IAAG;;;;;;kEACnD,6LAAC;wDAAK,GAAE;;;;;;;;;;;;4CACJ;;;;;;;kDAIR,6LAAC;wCACC,SAAS;wCACT,UAAU,eAAe,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;wCACjE,WAAU;kDAET,cAAc,WAAW;;;;;;kDAE5B,6LAAC,0KAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,+OAAU;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;;;;;;oBAO9C,iCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+IAAW;4BACV,cAAc;4BACd,eAAe;;;;;;;;;;;oBAMpB,eAAe,gBAAgB,CAAC,iCAC/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mJAAe;4BACd,OAAO;4BACP,YAAY;;;;;;;;;;;oBAKjB,CAAC,iCACA,6LAAC;wBAAI,WAAU;;0CAEf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8IAAU;oCACT,OAAO;oCACP,eAAe;oCACf,gBAAgB;oCAChB,UAAU;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAa;wCACZ,eAAe;wCACf,eAAe;wCACf,UAAU;;;;;;kDAEZ,6LAAC,oJAAgB;wCACf,OAAO,EAAE,0BAAA,oCAAA,cAAe,EAAE;wCAC1B,YAAY;wCACZ,oBAAoB;wCACpB,UAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mJAAe;oCACd,OAAO;oCACP,kBAAkB;oCAClB,kBAAkB;oCAClB,UAAU;;;;;;;;;;;;;;;;;;;;;;;YAQjB,MAAM,IAAI,kBACT,6LAAC,yIAAK;gBACJ,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,SAAS;;;;;;;;;;;;AAKnB;GAlNwB;KAAA", "debugId": null}}]}